# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
dist
build

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Firebase Service Account (NEVER commit this!)
firebase-service-account.json
**/firebase-service-account.json

# Firebase Admin SDK credentials
serviceAccountKey.json
firebase-adminsdk-*.json

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# AI Models (large files)
sam_vit_h_4b8939.pth  # Large model file - download separately

# Gradio generated files
.gradio/flagged/
.gradio/tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# Python
__pycache__/
*.py[cod]
*$py.class
venv/
env/

# Firebase Emulators
.firebase/emulators/
firebase-debug.log
firestore-debug.log
ui-debug.log
