name: 📚 Deploy Documentation

on:
  push:
    branches: [main]
    paths:
      - 'docs/**'
      - 'mkdocs.yml'
      - 'requirements-docs.txt'
      - '.github/workflows/docs-deploy.yml'
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 🐍 Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: pip
          
      - name: Configure Git User
        run: |
          git config user.name github-actions[bot]
          git config user.email 41898282+github-actions[bot]@users.noreply.github.com
          
      - name: 📦 Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-docs.txt
          
      - name: 🔍 Verify Installation
        run: |
          python -c "import torch; print(f'✅ PyTorch {torch.__version__} ready')"
          python -c "import mkdocs; print(f'✅ MkDocs ready')"
          
      - name: 🏗️ Build Documentation
        run: |
          echo "📚 Building documentation..."
          mkdocs build --verbose
          echo "✅ Build completed successfully!"
          
      - name: 🚀 Deploy to GitHub Pages
        run: |
          echo "🚀 Deploying to GitHub Pages..."
          mkdocs gh-deploy --force
          echo "✅ Deployment completed!"
          
      - name: 📊 Build Summary
        run: |
          echo "🎉 Documentation deployed successfully!"
          echo "📖 Available at: https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}/"
          echo "📈 Total pages: $(find docs -name '*.md' | wc -l)" 