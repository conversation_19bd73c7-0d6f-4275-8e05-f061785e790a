name: 📚 Build and Deploy Documentation

on:
  # Trigger no push para main
  push:
    branches: [ main, master ]
    paths:
      - 'docs/**'
      - 'mkdocs.yml'
      - 'requirements-docs.txt'
      - '.github/workflows/docs.yml'
  
  # Trigger manual
  workflow_dispatch:
  
  # Trigger em Pull Requests (só build, não deploy)
  pull_request:
    branches: [ main, master ]
    paths:
      - 'docs/**'
      - 'mkdocs.yml'
      - 'requirements-docs.txt'

# Permissões necessárias para GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Evitar builds concorrentes
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # Job 1: Build da documentação
  build:
    name: 🏗️ Build Documentation
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Necessário para git info plugin
    
    - name: 🐍 Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
    
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-docs.txt
    
    - name: 🔍 Verify PyTorch Installation
      run: |
        python -c "import torch; print(f'PyTorch {torch.__version__} ready')"
        python -c "import mkdocs; print(f'MkDocs {mkdocs.__version__} ready')"
    
    - name: 🏗️ Build Documentation
      run: |
        mkdocs build --verbose --clean
    
    - name: 📄 Upload Pages Artifact
      if: github.ref == 'refs/heads/main'
      uses: actions/upload-pages-artifact@v3
      with:
        path: site/
    
    - name: ✅ Build Success
      run: echo "📚 Documentation built successfully!"

  # Job 2: Deploy para GitHub Pages (só na branch main)
  deploy:
    name: 🚀 Deploy to GitHub Pages
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    needs: build
    
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    
    steps:
    - name: 🚀 Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v4
    
    - name: ✅ Deploy Success
      run: |
        echo "🎉 Documentation deployed successfully!"
        echo "📖 Available at: ${{ steps.deployment.outputs.page_url }}"

  # Job 3: Quality Checks (sempre roda)
  quality-checks:
    name: 🔍 Quality Checks
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
    
    - name: 🐍 Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
    
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-docs.txt
    
    - name: 🔍 Check Markdown Links
      run: |
        echo "🔗 Checking for broken links in documentation..."
        # Você pode adicionar tools como markdown-link-check aqui
    
    - name: 🎨 Code Quality Checks
      run: |
        echo "🎨 Running code quality checks..."
        # Verificar se há arquivos Python nos docs
        if find docs/ -name "*.py" | grep -q .; then
          echo "Running Python code checks..."
          black --check docs/ || echo "❌ Black formatting issues found"
          flake8 docs/ || echo "❌ Flake8 issues found"
        fi
    
    - name: 📝 Documentation Lint
      run: |
        echo "📝 Linting documentation..."
        mkdocs build --strict || echo "❌ MkDocs strict mode failed" 