# =============================================================================
# STRIPE CONNECT & MARKETPLACE CONFIGURATION
# =============================================================================

# Stripe API Keys (Get from https://dashboard.stripe.com/test/apikeys)
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here

# Stripe Connect (Get from https://dashboard.stripe.com/test/connect/overview)
STRIPE_CLIENT_ID=ca_your_connect_client_id_here

# Stripe Webhooks (Create endpoint at https://dashboard.stripe.com/test/webhooks)
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# =============================================================================
# MARKETPLACE SETTINGS
# =============================================================================

# Platform Fee (percentage that marketplace takes from each sale)
STRIPE_PLATFORM_FEE_PERCENTAGE=5.0

# Currency for all transactions
MARKETPLACE_CURRENCY=usd

# Minimum and maximum sale prices (in cents)
MARKETPLACE_MIN_SALE_PRICE=100  # $1.00
MARKETPLACE_MAX_SALE_PRICE=99999  # $999.99

# Marketplace URLs for Stripe Connect redirects
MARKETPLACE_BASE_URL=http://localhost:5773
STRIPE_CONNECT_REFRESH_URL=http://localhost:5773/seller/connect/refresh
STRIPE_CONNECT_RETURN_URL=http://localhost:5773/seller/connect/return

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable marketplace functionality
MARKETPLACE_ENABLED=true

# Enable automatic transfers to sellers (vs manual)
AUTO_TRANSFER_ENABLED=true

# Enable seller verification requirements
SELLER_VERIFICATION_REQUIRED=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Use Stripe test mode (always true for development)
STRIPE_TEST_MODE=true

# Log Stripe events for debugging
STRIPE_DEBUG_LOGGING=true

# Mock Stripe in tests
STRIPE_MOCK_MODE=false

# =============================================================================
# INSTRUCTIONS
# =============================================================================

# 1. Copy these variables to your .env file
# 2. Replace the placeholder values with your actual Stripe keys
# 3. Set up Stripe Connect in your Stripe dashboard
# 4. Configure webhook endpoints for production
# 5. Adjust platform fee and price limits as needed

# IMPORTANT NOTES:
# - Never commit real API keys to version control
# - Use test keys for development
# - Set up proper webhook endpoints for production
# - Consider using environment-specific configuration files 