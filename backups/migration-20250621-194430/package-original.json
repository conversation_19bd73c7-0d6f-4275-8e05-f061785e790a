{"name": "pinpal-project", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite --port 5773 --force", "dev:debug": "vite --debug --port 5773 --force", "dev:host": "vite --host --port 5773 --force", "dev:monitor": "node scripts/dev-monitor.js", "dev:watch": "nodemon", "dev:simple": "node scripts/simple-monitor.js", "dev:prod": "VITE_USE_FIREBASE_PRODUCTION=true vite --port 5773 --force", "dev:smart": "node scripts/dev-server.cjs", "db:create": "node scripts/createDatabase.mjs", "build": "tsc && vite build", "build:watch": "tsc --watch", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,scss,css,json}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx}\"", "audit:design": "./scripts/audit-design-system.sh", "audit:design:verbose": "./scripts/audit-design-system.sh --verbose", "test": "vitest --workspace vitest.workspace.ts", "test:run": "vitest run --workspace vitest.workspace.ts", "test:watch": "vitest --watch --workspace vitest.workspace.ts", "test:coverage": "vitest run --coverage --workspace vitest.workspace.ts", "test:ci": "vitest run --ci --coverage --workspace vitest.workspace.ts", "test:ui": "vitest --ui --workspace vitest.workspace.ts", "test:explore": "./scripts/run-explore-tests.sh", "test:e2e": "cypress run", "test:e2e:dev": "cypress open", "test:signup": "node scripts/test-signup-browser.cjs", "test:signup:open": "node scripts/test-signup-browser.cjs --open", "docs:dev": "mkdocs serve", "docs:build": "mkdocs build", "docs:api": "typedoc", "docs:all": "npm run docs:api && npm run docs:build", "docs:coverage-ts": "type-coverage --detail", "docs:coverage-ts-report": "typescript-coverage-report", "docs:coverage-py": "cd docs-env && source bin/activate && docstr-coverage src/", "docs:coverage-py-report": "cd docs-env && source bin/activate && interrogate -v src/", "docs:coverage-all": "npm run docs:coverage-ts-report && npm run docs:coverage-py-report", "docs:coverage-web": "./scripts/serve-coverage-reports.sh", "docs:hub": "./scripts/serve-integrated-docs.sh", "test:hub": "./scripts/start-hub-working.sh", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "validate": "npm run lint && npm run type-check && npm run test:run", "prepare": "husky install", "storybook": "storybook dev -p 6006", "storybook:watch": "storybook dev -p 6006 --no-open --quiet", "storybook:turbo": "storybook dev -p 6006 --no-manager-cache --no-version-updates", "storybook:dev": "concurrently \"npm run storybook:watch\" \"npm run storybook:auto-generate\"", "build-storybook": "storybook build", "build-storybook:analyze": "storybook build --webpack-stats-json", "generate-story": "node scripts/generate-story.cjs", "generate-component": "node scripts/generate-component.cjs", "storybook:auto-generate": "node scripts/auto-generate-stories.cjs --watch", "storybook:generate-all": "node scripts/generate-all-stories.cjs", "storybook:cleanup": "node scripts/cleanup-invalid-stories.cjs", "storybook:check": "node scripts/cleanup-invalid-stories.cjs --check", "analyze": "vite build --mode analyze", "analyze-bundle": "tsx scripts/analyze-bundle.ts", "cypress:open": "cypress open", "cypress:run": "cypress run", "test:all": "npm run test:run && npm run test:e2e", "server:heic": "node server.js", "server:all": "npm run server:heic", "migrate:avatars": "node scripts/migrate-avatars.cjs", "server": "npm run server:all", "server:dev": "nodemon server.js", "start": "concurrently \"npm run server:all\" \"npm run dev\"", "kill-ports": "kill -9 $(lsof -ti:5773,5774,3001) 2>/dev/null || true", "check:all": "npm run type-check && npm run lint", "monitor:console": "node scripts/monitor-console.js", "monitor:chrome": "node scripts/monitor-console-chrome.js", "monitor:trading-map": "node scripts/monitor-console.js http://localhost:5773 --trading-map", "monitor:chrome-trading": "node scripts/monitor-console-chrome.js http://localhost:5773 --trading-map", "monitor:existing": "node scripts/monitor-existing-chrome.js", "monitor:existing-start": "node scripts/monitor-existing-chrome.js --start-chrome", "monitor:simple": "node scripts/simple-console-monitor.js", "check:console": "node scripts/quick-console-check.js", "check:trading-map": "node scripts/quick-console-check.js http://localhost:5773 --trading-map", "migrate:prepare": "ts-node scripts/prepare-migration.ts", "migrate:users": "ts-node scripts/migrate-users.ts", "migrate:pins": "ts-node scripts/migrate-pins.ts", "migrate:boards": "ts-node scripts/migrate-boards.ts", "migrate:trades": "ts-node scripts/migrate-trades.ts", "migrate:messages": "ts-node scripts/migrate-messages.ts", "migrate:full": "ts-node scripts/migrate-full.ts", "migrate:rollback": "ts-node scripts/rollback-migration.ts", "migrate:validate": "ts-node scripts/validate-migration.ts", "dataconnect:generate": "firebase dataconnect:sdk:generate", "dataconnect:deploy": "firebase deploy --only dataconnect", "dataconnect:emulator": "firebase emulators:start --only dataconnect", "start:all": "./scripts/start-all-services.sh", "start:quick": "./scripts/dev-quick.sh", "test:all-comprehensive": "./scripts/test-all.sh", "services:start": "./scripts/start-all-services.sh", "services:frontend-only": "./scripts/start-all-services.sh --frontend-only", "services:no-firebase": "./scripts/start-all-services.sh --no-firebase", "services:cleanup": "./scripts/start-all-services.sh --cleanup", "services:check": "./scripts/start-all-services.sh --check", "migrate:default-boards:dry-run": "node scripts/dry-run-default-boards.js", "migrate:default-boards:run": "node scripts/create-default-boards-for-existing-users.js", "migrate:default-boards:help": "cat scripts/README-default-boards-migration.md", "cleanup:dry-run": "node scripts/cleanup-expired-trash.js --dry-run", "cleanup:run": "node scripts/cleanup-expired-trash.js", "cleanup:setup": "./scripts/setup-cron-cleanup.sh", "cleanup:monitor": "./scripts/monitor-cleanup.sh"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@firebase/data-connect": "^0.1.0", "@firebasegen/default-connector": "file:dataconnect-generated/js/default-connector", "@floating-ui/react": "^0.27.8", "@googlemaps/adv-markers-utils": "^1.2.4", "@headlessui/react": "^1.7.19", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@imgly/background-removal": "^1.6.0", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@radix-ui/react-popover": "^1.1.14", "@react-google-maps/api": "^2.20.6", "@sentry/react": "^9.16.1", "@tanstack/react-query": "^5.80.7", "@tensorflow-models/body-pix": "^2.2.1", "@types/google.maps": "^3.58.1", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "browser-image-compression": "^2.0.2", "clsx": "^2.1.1", "concurrently": "^8.2.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "emoji-mart": "^5.6.0", "esbuild": "^0.25.5", "express": "^4.21.2", "firebase": "^11.7.0", "firebase-admin": "^12.0.0", "framer-motion": "^12.12.2", "google-images": "^2.1.0", "graphql": "^16.8.1", "heic-convert": "^2.1.0", "helmet": "^7.1.0", "i18next": "^25.1.1", "i18next-browser-languagedetector": "^8.1.0", "lucide-react": "^0.511.0", "luxon": "^3.6.1", "morgan": "^1.10.0", "multer": "^2.0.1", "node-fetch": "^3.3.2", "onnxruntime-web": "^1.21.0-dev.20250206-d981b153d3", "pg": "^8.11.0", "photoeditorsdk": "^5.19.7", "posthog-js": "^1.240.3", "pragmatic-drag-and-drop": "^0.0.1-security", "query-string": "^9.2.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.1", "react-router-dom": "^6.22.3", "semantic-ui-react": "^2.1.5", "styled-components": "^6.1.18", "svelte": "^4.2.19", "svelte-i18n": "^4.0.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "web-push": "^3.6.6", "web-vitals": "^5.0.3", "zod": "^3.25.23", "zustand": "^5.0.4"}, "devDependencies": {"@amplicode/amplicode-mui-storybook": "^0.9.9", "@chromatic-com/storybook": "^3.2.6", "@storybook/addon-a11y": "^8.6.12", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-onboarding": "^8.6.12", "@storybook/addon-storysource": "^8.6.14", "@storybook/blocks": "^8.6.12", "@storybook/experimental-addon-test": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test": "^8.6.12", "@tailwindcss/postcss": "^4.1.10", "@tanstack/react-query-devtools": "^5.76.2", "@testing-library/cypress": "^10.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.17", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "@vitest/browser": "^3.1.3", "@vitest/coverage-v8": "^3.1.3", "chokidar": "^4.0.3", "cypress": "^14.3.3", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-storybook": "^0.12.0", "http-server": "^14.1.1", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0", "lint-staged": "^15.5.2", "nodemon": "^3.1.10", "playwright": "^1.53.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "puppeteer": "^24.10.0", "rollup-plugin-visualizer": "^6.0.3", "storybook": "^8.6.14", "tailwindcss": "^4.1.10", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsx": "^4.19.4", "type-coverage": "^2.29.7", "typedoc": "^0.28.4", "typescript": "^5.2.2", "typescript-coverage-report": "^1.1.1", "vite": "^5.1.6", "vite-plugin-compression": "^0.5.1", "vitest": "^3.1.3", "ws": "^8.18.2"}, "description": "A modern web application for Disney pin collectors and traders. Built with React, TypeScript, and TailwindCSS.", "main": ".eslintrc.js", "keywords": [], "author": "", "license": "ISC"}