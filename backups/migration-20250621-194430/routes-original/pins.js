import express from 'express';
import { getPool } from '../config/database.js';

const router = express.Router();

// Get all pins (for explore/discovery)
router.get('/', async (req, res) => {
  try {
    const { 
      limit = 30, 
      offset = 0, 
      sortBy = 'newest',
      userId,
      series,
      region,
      startDate
    } = req.query;
    
    const pool = await getPool();
    
    let query = `
      SELECT 
        p.id,
        p.title,
        p.image_url,
        p.description,
        p.origin,
        p.release_year,
        p.original_price,
        p.pin_number,
        p.tradable,
        p.is_public,
        p.likes_count,
        p.views_count,
        p.trades_count,
        p.user_id,
        p.created_at,
        p.updated_at,
        COALESCE(comment_count.count, 0) as comments_count,
        u.first_name,
        u.last_name,
        u.username,
        u.avatar_url
      FROM pin p
      LEFT JOIN "user" u ON p.user_id = u.id
      LEFT JOIN (
        SELECT pin_id, COUNT(*) as count 
        FROM pin_comments 
        GROUP BY pin_id
      ) comment_count ON p.id = comment_count.pin_id
      WHERE p.is_public = true
    `;
    
    const params = [];
    let paramIndex = 1;
    
    // Add filters
    if (series) {
      query += ` AND p.origin = $${paramIndex}`;
      params.push(series);
      paramIndex++;
    }
    
    if (startDate) {
      query += ` AND p.created_at >= $${paramIndex}`;
      params.push(startDate);
      paramIndex++;
    }
    
    // Add sorting
    switch (sortBy) {
      case 'trending':
        query += ` ORDER BY (p.likes_count + COALESCE(comment_count.count, 0) * 3) DESC, p.created_at DESC`;
        break;
      case 'newest':
        query += ` ORDER BY p.created_at DESC`;
        break;
      case 'popular':
        query += ` ORDER BY p.likes_count DESC, p.created_at DESC`;
        break;
      default:
        query += ` ORDER BY p.created_at DESC`;
    }
    
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, offset);
    
    const result = await pool.query(query, params);
    
    // Map to camelCase format
    const pins = result.rows.map(row => ({
      id: row.id,
      name: row.title || 'Untitled Pin',
      title: row.title || 'Untitled Pin',
      image: row.image_url || '',
      imageUrl: row.image_url || '',
      description: row.description || '',
      origin: row.origin,
      series: row.origin,
      releaseYear: row.release_year,
      year: row.release_year,
      originalPrice: row.original_price,
      pinNumber: row.pin_number,
      tradable: row.tradable || false,
      isPublic: row.is_public,
      likes: parseInt(row.likes_count) || 0,
      likesCount: parseInt(row.likes_count) || 0,
      viewsCount: parseInt(row.views_count) || 0,
      tradesCount: parseInt(row.trades_count) || 0,
      comments: parseInt(row.comments_count) || 0,
      commentsCount: parseInt(row.comments_count) || 0,
      isLiked: false, // TODO: Check if user liked this pin
      isSaved: false, // TODO: Check if user saved this pin
      userId: row.user_id,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      owner: {
        id: row.user_id,
        name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'Unknown',
        username: row.username,
        avatar: row.avatar_url
      },
      user: {
        id: row.user_id,
        displayName: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'Unknown',
        username: row.username,
        avatarUrl: row.avatar_url
      }
    }));
    
    console.log(`✅ Found ${pins.length} pins (sortBy: ${sortBy})`);
    res.json(pins);
    
  } catch (error) {
    console.error('Error getting pins:', error);
    res.status(500).json({ error: 'Failed to get pins' });
  }
});

// Get category statistics
router.get('/categories/stats', async (req, res) => {
  try {
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        origin as name,
        COUNT(*) as count,
        ROUND(
          (COUNT(*) * 100.0 / (SELECT COUNT(*) FROM pin WHERE is_public = true)), 
          1
        ) as growth,
        CASE WHEN COUNT(*) > 10 THEN true ELSE false END as is_popular
      FROM pin 
      WHERE is_public = true AND origin IS NOT NULL
      GROUP BY origin
      ORDER BY count DESC
      LIMIT 20
    `);
    
    const stats = result.rows.map(row => ({
      name: row.name,
      count: parseInt(row.count),
      growth: parseFloat(row.growth) || 0,
      isPopular: row.is_popular
    }));
    
    console.log(`✅ Found ${stats.length} category stats`);
    res.json(stats);
    
  } catch (error) {
    console.error('Error getting category stats:', error);
    res.status(500).json({ error: 'Failed to get category stats' });
  }
});

// Get all user pins (including those in boards)
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 50 } = req.query;
    
    const pool = await getPool();
    
    const query = `
      SELECT 
        p.id,
        p.title,
        p.image_url as imageUrl,
        p.description,
        p.origin,
        p.release_year as releaseYear,
        p.original_price as originalPrice,
        p.pin_number as pinNumber,
        p.tradable,
        p.is_public as isPublic,
        p.likes_count as likesCount,
        p.views_count as viewsCount,
        p.trades_count as tradesCount,
        p.user_id as userId,
        p.created_at as createdAt,
        p.updated_at as updatedAt,
        COALESCE(comment_count.count, 0) as commentsCount
      FROM pin p
      LEFT JOIN (
        SELECT pin_id, COUNT(*) as count 
        FROM pin_comments 
        GROUP BY pin_id
      ) comment_count ON p.id = comment_count.pin_id
      WHERE p.user_id = $1 
      ORDER BY p.created_at DESC
      LIMIT $2
    `;
    
    const result = await pool.query(query, [userId, limit]);
    
    const pins = result.rows.map(row => ({
      id: row.id,
      title: row.title || 'Untitled Pin',
      imageUrl: row.imageurl || '',
      description: row.description || '',
      origin: row.origin,
      releaseYear: row.releaseyear,
      originalPrice: row.originalprice,
      pinNumber: row.pinnumber,
      tradable: row.tradable || false,
      isPublic: row.ispublic,
      likesCount: row.likescount || 0,
      viewsCount: row.viewscount || 0,
      tradesCount: row.tradescount || 0,
      commentsCount: parseInt(row.commentscount) || 0,
      userId: row.userid,
      createdAt: row.createdat,
      updatedAt: row.updatedat,
      user: {
        id: row.userid,
        displayName: 'User',
        username: null,
        avatarUrl: null
      }
    }));
    
    console.log(`✅ Found ${pins.length} total pins for user ${userId}`);
    res.status(200).json(pins);
    
  } catch (error) {
    console.error('❌ Error fetching user pins:', error);
    res.status(500).json({ 
      error: 'Failed to fetch user pins',
      details: error.message 
    });
  }
});

// Get pins for a board
router.get('/board/:boardId', async (req, res) => {
  try {
    const { boardId } = req.params;
    const { page = 1, limit = 30 } = req.query;
    
    const offset = (page - 1) * limit;
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        p.*,
        bp.sort_order,
        bp.added_at,
        (SELECT COUNT(*) FROM pin_likes pl WHERE pl.pin_id = p.id) as likes_count,
        (SELECT COUNT(*) FROM pin_comments pc WHERE pc.pin_id = p.id) as comments_count
      FROM pin p
      INNER JOIN board_pin bp ON p.id = bp.pin_id
      WHERE bp.board_id = $1
      ORDER BY bp.sort_order ASC, bp.added_at DESC
      LIMIT $2 OFFSET $3
    `, [boardId, limit, offset]);
    
    // CORREÇÃO: Mapear dados para camelCase para compatibilidade com frontend
    const pins = result.rows.map(row => ({
      id: row.id,
      name: row.title || 'Untitled Pin',
      image: row.image_url || '',  // CORREÇÃO: image_url → image
      description: row.description || '',
      origin: row.origin,
      releaseYear: row.release_year,
      originalPrice: row.original_price,
      pinNumber: row.pin_number,
      tradable: row.tradable || false,
      category: 'other',
      rarity: 'common',
      condition: 'excellent',
      year: row.release_year,
      series: row.origin,
      tags: [],
      isForTrade: row.tradable || false,
      likes: parseInt(row.likes_count) || 0,
      comments: parseInt(row.comments_count) || 0,
      isLiked: false, // TODO: Implementar verificação de like do usuário
      isSaved: false, // TODO: Implementar verificação de save do usuário
      userId: row.user_id,
      sortOrder: row.sort_order,
      addedAt: row.added_at,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      owner: {
        id: row.user_id,
        name: 'User', // TODO: Buscar dados reais do usuário
        avatar: null
      }
    }));
    
    console.log(`✅ Found ${pins.length} pins for board ${boardId}`);
    res.json(pins);
    
  } catch (error) {
    console.error('Error getting board pins:', error);
    res.status(500).json({ error: 'Failed to get pins' });
  }
});

// Get user's pins without a board
router.get('/user/:userId/without-board', async (req, res) => {
  try {
    const { userId } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        p.*,
        (SELECT COUNT(*) FROM pin_likes pl WHERE pl.pin_id = p.id) as likes_count,
        (SELECT COUNT(*) FROM pin_comments pc WHERE pc.pin_id = p.id) as comments_count
      FROM pin p
      WHERE p.user_id = $1
      AND p.id NOT IN (SELECT pin_id FROM board_pin)
      ORDER BY p.created_at DESC
    `, [userId]);
    
    // CORREÇÃO: Mapear dados para camelCase para compatibilidade com frontend
    const pins = result.rows.map(row => ({
      id: row.id,
      name: row.title || 'Untitled Pin',
      image: row.image_url || '',  // CORREÇÃO: image_url → image
      description: row.description || '',
      origin: row.origin,
      releaseYear: row.release_year,
      originalPrice: row.original_price,
      pinNumber: row.pin_number,
      tradable: row.tradable || false,
      category: 'other',
      rarity: 'common',
      condition: 'excellent',
      year: row.release_year,
      series: row.origin,
      tags: [],
      isForTrade: row.tradable || false,
      likes: parseInt(row.likes_count) || 0,
      comments: parseInt(row.comments_count) || 0,
      isLiked: false, // TODO: Implementar verificação de like do usuário
      isSaved: false, // TODO: Implementar verificação de save do usuário
      userId: row.user_id,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      owner: {
        id: row.user_id,
        name: 'User', // TODO: Buscar dados reais do usuário
        avatar: null
      }
    }));
    
    console.log(`✅ Found ${pins.length} pins without board for user ${userId}`);
    res.json(pins);
    
  } catch (error) {
    console.error('Error getting user pins without board:', error);
    res.status(500).json({ error: 'Failed to get pins' });
  }
});

// Add pin to board
router.put('/:pinId/board', async (req, res) => {
  try {
    const { pinId } = req.params;
    const { boardId } = req.body;
    
    if (!boardId) {
      return res.status(400).json({ error: 'Board ID is required' });
    }
    
    const pool = await getPool();
    
    // Get next sort order for this board
    const sortResult = await pool.query(
      'SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order FROM board_pin WHERE board_id = $1',
      [boardId]
    );
    const sortOrder = sortResult.rows[0].next_order;
    
    // Add pin to board
    await pool.query(`
      INSERT INTO board_pin (board_id, pin_id, sort_order, added_at)
      VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
      ON CONFLICT (board_id, pin_id) DO NOTHING
    `, [boardId, pinId, sortOrder]);
    
    // Update board's pin count
    await pool.query(`
      UPDATE board 
      SET pins_count = (SELECT COUNT(*) FROM board_pin WHERE board_id = $1),
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [boardId]);
    
    res.json({ success: true, message: 'Pin added to board successfully' });
    
  } catch (error) {
    console.error('Error adding pin to board:', error);
    res.status(500).json({ error: 'Failed to add pin to board' });
  }
});

// Remove pin from board
router.delete('/:pinId/board', async (req, res) => {
  try {
    const { pinId } = req.params;
    const { boardId } = req.body;
    
    if (!boardId) {
      return res.status(400).json({ error: 'Board ID is required' });
    }
    
    const pool = await getPool();
    
    // Remove pin from board
    const result = await pool.query(
      'DELETE FROM board_pin WHERE board_id = $1 AND pin_id = $2',
      [boardId, pinId]
    );
    
    if (result.rowCount === 0) {
      return res.status(404).json({ error: 'Pin not found in board' });
    }
    
    // Update board's pin count
    await pool.query(`
      UPDATE board 
      SET pins_count = (SELECT COUNT(*) FROM board_pin WHERE board_id = $1),
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [boardId]);
    
    res.json({ success: true, message: 'Pin removed from board successfully' });
    
  } catch (error) {
    console.error('Error removing pin from board:', error);
    res.status(500).json({ error: 'Failed to remove pin from board' });
  }
});

// Update pin order in board
router.put('/board/:boardId/order', async (req, res) => {
  try {
    const { boardId } = req.params;
    const { pinIds } = req.body;
    
    if (!Array.isArray(pinIds)) {
      return res.status(400).json({ error: 'pinIds must be an array' });
    }
    
    const pool = await getPool();
    
    // Update sort order for each pin
    for (let i = 0; i < pinIds.length; i++) {
      await pool.query(
        'UPDATE board_pin SET sort_order = $1 WHERE board_id = $2 AND pin_id = $3',
        [i + 1, boardId, pinIds[i]]
      );
    }
    
    res.json({ success: true, message: 'Pin order updated successfully' });
    
  } catch (error) {
    console.error('Error updating pin order:', error);
    res.status(500).json({ error: 'Failed to update pin order' });
  }
});

// Like a pin
router.post('/:pinId/like', async (req, res) => {
  try {
    const { pinId } = req.params;
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    const pool = await getPool();
    
    // Check if already liked
    const existingLike = await pool.query(
      'SELECT id FROM pin_likes WHERE pin_id = $1 AND user_id = $2',
      [pinId, userId]
    );
    
    if (existingLike.rows.length > 0) {
      // Unlike
      await pool.query(
        'DELETE FROM pin_likes WHERE pin_id = $1 AND user_id = $2',
        [pinId, userId]
      );
      
      // Get updated count
      const countResult = await pool.query(
        'SELECT COUNT(*) as count FROM pin_likes WHERE pin_id = $1',
        [pinId]
      );
      
      res.json({
        liked: false,
        likesCount: parseInt(countResult.rows[0].count),
        message: 'Pin unliked successfully'
      });
    } else {
      // Like
      await pool.query(
        'INSERT INTO pin_likes (pin_id, user_id, created_at) VALUES ($1, $2, CURRENT_TIMESTAMP)',
        [pinId, userId]
      );
      
      // Get updated count
      const countResult = await pool.query(
        'SELECT COUNT(*) as count FROM pin_likes WHERE pin_id = $1',
        [pinId]
      );
      
      res.json({
        liked: true,
        likesCount: parseInt(countResult.rows[0].count),
        message: 'Pin liked successfully'
      });
    }
    
  } catch (error) {
    console.error('Error toggling pin like:', error);
    res.status(500).json({ error: 'Failed to toggle like' });
  }
});

// Get pin likes
router.get('/:pinId/likes', async (req, res) => {
  try {
    const { pinId } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        pl.created_at,
        u.id, u.first_name, u.last_name, u.username, u.avatar_url
      FROM pin_likes pl
      JOIN "user" u ON pl.user_id = u.id
      WHERE pl.pin_id = $1
      ORDER BY pl.created_at DESC
    `, [pinId]);
    
    res.json(result.rows);
    
  } catch (error) {
    console.error('Error getting pin likes:', error);
    res.status(500).json({ error: 'Failed to get likes' });
  }
});

// Check if user liked pin
router.get('/:pinId/likes/check/:userId', async (req, res) => {
  try {
    const { pinId, userId } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(
      'SELECT id FROM pin_likes WHERE pin_id = $1 AND user_id = $2',
      [pinId, userId]
    );
    
    res.json({ liked: result.rows.length > 0 });
    
  } catch (error) {
    console.error('Error checking pin like:', error);
    res.status(500).json({ error: 'Failed to check like status' });
  }
});

// Save/unsave a pin
router.post('/:pinId/save', async (req, res) => {
  try {
    const { pinId } = req.params;
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    const pool = await getPool();
    
    // Check if already saved
    const existingSave = await pool.query(
      'SELECT id FROM saved_pins WHERE pin_id = $1 AND user_id = $2',
      [pinId, userId]
    );
    
    if (existingSave.rows.length > 0) {
      // Unsave
      await pool.query(
        'DELETE FROM saved_pins WHERE pin_id = $1 AND user_id = $2',
        [pinId, userId]
      );
      
      res.json({
        saved: false,
        message: 'Pin removed from saved pins'
      });
    } else {
      // Save
      await pool.query(
        'INSERT INTO saved_pins (pin_id, user_id, created_at) VALUES ($1, $2, CURRENT_TIMESTAMP)',
        [pinId, userId]
      );
      
      res.json({
        saved: true,
        message: 'Pin saved successfully'
      });
    }
    
  } catch (error) {
    console.error('Error toggling pin save:', error);
    res.status(500).json({ error: 'Failed to toggle save' });
  }
});

// Get user's saved pins
router.get('/user/:userId/saved', async (req, res) => {
  try {
    const { userId } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        p.*,
        sp.created_at as saved_at,
        (SELECT COUNT(*) FROM pin_likes pl WHERE pl.pin_id = p.id) as likes_count,
        (SELECT COUNT(*) FROM pin_comments pc WHERE pc.pin_id = p.id) as comments_count
      FROM saved_pins sp
      JOIN pin p ON sp.pin_id = p.id
      WHERE sp.user_id = $1
      ORDER BY sp.created_at DESC
    `, [userId]);
    
    // CORREÇÃO: Mapear dados para camelCase para compatibilidade com frontend
    const pins = result.rows.map(row => ({
      id: row.id,
      name: row.title || 'Untitled Pin',
      image: row.image_url || '',  // CORREÇÃO: image_url → image
      description: row.description || '',
      origin: row.origin,
      releaseYear: row.release_year,
      originalPrice: row.original_price,
      pinNumber: row.pin_number,
      tradable: row.tradable || false,
      category: 'other',
      rarity: 'common',
      condition: 'excellent',
      year: row.release_year,
      series: row.origin,
      tags: [],
      isForTrade: row.tradable || false,
      likes: parseInt(row.likes_count) || 0,
      comments: parseInt(row.comments_count) || 0,
      isLiked: false, // TODO: Implementar verificação de like do usuário
      isSaved: true, // Todos os pins desta lista estão salvos
      userId: row.user_id,
      savedAt: row.saved_at,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      owner: {
        id: row.user_id,
        name: 'User', // TODO: Buscar dados reais do usuário
        avatar: null
      }
    }));
    
    console.log(`✅ Found ${pins.length} saved pins for user ${userId}`);
    res.json(pins);
    
  } catch (error) {
    console.error('Error getting saved pins:', error);
    res.status(500).json({ error: 'Failed to get saved pins' });
  }
});

// Add comment to pin
router.post('/:pinId/comments', async (req, res) => {
  try {
    const { pinId } = req.params;
    const { userId, content, parentCommentId = null } = req.body; // CORREÇÃO: parentCommentId em vez de parentId
    
    if (!userId || !content) {
      return res.status(400).json({ error: 'User ID and content are required' });
    }
    
    const pool = await getPool();
    
    const result = await pool.query(`
      INSERT INTO pin_comments (pin_id, user_id, content, parent_id, created_at, updated_at)
      VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [pinId, userId, content, parentCommentId]);
    
    // Get comment with user info
    const commentResult = await pool.query(`
      SELECT 
        c.*,
        u.first_name, u.last_name, u.username, u.avatar_url
      FROM pin_comments c
      JOIN "user" u ON c.user_id = u.id
      WHERE c.id = $1
    `, [result.rows[0].id]);
    
    // CORREÇÃO: Mapear dados para camelCase para compatibilidade com frontend
    const row = commentResult.rows[0];
    const comment = {
      id: row.id,
      pinId: row.pin_id,
      userId: row.user_id,
      content: row.content,
      parentCommentId: row.parent_id,
      isEdited: row.is_edited || false,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      likesCount: 0, // New comment has 0 likes
      user: {
        id: row.user_id,
        name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'User',
        firstName: row.first_name,
        lastName: row.last_name,
        username: row.username,
        avatar: row.avatar_url
      }
    };
    
    console.log(`✅ Comment created with ID ${comment.id} for pin ${pinId}`);
    res.status(201).json(comment);
    
  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(500).json({ error: 'Failed to add comment' });
  }
});

// Get pin comments
router.get('/:pinId/comments', async (req, res) => {
  try {
    const { pinId } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        c.*,
        u.first_name, u.last_name, u.username, u.avatar_url,
        (SELECT COUNT(*) FROM comment_likes cl WHERE cl.comment_id = c.id) as likes_count
      FROM pin_comments c
      JOIN "user" u ON c.user_id = u.id
      WHERE c.pin_id = $1
      ORDER BY c.created_at ASC
    `, [pinId]);
    
    // CORREÇÃO: Mapear dados para camelCase para compatibilidade com frontend
    const comments = result.rows.map(row => ({
      id: row.id,
      pinId: row.pin_id,
      userId: row.user_id,
      content: row.content,
      parentCommentId: row.parent_id,
      isEdited: row.is_edited || false,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      likesCount: parseInt(row.likes_count) || 0,
      user: {
        id: row.user_id,
        name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'User',
        firstName: row.first_name,
        lastName: row.last_name,
        username: row.username,
        avatar: row.avatar_url
      }
    }));
    
    console.log(`✅ Found ${comments.length} comments for pin ${pinId}`);
    res.json(comments);
    
  } catch (error) {
    console.error('Error getting comments:', error);
    res.status(500).json({ error: 'Failed to get comments' });
  }
});

// Get social proof for a pin
router.get('/:pinId/social-proof', async (req, res) => {
  try {
    const { pinId } = req.params;
    const { userId } = req.query;
    
    const pool = await getPool();
    
    // Get mutual friends who liked this pin
    const mutualLikesResult = await pool.query(`
      SELECT 
        u.id,
        u.first_name,
        u.last_name,
        u.username,
        u.avatar_url
      FROM pin_likes pl
      JOIN "user" u ON pl.user_id = u.id
      WHERE pl.pin_id = $1
      AND pl.user_id IN (
        SELECT followed_id FROM user_follow WHERE follower_id = $2
      )
      LIMIT 5
    `, [pinId, userId]);
    
    // Get total likes and comments
    const statsResult = await pool.query(`
      SELECT 
        p.likes_count,
        COALESCE(comment_count.count, 0) as comments_count
      FROM pin p
      LEFT JOIN (
        SELECT pin_id, COUNT(*) as count 
        FROM pin_comments 
        GROUP BY pin_id
      ) comment_count ON p.id = comment_count.pin_id
      WHERE p.id = $1
    `, [pinId]);
    
    const stats = statsResult.rows[0] || { likes_count: 0, comments_count: 0 };
    
    const socialProof = {
      mutualLikes: mutualLikesResult.rows.map(row => ({
        id: row.id,
        name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'Unknown',
        username: row.username,
        avatar: row.avatar_url
      })),
      totalLikes: parseInt(stats.likes_count) || 0,
      totalComments: parseInt(stats.comments_count) || 0,
      engagement: (parseInt(stats.likes_count) || 0) + (parseInt(stats.comments_count) || 0)
    };
    
    console.log(`✅ Generated social proof for pin ${pinId}`);
    res.json(socialProof);
    
  } catch (error) {
    console.error('Error getting social proof:', error);
    res.status(500).json({ error: 'Failed to get social proof' });
  }
});

export default router; 