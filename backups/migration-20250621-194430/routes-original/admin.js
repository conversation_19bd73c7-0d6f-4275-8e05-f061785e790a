import express from 'express';
import { getPool } from '../config/database.js';

const router = express.Router();

// Middleware to check admin privileges
const requireAdmin = async (req, res, next) => {
  try {
    const { adminUserId } = req.body.adminUserId ? req.body : req.query;
    
    if (!adminUserId) {
      return res.status(400).json({ error: 'Admin user ID is required' });
    }

    const pool = await getPool();
    
    const adminCheck = await pool.query(
      'SELECT * FROM "user" WHERE uid = $1 AND role = $2',
      [adminUserId, 'admin']
    );

    if (adminCheck.rows.length === 0) {
      return res.status(403).json({ error: 'Admin privileges required' });
    }

    req.adminUser = adminCheck.rows[0];
    next();
  } catch (error) {
    console.error('Error checking admin privileges:', error);
    res.status(500).json({ error: 'Failed to verify admin privileges' });
  }
};

// GET /api/admin/dashboard
router.get('/dashboard', requireAdmin, async (req, res) => {
  try {
    const pool = await getPool();
    
    // Get overview statistics
    const statsQueries = await Promise.all([
      pool.query('SELECT COUNT(*) as total FROM "user"'),
      pool.query('SELECT COUNT(*) as total FROM pin WHERE is_deleted = false'),
      pool.query('SELECT COUNT(*) as total FROM board'),
      pool.query('SELECT COUNT(*) as total FROM message'),
      pool.query('SELECT COUNT(*) as total FROM trading_point WHERE is_active = true'),
      pool.query('SELECT COUNT(*) as total FROM pin_reports WHERE status = $1', ['pending']),
      pool.query(`
        SELECT COUNT(*) as total FROM "user" 
        WHERE created_at >= NOW() - INTERVAL '30 days'
      `),
      pool.query(`
        SELECT COUNT(*) as total FROM pin 
        WHERE created_at >= NOW() - INTERVAL '30 days' AND is_deleted = false
      `)
    ]);

    const [
      totalUsers, totalPins, totalBoards, totalMessages, 
      totalTradingPoints, pendingReports, newUsersMonth, newPinsMonth
    ] = statsQueries;

    // Get recent activity
    const recentActivity = await pool.query(`
      SELECT 
        'user_created' as type,
        u.uid as entity_id,
        u.first_name || ' ' || u.last_name as entity_name,
        u.created_at as timestamp
      FROM "user" u
      WHERE u.created_at >= NOW() - INTERVAL '7 days'
      
      UNION ALL
      
      SELECT 
        'pin_created' as type,
        p.id as entity_id,
        p.name as entity_name,
        p.created_at as timestamp
      FROM pin p
      WHERE p.created_at >= NOW() - INTERVAL '7 days' AND p.is_deleted = false
      
      UNION ALL
      
      SELECT 
        'report_created' as type,
        pr.id as entity_id,
        'Pin Report #' || pr.id as entity_name,
        pr.created_at as timestamp
      FROM pin_reports pr
      WHERE pr.created_at >= NOW() - INTERVAL '7 days'
      
      ORDER BY timestamp DESC
      LIMIT 20
    `);

    res.json({
      statistics: {
        totalUsers: parseInt(totalUsers.rows[0].total),
        totalPins: parseInt(totalPins.rows[0].total),
        totalBoards: parseInt(totalBoards.rows[0].total),
        totalMessages: parseInt(totalMessages.rows[0].total),
        totalTradingPoints: parseInt(totalTradingPoints.rows[0].total),
        pendingReports: parseInt(pendingReports.rows[0].total),
        newUsersThisMonth: parseInt(newUsersMonth.rows[0].total),
        newPinsThisMonth: parseInt(newPinsMonth.rows[0].total)
      },
      recentActivity: recentActivity.rows
    });

  } catch (error) {
    console.error('Error getting admin dashboard:', error);
    res.status(500).json({ error: 'Failed to get dashboard data' });
  }
});

// GET /api/admin/users
router.get('/users', requireAdmin, async (req, res) => {
  try {
    const { 
      limit = 50, 
      offset = 0, 
      search = null, 
      role = null,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const pool = await getPool();
    
    let query = `
      SELECT 
        u.*,
        COUNT(DISTINCT p.id) as pins_count,
        COUNT(DISTINCT b.id) as boards_count,
        COUNT(DISTINCT pr.id) as reports_count
      FROM "user" u
      LEFT JOIN pin p ON u.uid = p.user_id AND p.is_deleted = false
      LEFT JOIN board b ON u.uid = b.user_id
      LEFT JOIN pin_reports pr ON u.uid = pr.reported_by
      WHERE 1=1
    `;
    
    const params = [];
    let paramIndex = 1;

    if (search) {
      query += ` AND (
        u.first_name ILIKE $${paramIndex} OR 
        u.last_name ILIKE $${paramIndex} OR
        u.username ILIKE $${paramIndex} OR
        u.email ILIKE $${paramIndex}
      )`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    if (role) {
      query += ` AND u.role = $${paramIndex}`;
      params.push(role);
      paramIndex++;
    }

    query += ` GROUP BY u.uid`;
    
    const validSortColumns = ['created_at', 'first_name', 'last_name', 'email', 'role'];
    const validSortOrders = ['asc', 'desc'];
    
    if (validSortColumns.includes(sortBy) && validSortOrders.includes(sortOrder)) {
      query += ` ORDER BY u.${sortBy} ${sortOrder.toUpperCase()}`;
    } else {
      query += ` ORDER BY u.created_at DESC`;
    }

    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), parseInt(offset));

    const result = await pool.query(query, params);

    // Get total count
    let countQuery = 'SELECT COUNT(*) as total FROM "user" WHERE 1=1';
    const countParams = [];
    let countParamIndex = 1;

    if (search) {
      countQuery += ` AND (
        first_name ILIKE $${countParamIndex} OR 
        last_name ILIKE $${countParamIndex} OR
        username ILIKE $${countParamIndex} OR
        email ILIKE $${countParamIndex}
      )`;
      countParams.push(`%${search}%`);
      countParamIndex++;
    }

    if (role) {
      countQuery += ` AND role = $${countParamIndex}`;
      countParams.push(role);
    }

    const countResult = await pool.query(countQuery, countParams);

    res.json({
      users: result.rows,
      total: parseInt(countResult.rows[0].total),
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

  } catch (error) {
    console.error('Error getting admin users:', error);
    res.status(500).json({ error: 'Failed to get users' });
  }
});

// PUT /api/admin/users/:userId
router.put('/users/:userId', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { role, isVerified, isBanned } = req.body;

    const pool = await getPool();
    
    const updates = [];
    const params = [];
    let paramIndex = 1;

    if (role !== undefined) {
      const validRoles = ['user', 'admin', 'moderator'];
      if (!validRoles.includes(role)) {
        return res.status(400).json({ error: 'Invalid role' });
      }
      updates.push(`role = $${paramIndex}`);
      params.push(role);
      paramIndex++;
    }

    if (isVerified !== undefined) {
      updates.push(`is_verified = $${paramIndex}`);
      params.push(isVerified);
      paramIndex++;
    }

    if (isBanned !== undefined) {
      updates.push(`is_banned = $${paramIndex}`);
      params.push(isBanned);
      paramIndex++;
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updates.push(`updated_at = NOW()`);
    params.push(userId);

    const query = `
      UPDATE "user" 
      SET ${updates.join(', ')}
      WHERE uid = $${paramIndex}
      RETURNING *
    `;

    const result = await pool.query(query, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      message: 'User updated successfully',
      user: result.rows[0]
    });

  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// GET /api/admin/pins
router.get('/pins', requireAdmin, async (req, res) => {
  try {
    const { 
      limit = 50, 
      offset = 0, 
      search = null, 
      userId = null,
      isDeleted = false
    } = req.query;

    const pool = await getPool();
    
    let query = `
      SELECT 
        p.*,
        u.first_name,
        u.last_name,
        u.username,
        COUNT(DISTINCT pl.user_id) as likes_count,
        COUNT(DISTINCT sp.user_id) as saves_count,
        COUNT(DISTINCT pc.id) as comments_count,
        COUNT(DISTINCT pr.id) as reports_count
      FROM pin p
      JOIN "user" u ON p.user_id = u.uid
      LEFT JOIN pin_likes pl ON p.id = pl.pin_id
      LEFT JOIN saved_pins sp ON p.id = sp.pin_id
      LEFT JOIN pin_comments pc ON p.id = pc.pin_id AND pc.is_deleted = false
      LEFT JOIN pin_reports pr ON p.id = pr.pin_id
      WHERE p.is_deleted = $1
    `;
    
    const params = [isDeleted === 'true'];
    let paramIndex = 2;

    if (search) {
      query += ` AND (
        p.name ILIKE $${paramIndex} OR 
        p.description ILIKE $${paramIndex} OR
        p.series ILIKE $${paramIndex}
      )`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    if (userId) {
      query += ` AND p.user_id = $${paramIndex}`;
      params.push(userId);
      paramIndex++;
    }

    query += ` GROUP BY p.id, u.first_name, u.last_name, u.username`;
    query += ` ORDER BY p.created_at DESC`;
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), parseInt(offset));

    const result = await pool.query(query, params);

    res.json({
      pins: result.rows,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

  } catch (error) {
    console.error('Error getting admin pins:', error);
    res.status(500).json({ error: 'Failed to get pins' });
  }
});

// GET /api/admin/pins/reports
router.get('/pins/reports', requireAdmin, async (req, res) => {
  try {
    const { 
      limit = 50, 
      offset = 0, 
      status = null 
    } = req.query;

    const pool = await getPool();
    
    let query = `
      SELECT 
        pr.*,
        p.name as pin_name,
        p.image_url as pin_image_url,
        u.first_name || ' ' || u.last_name as reporter_name,
        u.username as reporter_username,
        pu.first_name || ' ' || pu.last_name as pin_owner_name,
        pu.username as pin_owner_username
      FROM pin_reports pr
      JOIN pin p ON pr.pin_id = p.id
      JOIN "user" u ON pr.reported_by = u.uid
      JOIN "user" pu ON p.user_id = pu.uid
      WHERE 1=1
    `;
    
    const params = [];
    let paramIndex = 1;

    if (status) {
      query += ` AND pr.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    query += ` ORDER BY pr.created_at DESC`;
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), parseInt(offset));

    const result = await pool.query(query, params);

    res.json({
      reports: result.rows,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

  } catch (error) {
    console.error('Error getting pin reports:', error);
    res.status(500).json({ error: 'Failed to get pin reports' });
  }
});

// PUT /api/admin/pins/reports/:reportId
router.put('/pins/reports/:reportId', requireAdmin, async (req, res) => {
  try {
    const { reportId } = req.params;
    const { status, adminNotes } = req.body;

    if (!status) {
      return res.status(400).json({ error: 'Status is required' });
    }

    const validStatuses = ['pending', 'resolved', 'dismissed'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    const pool = await getPool();
    
    const result = await pool.query(`
      UPDATE pin_reports 
      SET 
        status = $1,
        admin_notes = $2,
        resolved_at = CASE WHEN $1 IN ('resolved', 'dismissed') THEN NOW() ELSE NULL END,
        updated_at = NOW()
      WHERE id = $3
      RETURNING *
    `, [status, adminNotes || null, reportId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Report not found' });
    }

    res.json({
      message: 'Report updated successfully',
      report: result.rows[0]
    });

  } catch (error) {
    console.error('Error updating report:', error);
    res.status(500).json({ error: 'Failed to update report' });
  }
});

// GET /api/admin/analytics
router.get('/analytics', requireAdmin, async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    let interval;
    switch (period) {
      case '7d':
        interval = '7 days';
        break;
      case '30d':
        interval = '30 days';
        break;
      case '90d':
        interval = '90 days';
        break;
      case '1y':
        interval = '1 year';
        break;
      default:
        interval = '30 days';
    }

    const pool = await getPool();
    
    // User growth
    const userGrowth = await pool.query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as new_users
      FROM "user"
      WHERE created_at >= NOW() - INTERVAL '${interval}'
      GROUP BY DATE(created_at)
      ORDER BY date
    `);

    // Pin creation
    const pinGrowth = await pool.query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as new_pins
      FROM pin
      WHERE created_at >= NOW() - INTERVAL '${interval}' AND is_deleted = false
      GROUP BY DATE(created_at)
      ORDER BY date
    `);

    // Most active users
    const activeUsers = await pool.query(`
      SELECT 
        u.uid,
        u.first_name || ' ' || u.last_name as name,
        u.username,
        COUNT(DISTINCT p.id) as pins_created,
        COUNT(DISTINCT m.id) as messages_sent,
        COUNT(DISTINCT pl.pin_id) as pins_liked
      FROM "user" u
      LEFT JOIN pin p ON u.uid = p.user_id AND p.created_at >= NOW() - INTERVAL '${interval}' AND p.is_deleted = false
      LEFT JOIN message m ON u.uid = m.sender_id AND m.created_at >= NOW() - INTERVAL '${interval}'
      LEFT JOIN pin_likes pl ON u.uid = pl.user_id AND pl.liked_at >= NOW() - INTERVAL '${interval}'
      GROUP BY u.uid, u.first_name, u.last_name, u.username
      HAVING COUNT(DISTINCT p.id) + COUNT(DISTINCT m.id) + COUNT(DISTINCT pl.pin_id) > 0
      ORDER BY (COUNT(DISTINCT p.id) + COUNT(DISTINCT m.id) + COUNT(DISTINCT pl.pin_id)) DESC
      LIMIT 10
    `);

    // Popular pins
    const popularPins = await pool.query(`
      SELECT 
        p.id,
        p.name,
        p.image_url,
        u.first_name || ' ' || u.last_name as owner_name,
        COUNT(DISTINCT pl.user_id) as likes_count,
        COUNT(DISTINCT sp.user_id) as saves_count,
        COUNT(DISTINCT pc.id) as comments_count
      FROM pin p
      JOIN "user" u ON p.user_id = u.uid
      LEFT JOIN pin_likes pl ON p.id = pl.pin_id AND pl.liked_at >= NOW() - INTERVAL '${interval}'
      LEFT JOIN saved_pins sp ON p.id = sp.pin_id AND sp.saved_at >= NOW() - INTERVAL '${interval}'
      LEFT JOIN pin_comments pc ON p.id = pc.pin_id AND pc.created_at >= NOW() - INTERVAL '${interval}' AND pc.is_deleted = false
      WHERE p.is_deleted = false
      GROUP BY p.id, p.name, p.image_url, u.first_name, u.last_name
      HAVING COUNT(DISTINCT pl.user_id) + COUNT(DISTINCT sp.user_id) + COUNT(DISTINCT pc.id) > 0
      ORDER BY (COUNT(DISTINCT pl.user_id) + COUNT(DISTINCT sp.user_id) + COUNT(DISTINCT pc.id)) DESC
      LIMIT 10
    `);

    res.json({
      period,
      userGrowth: userGrowth.rows,
      pinGrowth: pinGrowth.rows,
      activeUsers: activeUsers.rows,
      popularPins: popularPins.rows
    });

  } catch (error) {
    console.error('Error getting analytics:', error);
    res.status(500).json({ error: 'Failed to get analytics' });
  }
});

// Get admin dashboard stats
router.get('/dashboard/stats', requireAdmin, async (req, res) => {
  try {
    const pool = await getPool();
    
    // Get various stats in parallel
    const [
      usersResult,
      pinsResult,
      boardsResult,
      messagesResult,
      notificationsResult
    ] = await Promise.all([
      pool.query('SELECT COUNT(*) as count FROM "user"'),
      pool.query('SELECT COUNT(*) as count FROM pin'),
      pool.query('SELECT COUNT(*) as count FROM board'),
      pool.query('SELECT COUNT(*) as count FROM message WHERE created_at > CURRENT_DATE - INTERVAL \'7 days\''),
      pool.query('SELECT COUNT(*) as count FROM notifications WHERE created_at > CURRENT_DATE - INTERVAL \'24 hours\'')
    ]);
    
    res.json({
      totalUsers: parseInt(usersResult.rows[0].count),
      totalPins: parseInt(pinsResult.rows[0].count),
      totalBoards: parseInt(boardsResult.rows[0].count),
      messagesLastWeek: parseInt(messagesResult.rows[0].count),
      notificationsLast24h: parseInt(notificationsResult.rows[0].count),
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error getting admin stats:', error);
    res.status(500).json({ error: 'Failed to get admin stats' });
  }
});

// Get user management data
router.get('/users', requireAdmin, async (req, res) => {
  try {
    const { limit = 50, offset = 0, search } = req.query;
    const pool = await getPool();
    
    let query = `
      SELECT 
        u.*,
        COUNT(DISTINCT b.id) as boards_count,
        COUNT(DISTINCT p.id) as pins_count,
        COUNT(DISTINCT m.id) as messages_count
      FROM "user" u
      LEFT JOIN board b ON u.id = b.user_id
      LEFT JOIN pin p ON u.id = p.user_id
      LEFT JOIN message m ON u.id = m.sender_id
    `;
    
    const params = [];
    
    if (search) {
      query += ` WHERE (
        LOWER(u.first_name) LIKE LOWER($1) OR
        LOWER(u.last_name) LIKE LOWER($1) OR
        LOWER(u.username) LIKE LOWER($1) OR
        LOWER(u.email) LIKE LOWER($1)
      )`;
      params.push(`%${search}%`);
    }
    
    query += `
      GROUP BY u.id
      ORDER BY u.created_at DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;
    params.push(limit, offset);
    
    const result = await pool.query(query, params);
    
    res.json(result.rows);
    
  } catch (error) {
    console.error('Error getting admin users:', error);
    res.status(500).json({ error: 'Failed to get users' });
  }
});

// Update user status
router.put('/users/:userId/status', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { isActive } = req.body;
    
    const pool = await getPool();
    await pool.query(
      'UPDATE "user" SET is_active = $1, updated_at = NOW() WHERE id = $2',
      [isActive, userId]
    );
    
    res.json({ success: true });
    
  } catch (error) {
    console.error('Error updating user status:', error);
    res.status(500).json({ error: 'Failed to update user status' });
  }
});

// Get system logs
router.get('/logs', requireAdmin, async (req, res) => {
  try {
    // In a real implementation, you would read from log files or database
    res.json({
      logs: [
        {
          timestamp: new Date().toISOString(),
          level: 'info',
          message: 'Server started successfully',
          source: 'server'
        }
      ]
    });
    
  } catch (error) {
    console.error('Error getting logs:', error);
    res.status(500).json({ error: 'Failed to get logs' });
  }
});

// Get all feature flags (admin)
router.get('/feature-flags', requireAdmin, async (req, res) => {
  try {
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        ff.*,
        COUNT(DISTINCT ffg.group_id) as assigned_groups_count,
        COUNT(DISTINCT ffu.user_id) as user_overrides_count
      FROM feature_flags ff
      LEFT JOIN feature_flag_groups ffg ON ff.id = ffg.flag_id AND ffg.is_active = true
      LEFT JOIN feature_flag_user_overrides ffu ON ff.id = ffu.flag_id 
        AND (ffu.expires_at IS NULL OR ffu.expires_at > CURRENT_TIMESTAMP)
      GROUP BY ff.id
      ORDER BY ff.created_at DESC
    `);
    
    res.json(result.rows);
    
  } catch (error) {
    console.error('Error getting admin feature flags:', error);
    res.status(500).json({ error: 'Failed to get feature flags' });
  }
});

// Create feature flag (admin)
router.post('/feature-flags', requireAdmin, async (req, res) => {
  try {
    const { key, name, description, defaultValue = false, isActive = true } = req.body;
    
    if (!key || !name) {
      return res.status(400).json({ error: 'Key and name are required' });
    }
    
    const pool = await getPool();
    
    const result = await pool.query(`
      INSERT INTO feature_flags (key, name, description, default_value, is_active, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [key, name, description, defaultValue, isActive]);
    
    res.status(201).json(result.rows[0]);
    
  } catch (error) {
    console.error('Error creating feature flag:', error);
    res.status(500).json({ error: 'Failed to create feature flag' });
  }
});

// Update feature flag (admin)
router.put('/feature-flags/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, defaultValue, isActive } = req.body;
    
    const pool = await getPool();
    
    const result = await pool.query(`
      UPDATE feature_flags 
      SET name = COALESCE($1, name),
          description = COALESCE($2, description),
          default_value = COALESCE($3, default_value),
          is_active = COALESCE($4, is_active),
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
      RETURNING *
    `, [name, description, defaultValue, isActive, id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Feature flag not found' });
    }
    
    res.json(result.rows[0]);
    
  } catch (error) {
    console.error('Error updating feature flag:', error);
    res.status(500).json({ error: 'Failed to update feature flag' });
  }
});

// Delete feature flag (admin)
router.delete('/feature-flags/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const pool = await getPool();
    
    const result = await pool.query('DELETE FROM feature_flags WHERE id = $1', [id]);
    
    if (result.rowCount === 0) {
      return res.status(404).json({ error: 'Feature flag not found' });
    }
    
    res.json({ success: true, message: 'Feature flag deleted' });
    
  } catch (error) {
    console.error('Error deleting feature flag:', error);
    res.status(500).json({ error: 'Failed to delete feature flag' });
  }
});

// Get user groups (admin)
router.get('/user-groups', requireAdmin, async (req, res) => {
  try {
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        ug.*,
        COUNT(DISTINCT ugm.user_id) as member_count
      FROM user_groups ug
      LEFT JOIN user_group_members ugm ON ug.id = ugm.group_id
      GROUP BY ug.id
      ORDER BY ug.created_at DESC
    `);
    
    res.json(result.rows);
    
  } catch (error) {
    console.error('Error getting user groups:', error);
    res.status(500).json({ error: 'Failed to get user groups' });
  }
});

// Create user group (admin)
router.post('/user-groups', requireAdmin, async (req, res) => {
  try {
    const { name, description } = req.body;
    
    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }
    
    const pool = await getPool();
    
    const result = await pool.query(`
      INSERT INTO user_groups (name, description, created_at, updated_at)
      VALUES ($1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [name, description]);
    
    res.status(201).json(result.rows[0]);
    
  } catch (error) {
    console.error('Error creating user group:', error);
    res.status(500).json({ error: 'Failed to create user group' });
  }
});

// Get reported content
router.get('/reports', requireAdmin, async (req, res) => {
  try {
    const { status = 'pending', limit = 20, offset = 0 } = req.query;
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        pr.*,
        p.title as pin_title,
        p.image_url as pin_image,
        u.first_name as reporter_first_name,
        u.last_name as reporter_last_name,
        u.username as reporter_username
      FROM pin_reports pr
      JOIN pin p ON pr.pin_id = p.id
      JOIN "user" u ON pr.user_id = u.id
      WHERE pr.status = $1
      ORDER BY pr.created_at DESC
      LIMIT $2 OFFSET $3
    `, [status, limit, offset]);
    
    res.json(result.rows);
    
  } catch (error) {
    console.error('Error getting reports:', error);
    res.status(500).json({ error: 'Failed to get reports' });
  }
});

// Save FCM token for push notifications
router.post('/push-notifications/tokens', async (req, res) => {
  try {
    const { userId, token, platform, userAgent } = req.body;
    
    if (!userId || !token) {
      return res.status(400).json({ error: 'userId and token are required' });
    }
    
    const pool = await getPool();
    
    // Insert or update token
    await pool.query(`
      INSERT INTO fcm_tokens (user_id, token, platform, user_agent, last_used)
      VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
      ON CONFLICT (token) 
      DO UPDATE SET 
        user_id = EXCLUDED.user_id,
        platform = EXCLUDED.platform,
        user_agent = EXCLUDED.user_agent,
        last_used = CURRENT_TIMESTAMP,
        is_active = true
    `, [userId, token, platform || 'web', userAgent]);
    
    console.log(`✅ FCM token saved for user ${userId}`);
    res.json({ success: true });
    
  } catch (error) {
    console.error('❌ Error saving FCM token:', error);
    res.status(500).json({ error: 'Failed to save FCM token' });
  }
});

export default router; 