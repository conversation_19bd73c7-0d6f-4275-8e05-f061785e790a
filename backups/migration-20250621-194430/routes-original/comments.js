import express from 'express';
import { getPool } from '../config/database.js';

const router = express.Router();

// Delete a comment
router.delete('/:commentId', async (req, res) => {
  try {
    const { commentId } = req.params;
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    const pool = await getPool();
    
    // Check if user owns the comment
    const checkResult = await pool.query(
      'SELECT user_id FROM pin_comments WHERE id = $1',
      [commentId]
    );
    
    if (checkResult.rows.length === 0) {
      return res.status(404).json({ error: 'Comment not found' });
    }
    
    if (checkResult.rows[0].user_id !== userId) {
      return res.status(403).json({ error: 'Not authorized to delete this comment' });
    }
    
    // Delete comment (this will also delete replies due to CASCADE)
    await pool.query('DELETE FROM pin_comments WHERE id = $1', [commentId]);
    
    console.log(`✅ Comment ${commentId} deleted`);
    res.json({ success: true });
    
  } catch (error) {
    console.error('Error deleting comment:', error);
    res.status(500).json({ error: 'Failed to delete comment' });
  }
});

// Update a comment
router.put('/:commentId', async (req, res) => {
  try {
    const { commentId } = req.params;
    const { userId, content } = req.body;
    
    if (!userId || !content) {
      return res.status(400).json({ error: 'User ID and content are required' });
    }
    
    const pool = await getPool();
    
    // Check if user owns the comment
    const checkResult = await pool.query(
      'SELECT user_id FROM pin_comments WHERE id = $1',
      [commentId]
    );
    
    if (checkResult.rows.length === 0) {
      return res.status(404).json({ error: 'Comment not found' });
    }
    
    if (checkResult.rows[0].user_id !== userId) {
      return res.status(403).json({ error: 'Not authorized to update this comment' });
    }
    
    // Update comment
    const result = await pool.query(`
      UPDATE pin_comments 
      SET content = $1, updated_at = CURRENT_TIMESTAMP, is_edited = true
      WHERE id = $2
      RETURNING *
    `, [content, commentId]);
    
    // Get comment with user info
    const commentResult = await pool.query(`
      SELECT 
        c.*,
        u.first_name, u.last_name, u.username, u.avatar_url
      FROM pin_comments c
      JOIN "user" u ON c.user_id = u.id
      WHERE c.id = $1
    `, [commentId]);
    
    // CORREÇÃO: Mapear dados para camelCase para compatibilidade com frontend
    const row = commentResult.rows[0];
    const comment = {
      id: row.id,
      pinId: row.pin_id,
      userId: row.user_id,
      content: row.content,
      parentCommentId: row.parent_id,
      isEdited: row.is_edited || false,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      user: {
        id: row.user_id,
        name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'User',
        firstName: row.first_name,
        lastName: row.last_name,
        username: row.username,
        avatar: row.avatar_url
      }
    };
    
    console.log(`✅ Comment ${commentId} updated`);
    res.json(comment);
    
  } catch (error) {
    console.error('Error updating comment:', error);
    res.status(500).json({ error: 'Failed to update comment' });
  }
});

// Toggle like on a comment
router.post('/:commentId/like', async (req, res) => {
  try {
    const { commentId } = req.params;
    const { userId, isLiked } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    console.log(`🔄 Toggling like for comment ${commentId}, user ${userId}, isLiked: ${isLiked}`);
    
    const pool = await getPool();
    
    if (isLiked) {
      // Add like
      await pool.query(
        'INSERT INTO comment_likes (comment_id, user_id) VALUES ($1, $2) ON CONFLICT (comment_id, user_id) DO NOTHING',
        [commentId, userId]
      );
      console.log(`✅ Like added for comment ${commentId} by user ${userId}`);
    } else {
      // Remove like
      await pool.query(
        'DELETE FROM comment_likes WHERE comment_id = $1 AND user_id = $2',
        [commentId, userId]
      );
      console.log(`✅ Like removed for comment ${commentId} by user ${userId}`);
    }
    
    // Get updated like count
    const countResult = await pool.query(
      'SELECT COUNT(*) as like_count FROM comment_likes WHERE comment_id = $1',
      [commentId]
    );
    
    const likeCount = parseInt(countResult.rows[0].like_count);
    
    res.json({ 
      success: true, 
      isLiked,
      likeCount
    });
    
  } catch (error) {
    console.error('Error toggling comment like:', error);
    res.status(500).json({ error: 'Failed to toggle comment like' });
  }
});

// Check if user liked a comment
router.get('/:commentId/likes/check/:userId', async (req, res) => {
  try {
    const { commentId, userId } = req.params;
    
    console.log(`🔄 Checking like status for comment ${commentId} by user ${userId}`);
    
    const pool = await getPool();
    
    // First check if the comment exists
    const commentCheck = await pool.query(
      'SELECT id FROM pin_comments WHERE id = $1',
      [commentId]
    );
    
    if (commentCheck.rows.length === 0) {
      console.log(`⚠️ Comment ${commentId} not found`);
      return res.status(404).json({ 
        error: 'Comment not found',
        isLiked: false 
      });
    }
    
    // Check if user liked the comment
    const result = await pool.query(
      'SELECT id FROM comment_likes WHERE comment_id = $1 AND user_id = $2',
      [commentId, userId]
    );
    
    const isLiked = result.rows.length > 0;
    console.log(`✅ Like status for comment ${commentId}: ${isLiked}`);
    res.json({ isLiked });
    
  } catch (error) {
    console.error('Error checking comment like status:', error);
    res.status(500).json({ error: 'Failed to check comment like status' });
  }
});

// Get likes for a comment
router.get('/:commentId/likes', async (req, res) => {
  try {
    const { commentId } = req.params;
    const { limit = 20, offset = 0 } = req.query;
    
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        cl.id,
        cl.user_id,
        cl.created_at,
        u.first_name,
        u.last_name,
        u.username,
        u.avatar_url
      FROM comment_likes cl
      LEFT JOIN "user" u ON cl.user_id = u.id
      WHERE cl.comment_id = $1
      ORDER BY cl.created_at DESC
      LIMIT $2 OFFSET $3
    `, [commentId, limit, offset]);
    
    // CORREÇÃO: Mapear dados para camelCase para compatibilidade com frontend
    const likes = result.rows.map(row => ({
      id: row.id,
      userId: row.user_id,
      createdAt: row.created_at,
      user: {
        id: row.user_id,
        name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'User',
        username: row.username,
        avatar: row.avatar_url
      }
    }));
    
    console.log(`✅ Found ${likes.length} likes for comment ${commentId}`);
    res.json(likes);
    
  } catch (error) {
    console.error('Error fetching comment likes:', error);
    res.status(500).json({ error: 'Failed to fetch comment likes' });
  }
});

export default router; 