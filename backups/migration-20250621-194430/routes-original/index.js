import express from 'express';

// Import all route modules
import authRoutes from './auth.js';
import usersRoutes from './users.js';
import boardsRoutes from './boards.js';
import pinsRoutes from './pins.js';
import commentsRoutes from './comments.js';
import messagesRoutes from './messages.js';
import notificationsRoutes from './notifications.js';
import featureFlagsRoutes from './feature-flags.js';
import adminRoutes from './admin.js';
import tradingPointsRoutes from './trading-points.js';
import tradeRoutes from './trade.js';
import utilityRoutes from './utility.js';

const router = express.Router();

// Mount all routes with their respective prefixes
router.use('/auth', authRoutes);
router.use('/users', usersRoutes);
router.use('/boards', boardsRoutes);
router.use('/pins', pinsRoutes);
router.use('/comments', commentsRoutes);
router.use('/messages', messagesRoutes);
router.use('/notifications', notificationsRoutes);
router.use('/feature-flags', featureFlagsRoutes);
router.use('/admin', adminRoutes);
router.use('/trading-points', tradingPointsRoutes);
router.use('/trade', tradeRoutes);
router.use('/utility', utilityRoutes);

// API info endpoint
router.get('/', (req, res) => {
  res.json({
    name: 'PinPal API',
    version: '1.0.0',
    description: 'Modular PinPal API Server',
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      boards: '/api/boards',
      pins: '/api/pins',
      comments: '/api/comments',
      messages: '/api/messages',
      notifications: '/api/notifications',
      featureFlags: '/api/feature-flags',
      admin: '/api/admin',
      tradingPoints: '/api/trading-points',
      trade: '/api/trade',
      utility: '/api/utility'
    },
    timestamp: new Date().toISOString()
  });
});

export default router; 