import { connectDataConnectEmulator, getDataConnect } from 'firebase/data-connect';
import { 
  connectorConfig, 
  createPin, 
  getPin,
  getUserPins,
  updatePin,
  deletePin,
  addPinToBoard,
  CreatePinVariables,
  GetPinVariables,
  GetUserPinsVariables,
  UpdatePinVariables,
  DeletePinVariables,
  AddPinToBoardVariables
} from '../../../dataconnect-generated/js/default-connector';
import { dataConnect } from './config';

// Note: Using production Data Connect service
// The service is deployed at: https://console.firebase.google.com/project/iconpal-cf925/dataconnect

export interface Pin {
  id: string;
  userId: string;
  title: string;
  description?: string;
  imageUrl: string;
  origin?: string;
  releaseYear?: number;
  originalPrice?: number;
  pinNumber?: string;
  tradable: boolean;
  isPublic: boolean;
  likesCount: number;
  viewsCount: number;
  tradesCount: number;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    username?: string;
    displayName?: string;
    avatarUrl?: string;
  };
}

export interface CreatePinData {
  userId: string;
  title: string;
  description?: string;
  imageUrl: string;
  origin?: string;
  releaseYear?: number;
  originalPrice?: number;
  pinNumber?: string;
  tradable?: boolean;
  isPublic?: boolean;
  boardId?: string; // Optional board to add pin to
}

export interface CreatePinInput {
  title: string;
  description?: string;
  imageUrl: string;
  origin?: string;
  releaseYear?: number;
  originalPrice?: number;
  pinNumber?: string;
  tradable?: boolean;
  isPublic?: boolean;
}

export interface UpdatePinInput {
  title?: string;
  description?: string;
  imageUrl?: string;
  origin?: string;
  releaseYear?: number;
  originalPrice?: number;
  pinNumber?: string;
  tradable?: boolean;
  isPublic?: boolean;
}

export class PinsDataConnectService {
  /**
   * Get pin by ID
   */
  async getById(id: string): Promise<Pin | null> {
    try {
      console.log('🔄 Getting pin by ID:', id);
      
      const variables: GetPinVariables = { id };
      const result = await getPin(dataConnect, variables);
      
      if (!result.data.pin) {
        return null;
      }

      const pin = result.data.pin;
      return {
        id: pin.id,
        userId: pin.user.id,
        title: pin.title,
        description: pin.description || undefined,
        imageUrl: pin.imageUrl,
        origin: pin.origin || undefined,
        releaseYear: pin.releaseYear || undefined,
        originalPrice: pin.originalPrice || undefined,
        pinNumber: pin.pinNumber || undefined,
        tradable: pin.tradable,
        isPublic: pin.isPublic,
        likesCount: pin.likesCount,
        viewsCount: pin.viewsCount,
        tradesCount: pin.tradesCount,
        createdAt: pin.createdAt,
        updatedAt: pin.updatedAt,
        user: pin.user  // ← CORREÇÃO: Incluir dados do usuário
      };
    } catch (error) {
      console.error('❌ Error fetching pin:', error);
      throw new Error('Failed to fetch pin');
    }
  }

  /**
   * Get user pins
   */
  async getUserPins(userId: string, limit = 20): Promise<Pin[]> {
    try {
      console.log('🔄 Getting user pins:', { userId, limit });
      
      const variables: GetUserPinsVariables = { userId, limit };
      
      const result = await getUserPins(dataConnect, variables);
      
      return result.data.pins.map(pin => ({
        id: pin.id,
        userId: pin.user.id,
        title: pin.title,
        description: pin.description || undefined,
        imageUrl: pin.imageUrl,
        origin: pin.origin || undefined,
        releaseYear: pin.releaseYear || undefined,
        originalPrice: pin.originalPrice || undefined,
        pinNumber: pin.pinNumber || undefined,
        tradable: pin.tradable,
        isPublic: pin.isPublic,
        likesCount: pin.likesCount,
        viewsCount: pin.viewsCount,
        tradesCount: pin.tradesCount,
        createdAt: pin.createdAt,
        updatedAt: pin.updatedAt,
        user: pin.user
      }));
    } catch (error) {
      console.error('❌ Error fetching user pins:', error);
      throw new Error('Failed to fetch user pins');
    }
  }

  /**
   * Create new pin
   */
  async create(data: CreatePinData): Promise<{ id: string }> {
    try {
      console.log('🔄 Creating pin:', data);
      
      const variables: CreatePinVariables = {
        userId: data.userId,
        title: data.title,
        imageUrl: data.imageUrl,
        origin: data.origin,
        releaseYear: data.releaseYear,
        originalPrice: data.originalPrice,
        pinNumber: data.pinNumber,
        tradable: data.tradable || false,
        isPublic: data.isPublic !== false
      };
      
      const result = await createPin(dataConnect, variables);
      const pinId = result.data.pin_insert.id;
      
      console.log('✅ Pin created with ID:', pinId);
      
      // If boardId is provided, add pin to board
      if (data.boardId) {
        try {
          console.log('🔄 Adding pin to board:', { pinId, boardId: data.boardId });
          
          const addToBoardVariables: AddPinToBoardVariables = {
            boardId: data.boardId,
            pinId: pinId
          };
          
          await addPinToBoard(dataConnect, addToBoardVariables);
          console.log('✅ Pin added to board successfully');
        } catch (boardError) {
          console.error('❌ Error adding pin to board:', boardError);
          // Don't throw here - pin was created successfully, just board association failed
          console.warn('⚠️ Pin created but failed to add to board. Pin ID:', pinId);
        }
      }
      
      return { id: pinId };
    } catch (error) {
      console.error('❌ Error creating pin:', error);
      throw new Error('Failed to create pin');
    }
  }

  /**
   * Add existing pin to board
   */
  async addPinToBoard(pinId: string, boardId: string): Promise<void> {
    try {
      console.log('🔄 Adding existing pin to board:', { pinId, boardId });
      
      const variables: AddPinToBoardVariables = {
        boardId,
        pinId
      };
      
      await addPinToBoard(dataConnect, variables);
      console.log('✅ Pin added to board successfully');
    } catch (error) {
      console.error('❌ Error adding pin to board:', error);
      throw new Error('Failed to add pin to board');
    }
  }

  /**
   * Update pin
   */
  async update(id: string, data: Partial<CreatePinData>): Promise<void> {
    try {
      console.log('🔄 PinsDataConnectService.update iniciado');
      console.log('📝 ID do pin:', id);
      console.log('📝 Dados recebidos:', data);
      console.log('🔍 Verificação específica do title:', {
        'data.title': data.title,
        'typeof data.title': typeof data.title,
        'data.title === undefined': data.title === undefined,
        'data.title === null': data.title === null,
        'data.title === ""': data.title === '',
      });
      
      // CORREÇÃO CRÍTICA: Construir objeto apenas com campos fornecidos
      // Campos obrigatórios (title, imageUrl, tradable, isPublic) só são incluídos se fornecidos
      // Campos opcionais podem ser null se fornecidos explicitamente
      
      const variables: UpdatePinVariables = { id };
      
      // Campos obrigatórios - só incluir se fornecidos
      if (data.title !== undefined) {
        variables.title = data.title;
        console.log('✅ Incluindo title:', data.title);
      }
      
      if (data.imageUrl !== undefined) {
        variables.imageUrl = data.imageUrl;
        console.log('✅ Incluindo imageUrl:', data.imageUrl);
      }
      
      if (data.tradable !== undefined) {
        variables.tradable = data.tradable;
        console.log('✅ Incluindo tradable:', data.tradable);
      }
      
      if (data.isPublic !== undefined) {
        variables.isPublic = data.isPublic;
        console.log('✅ Incluindo isPublic:', data.isPublic);
      }
      
      // Campos opcionais - converter undefined para null
      if (data.description !== undefined) {
        variables.description = data.description;
        console.log('✅ Incluindo description:', data.description);
      }
      
      if (data.origin !== undefined) {
        variables.origin = data.origin;
        console.log('✅ Incluindo origin:', data.origin);
      }
      
      if (data.releaseYear !== undefined) {
        variables.releaseYear = data.releaseYear;
        console.log('✅ Incluindo releaseYear:', data.releaseYear);
      }
      
      if (data.originalPrice !== undefined) {
        variables.originalPrice = data.originalPrice;
        console.log('✅ Incluindo originalPrice:', data.originalPrice);
      }
      
      if (data.pinNumber !== undefined) {
        variables.pinNumber = data.pinNumber;
        console.log('✅ Incluindo pinNumber:', data.pinNumber);
      }
      
      console.log('📡 Variáveis finais para GraphQL:', variables);
      console.log('🔍 Verificação final dos campos obrigatórios:', {
        'variables.title': variables.title,
        'variables.imageUrl': variables.imageUrl,
        'variables.tradable': variables.tradable,
        'variables.isPublic': variables.isPublic,
        'title incluído': 'title' in variables,
        'imageUrl incluído': 'imageUrl' in variables,
        'tradable incluído': 'tradable' in variables,
        'isPublic incluído': 'isPublic' in variables,
      });
      
      await updatePin(dataConnect, variables);
      console.log('✅ updatePin GraphQL executado com sucesso');
    } catch (error) {
      console.error('❌ Erro no PinsDataConnectService.update:', error);
      if (error instanceof Error) {
        console.error('❌ Mensagem do erro:', error.message);
        console.error('❌ Stack do erro:', error.stack);
      }
      throw new Error('Failed to update pin');
    }
  }

  /**
   * Delete pin
   */
  async delete(id: string): Promise<void> {
    try {
      console.log('🔄 Deleting pin:', id);
      
      const variables: DeletePinVariables = { id };
      await deletePin(dataConnect, variables);
      
      console.log('✅ Pin deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting pin:', error);
      throw new Error('Failed to delete pin');
    }
  }

  /**
   * Get pins for trade
   */
  async getPinsForTrade(releaseYear?: number, limit: number = 20): Promise<Pin[]> {
    try {
      console.log('🔄 Getting pins for trade:', { releaseYear, limit });
      
      // This would use the GetPinsForTrade query
      // For now, returning empty array as placeholder
      return [];
    } catch (error) {
      console.error('❌ Error fetching pins for trade:', error);
      throw new Error('Failed to fetch pins for trade');
    }
  }

  /**
   * Get popular pins
   */
  async getPopularPins(limit: number = 20): Promise<Pin[]> {
    try {
      console.log('🔄 Getting popular pins:', { limit });
      
      // This would use the GetPopularPins query
      // For now, returning empty array as placeholder
      return [];
    } catch (error) {
      console.error('❌ Error fetching popular pins:', error);
      throw new Error('Failed to fetch popular pins');
    }
  }

  /**
   * Get pins by price range
   */
  async getPinsByPriceRange(minPrice?: number, maxPrice?: number, limit: number = 20): Promise<Pin[]> {
    try {
      console.log('🔄 Getting pins by price range:', { minPrice, maxPrice, limit });
      
      // This would use the GetPinsByPriceRange query
      // For now, returning empty array as placeholder
      return [];
    } catch (error) {
      console.error('❌ Error fetching pins by price range:', error);
      throw new Error('Failed to fetch pins by price range');
    }
  }

  /**
   * Get pins by year
   */
  async getPinsByYear(releaseYear: number, limit: number = 20): Promise<Pin[]> {
    try {
      console.log('🔄 Getting pins by year:', { releaseYear, limit });
      
      // This would use the GetPinsByYear query
      // For now, returning empty array as placeholder
      return [];
    } catch (error) {
      console.error('❌ Error fetching pins by year:', error);
      throw new Error('Failed to fetch pins by year');
    }
  }

  /**
   * Get pins by origin
   */
  async getPinsByOrigin(origin: string, limit: number = 20): Promise<Pin[]> {
    try {
      console.log('🔄 Getting pins by origin:', { origin, limit });
      
      // This would use the GetPinsByOrigin query
      // For now, returning empty array as placeholder
      return [];
    } catch (error) {
      console.error('❌ Error fetching pins by origin:', error);
      throw new Error('Failed to fetch pins by origin');
    }
  }

  /**
   * Get pin by number
   */
  async getPinByNumber(pinNumber: string): Promise<Pin | null> {
    try {
      console.log('🔄 Getting pin by number:', pinNumber);
      
      // This would use the GetPinByNumber query
      // For now, returning null as placeholder
      return null;
    } catch (error) {
      console.error('❌ Error fetching pin by number:', error);
      throw new Error('Failed to fetch pin by number');
    }
  }

  /**
   * Like pin
   */
  async likePin(pinId: string): Promise<void> {
    try {
      console.log('🔄 Liking pin:', pinId);
      
      // This would use the LikePin mutation
      console.log('✅ Pin liked successfully');
    } catch (error) {
      console.error('❌ Error liking pin:', error);
      throw new Error('Failed to like pin');
    }
  }

  /**
   * Unlike pin
   */
  async unlikePin(pinId: string): Promise<void> {
    try {
      console.log('🔄 Unliking pin:', pinId);
      
      // This would use the UnlikePin mutation
      console.log('✅ Pin unliked successfully');
    } catch (error) {
      console.error('❌ Error unliking pin:', error);
      throw new Error('Failed to unlike pin');
    }
  }

  /**
   * Increment views
   */
  async incrementViews(pinId: string): Promise<void> {
    try {
      console.log('🔄 Incrementing pin views:', pinId);
      
      // This would use the IncrementPinViews mutation
      console.log('✅ Pin views incremented successfully');
    } catch (error) {
      console.error('❌ Error incrementing pin views:', error);
      throw new Error('Failed to increment pin views');
    }
  }

  // Legacy methods for compatibility
  async getAllForUser(userId: string): Promise<Pin[]> {
    return this.getUserPins(userId);
  }

  async search(query: string): Promise<Pin[]> {
    // Placeholder for search functionality
    return [];
  }

  async getByLocation(latitude: number, longitude: number, radius = 1000): Promise<Pin[]> {
    // Placeholder for location-based search
    return [];
  }

  async getByCategory(category: string, limit = 20): Promise<Pin[]> {
    // Placeholder for category-based search
    return [];
  }
}

// Export singleton instance
export const pinsDataConnectService = new PinsDataConnectService(); 