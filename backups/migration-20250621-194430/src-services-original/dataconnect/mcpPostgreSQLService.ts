// Service para acesso direto ao PostgreSQL via MCP
// Este serviço permite queries SQL diretas quando o Data Connect não for suficiente

export interface SQLQueryResult {
  rows: any[];
  rowCount: number;
  fields: any[];
}

export class MCPPostgreSQLService {
  /**
   * Executa uma query SQL diretamente no PostgreSQL
   * @param sql - A query SQL para executar
   * @returns Resultado da query
   */
  async executeQuery(sql: string): Promise<SQLQueryResult> {
    try {
      // Note: Esta função seria implementada usando o MCP
      // Por agora, retornamos um mock para evitar erros
      console.warn('MCP PostgreSQL Service: Query não executada (implementação pendente)');
      console.log('SQL:', sql);
      
      return {
        rows: [],
        rowCount: 0,
        fields: []
      };
    } catch (error) {
      console.error('Error executing SQL query:', error);
      throw new Error('Failed to execute SQL query');
    }
  }

  /**
   * Verifica a conexão com o banco
   */
  async checkConnection(): Promise<boolean> {
    try {
      const result = await this.executeQuery('SELECT 1 as test');
      return result.rowCount > 0;
    } catch (error) {
      console.error('Database connection failed:', error);
      return false;
    }
  }

  /**
   * Lista todas as tabelas do banco
   */
  async listTables(): Promise<string[]> {
    try {
      const result = await this.executeQuery(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name
      `);
      
      return result.rows.map(row => row.table_name);
    } catch (error) {
      console.error('Error listing tables:', error);
      throw new Error('Failed to list tables');
    }
  }

  /**
   * Obtém informações sobre uma tabela específica
   */
  async getTableInfo(tableName: string): Promise<any[]> {
    try {
      const result = await this.executeQuery(`
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default,
          character_maximum_length
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
          AND table_name = '${tableName}'
        ORDER BY ordinal_position
      `);
      
      return result.rows;
    } catch (error) {
      console.error(`Error getting table info for ${tableName}:`, error);
      throw new Error(`Failed to get table info for ${tableName}`);
    }
  }

  /**
   * Executa queries analíticas complexas
   */
  async getAnalytics(): Promise<any> {
    try {
      const queries = {
        totalUsers: 'SELECT COUNT(*) as count FROM "user"',
        totalPins: 'SELECT COUNT(*) as count FROM pin',
        totalBoards: 'SELECT COUNT(*) as count FROM board',
        totalTradingPoints: 'SELECT COUNT(*) as count FROM trading_point',
        totalCheckIns: 'SELECT COUNT(*) as count FROM check_in',
        
        // Analytics mais avançadas
        pinsPerCategory: `
          SELECT category, COUNT(*) as count 
          FROM pin 
          GROUP BY category 
          ORDER BY count DESC
        `,
        
        boardsWithMostPins: `
          SELECT b.name, COUNT(bp.pin_id) as pin_count
          FROM board b
          LEFT JOIN board_pin bp ON b.id = bp.board_id
          GROUP BY b.id, b.name
          ORDER BY pin_count DESC
          LIMIT 10
        `,
        
        mostActiveTradingPoints: `
          SELECT tp.name, COUNT(ci.id) as checkin_count
          FROM trading_point tp
          LEFT JOIN check_in ci ON tp.id = ci.trading_point_id
          GROUP BY tp.id, tp.name
          ORDER BY checkin_count DESC
          LIMIT 10
        `
      };

      const results: any = {};

      for (const [key, query] of Object.entries(queries)) {
        try {
          results[key] = await this.executeQuery(query);
        } catch (error) {
          console.error(`Error in analytics query ${key}:`, error);
          results[key] = { rows: [], rowCount: 0, fields: [] };
        }
      }

      return results;
    } catch (error) {
      console.error('Error getting analytics:', error);
      throw new Error('Failed to get analytics');
    }
  }

  /**
   * Busca avançada usando SQL completo
   */
  async advancedSearch(params: {
    query?: string;
    category?: string;
    location?: { lat: number; lng: number; radius: number };
    dateRange?: { start: string; end: string };
    limit?: number;
  }): Promise<any[]> {
    try {
      let whereClause = 'WHERE 1=1';
      
      if (params.query) {
        whereClause += ` AND (title ILIKE '%${params.query}%' OR description ILIKE '%${params.query}%')`;
      }
      
      if (params.category) {
        whereClause += ` AND category = '${params.category}'`;
      }
      
      if (params.dateRange) {
        whereClause += ` AND created_at BETWEEN '${params.dateRange.start}' AND '${params.dateRange.end}'`;
      }
      
      const limit = params.limit || 50;
      
      const sql = `
        SELECT * FROM pin
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT ${limit}
      `;
      
      const result = await this.executeQuery(sql);
      return result.rows;
    } catch (error) {
      console.error('Error in advanced search:', error);
      throw new Error('Failed to perform advanced search');
    }
  }

  /**
   * Backup específico de dados
   */
  async exportData(tableName: string, format: 'json' | 'csv' = 'json'): Promise<string> {
    try {
      const result = await this.executeQuery(`SELECT * FROM ${tableName}`);
      
      if (format === 'json') {
        return JSON.stringify(result.rows, null, 2);
      } else {
        // Implementar CSV se necessário
        return JSON.stringify(result.rows);
      }
    } catch (error) {
      console.error(`Error exporting data from ${tableName}:`, error);
      throw new Error(`Failed to export data from ${tableName}`);
    }
  }

  /**
   * Queries de migração e manutenção
   */
  async runMaintenance(): Promise<void> {
    try {
      const maintenanceQueries = [
        'VACUUM ANALYZE;',
        'REINDEX DATABASE postgres;',
        'UPDATE pg_stat_user_tables SET n_tup_upd = 0;'
      ];

      for (const query of maintenanceQueries) {
        await this.executeQuery(query);
      }
      
      console.log('✅ Database maintenance completed');
    } catch (error) {
      console.error('Error during maintenance:', error);
      throw new Error('Failed to run maintenance');
    }
  }
} 