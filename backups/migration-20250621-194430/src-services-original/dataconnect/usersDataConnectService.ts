import { dataConnect } from './config';

export interface User {
  id: string;
  uid: string;
  email: string;
  username: string;
  displayName: string;
  profileImageUrl?: string;
  bio?: string;
  favoriteCharacter?: string;
  pinsCount: number;
  tradingPreferences: string[];
  location?: string;
  joinedAt: string;
  lastSeen: string;
  isActive: boolean;
  preferences: {
    theme: string;
    notifications: boolean;
    privacy: string;
  };
  stats: {
    totalPins: number;
    totalTrades: number;
    totalBoards: number;
    totalLikes: number;
  };
}

export interface CreateUserData {
  uid: string;
  email: string;
  username: string;
  displayName: string;
  profileImageUrl?: string;
  bio?: string;
  favoriteCharacter?: string;
  location?: string;
}

export class UsersDataConnectService {
  /**
   * Create new user - For now, users are auto-created when they authenticate
   */
  async create(data: CreateUserData): Promise<{ id: string }> {
    try {
      console.log('🔄 Creating user:', data);
      
      // In Firebase Data Connect with auth.uid, users are created automatically
      // when they authenticate. Return the uid as the id.
      console.log('✅ User auto-created via Firebase Auth with ID:', data.uid);
      
      return { id: data.uid };
    } catch (error) {
      console.error('❌ Error creating user:', error);
      throw new Error('Failed to create user');
    }
  }

  /**
   * Delete user - simplified version
   */
  async delete(id: string): Promise<void> {
    try {
      console.log('🔄 Deleting user:', id);
      console.log('✅ User deletion simulated (would cascade delete related data)');
    } catch (error) {
      console.error('❌ Error deleting user:', error);
      throw new Error('Failed to delete user');
    }
  }

  // Other methods simplified or stubbed for compatibility
  async getById(id: string): Promise<User | null> {
    console.log('⚠️ getById not implemented in simplified version');
    return null;
  }

  async getByUid(uid: string): Promise<User | null> {
    return this.getById(uid);
  }

  async getByUsername(username: string): Promise<User | null> {
    console.log('⚠️ getByUsername not implemented in simplified version');
    return null;
  }

  async update(id: string, data: Partial<CreateUserData>): Promise<void> {
    console.log('⚠️ update not implemented in simplified version');
  }

  async search(query: string, limit = 20): Promise<User[]> {
    console.log('⚠️ search not implemented in simplified version');
    return [];
  }

  async isUsernameAvailable(username: string): Promise<boolean> {
    console.log('⚠️ isUsernameAvailable not implemented in simplified version');
    return true;
  }

  async updateStats(id: string, stats: Partial<User['stats']>): Promise<void> {
    console.log('⚠️ updateStats not implemented in simplified version');
  }

  async updateLastSeen(id: string): Promise<void> {
    console.log('⚠️ updateLastSeen not implemented in simplified version');
  }

  async getByIds(ids: string[]): Promise<User[]> {
    console.log('⚠️ getByIds not implemented in simplified version');
    return [];
  }
} 