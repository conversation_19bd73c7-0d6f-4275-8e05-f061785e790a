import { connectDataConnectEmulator, getDataConnect } from 'firebase/data-connect';
import { 
  connectorConfig, 
  createBoard, 
  getBoard,
  getUserBoards,
  getPublicBoards,
  updateBoard,
  deleteBoard,
  getBoardPins,
  addPinToBoard,
  CreateBoardVariables,
  GetBoardVariables,
  GetUserBoardsVariables,
  GetPublicBoardsVariables,
  UpdateBoardVariables,
  DeleteBoardVariables,
  GetBoardPinsVariables,
  AddPinToBoardVariables
} from '../../../dataconnect-generated/js/default-connector';
import { dataConnect } from './config';

// Note: Using production Data Connect service instead of emulator
// The service is already deployed and running at:
// https://console.firebase.google.com/project/iconpal-cf925/dataconnect

export interface Board {
  id: string;
  userId: string;
  name: string;
  description?: string;
  coverImageUrl?: string;
  isPublic: boolean;
  isCollaborative: boolean;
  pinsCount: number;
  followersCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateBoardData {
  userId: string;
  name: string;
  description?: string;
  coverImageUrl?: string;
  isPublic?: boolean;
  isCollaborative?: boolean;
  email?: string;
  username?: string;
  displayName?: string;
}

// Interface que corresponde exatamente à mutation GraphQL CreateBoard
interface CreateBoardInput {
  name: string;
  description?: string;
  coverImageUrl?: string;
  isPublic?: boolean;
  isCollaborative?: boolean;
}

export class BoardsDataConnectService {
  constructor() {
    // Removed PostgreSQL service initialization to prevent browser import issues
  }

  /**
   * Get board by ID
   */
  async getById(id: string): Promise<Board | null> {
    try {
      console.log('🔄 Getting board by ID:', id);
      
      const variables: GetBoardVariables = { id };
      const result = await getBoard(dataConnect, variables);
      
      if (!result.data.board) {
        return null;
      }

      const board = result.data.board;
      return {
        id: board.id,
        userId: '', // Note: Current query doesn't return userId
        name: board.name,
        description: undefined,
        coverImageUrl: undefined,
        isPublic: true,
        isCollaborative: false,
        pinsCount: 0,
        followersCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Error fetching board:', error);
      throw new Error('Failed to fetch board');
    }
  }

  /**
   * Get user boards
   */
  async getUserBoards(userId: string, limit = 20): Promise<Board[]> {
    try {
      console.log('🔄 Getting user boards:', { userId, limit });
      
      const variables: GetUserBoardsVariables = { userId, limit };
      const result = await getUserBoards(dataConnect, variables);
      
      const boards = result.data.boards.map(board => ({
        id: board.id,
        userId,
        name: board.name,
        description: board.description || undefined,
        coverImageUrl: board.coverImageUrl || undefined,
        isPublic: board.isPublic,
        isCollaborative: false,
        pinsCount: board.pinsCount,
        followersCount: 0,
        createdAt: board.createdAt,
        updatedAt: new Date().toISOString()
      }));

      // Note: Pin counts are managed by the PostgreSQL backend
      
      return boards;
    } catch (error) {
      console.error('❌ Error fetching user boards:', error);
      throw new Error('Failed to fetch user boards');
    }
  }

  /**
   * Create new board - Updated to work without direct PostgreSQL access
   */
  async create(data: CreateBoardData): Promise<{ id: string }> {
    try {
      console.log('🔄 Creating board:', data);
      
      // Try to create board directly - if user doesn't exist, the error will be handled
      console.log('📋 Creating board...');
      const result = await createBoard(dataConnect, {
        userId: data.userId,
        name: data.name
      });
      
      console.log('✅ Board created successfully with ID:', result.data.board_insert.id);
      
      return { id: result.data.board_insert.id };
    } catch (error) {
      console.error('❌ Error creating board:', error);
      
      // If the error is related to user not existing, we could make an API call to ensure user exists
      if (error instanceof Error && error.message.includes('foreign key')) {
        console.log('🔄 User might not exist, attempting to create user via API...');
        
        try {
          // Make API call to ensure user exists (this would be handled by server-side code)
          await fetch('http://localhost:3001/api/users/ensure', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              id: data.userId,
              email: data.email || `user${Date.now()}@pinpal.app`,
              username: data.username || `user_${Date.now()}`,
              displayName: data.displayName || 'PinPal User'
            }),
          });
          
          // Retry board creation
          const retryResult = await createBoard(dataConnect, {
            userId: data.userId,
            name: data.name
          });
          
          console.log('✅ Board created successfully after user creation:', retryResult.data.board_insert.id);
          return { id: retryResult.data.board_insert.id };
          
        } catch (apiError) {
          console.error('❌ Failed to create user via API:', apiError);
          throw new Error('Failed to create board: User creation failed');
        }
      }
      
      throw new Error('Failed to create board');
    }
  }

  /**
   * Update board
   */
  async update(id: string, data: Partial<CreateBoardData>): Promise<void> {
    try {
      console.log('🔄 Updating board:', { id, data });
      
      const variables: UpdateBoardVariables = {
        id,
        name: data.name,
        description: data.description,
        isPublic: data.isPublic
      };
      
      await updateBoard(dataConnect, variables);
      
      console.log('✅ Board updated');
    } catch (error) {
      console.error('❌ Error updating board:', error);
      throw new Error('Failed to update board');
    }
  }

  /**
   * Delete board
   */
  async delete(id: string): Promise<void> {
    try {
      console.log('🔄 Deleting board:', id);
      
      await deleteBoard(dataConnect, { id });
      
      console.log('✅ Board deleted');
    } catch (error) {
      console.error('❌ Error deleting board:', error);
      throw new Error('Failed to delete board');
    }
  }

  /**
   * Get public boards
   */
  async getPublicBoards(limit = 20): Promise<Board[]> {
    try {
      console.log('🔄 Getting public boards, limit:', limit);
      
      const variables: GetPublicBoardsVariables = { limit };
      const result = await getPublicBoards(dataConnect, variables);
      
      return result.data.boards.map(board => ({
        id: board.id,
        userId: board.user.id,
        name: board.name,
        description: board.description || undefined,
        coverImageUrl: board.coverImageUrl || undefined,
        isPublic: true,
        isCollaborative: false,
        pinsCount: board.pinsCount,
        followersCount: 0,
        createdAt: board.createdAt,
        updatedAt: new Date().toISOString()
      }));
    } catch (error) {
      console.error('❌ Error fetching public boards:', error);
      throw new Error('Failed to fetch public boards');
    }
  }

  /**
   * Add pin to board
   */
  async addPinToBoard(boardId: string, pinId: string): Promise<void> {
    try {
      console.log('🔄 Adding pin to board:', { boardId, pinId });
      
      try {
        const variables: AddPinToBoardVariables = { boardId, pinId };
        await addPinToBoard(dataConnect, variables);
        
        console.log('✅ Pin added to board successfully');
      } catch (mutationError: any) {
        if (mutationError.message?.includes('operation "AddPinToBoard" not found')) {
          console.log('⚠️ AddPinToBoard mutation not deployed to production yet');
          console.log('📋 Pin creation logged but not associated with board yet');
          // Don't throw error to allow UI to continue working
          return;
        }
        throw mutationError;
      }
    } catch (error) {
      console.error('❌ Error adding pin to board:', error);
      // Don't throw error to allow UI to continue working for now
      console.log('⚠️ Continuing without board association');
    }
  }

  /**
   * Remove pin from board
   */
  async removePinFromBoard(boardId: string, pinId: string): Promise<void> {
    try {
      console.log('🔄 Removing pin from board:', { boardId, pinId });
      // TODO: Implement removePinFromBoard mutation
    } catch (error) {
      console.error('❌ Error removing pin from board:', error);
      throw new Error('Failed to remove pin from board');
    }
  }

  /**
   * Get board pins
   */
  async getBoardPins(boardId: string, limit: number = 20): Promise<any[]> {
    try {
      console.log('🔄 Getting board pins:', { boardId, limit });
      console.log('🔗 DataConnect instance:', dataConnect);
      
      try {
        const variables: GetBoardPinsVariables = { boardId, limit };
        console.log('📋 Query variables:', variables);
        
        console.log('🚀 Executing getBoardPins query...');
        const result = await getBoardPins(dataConnect, variables);
        console.log('✅ getBoardPins query completed:', result);
        
        return result.data.boardPins.map(boardPin => ({
          id: boardPin.pin.id,
          title: boardPin.pin.title,
          description: boardPin.pin.description,
          imageUrl: boardPin.pin.imageUrl,
          origin: boardPin.pin.origin,
          releaseYear: boardPin.pin.releaseYear,
          originalPrice: boardPin.pin.originalPrice,
          pinNumber: boardPin.pin.pinNumber,
          tradable: boardPin.pin.tradable,
          isPublic: boardPin.pin.isPublic,
          likesCount: boardPin.pin.likesCount,
          viewsCount: boardPin.pin.viewsCount,
          tradesCount: boardPin.pin.tradesCount,
          createdAt: boardPin.pin.createdAt,
          updatedAt: boardPin.pin.updatedAt,
          addedAt: boardPin.addedAt,
          user: boardPin.pin.user
        }));
      } catch (queryError: any) {
        if (queryError.message?.includes('operation "GetBoardPins" not found')) {
          console.log('⚠️ GetBoardPins query not deployed to production yet');
          return [];
        }
        throw queryError;
      }
    } catch (error) {
      console.error('❌ Error fetching board pins:', error);
      throw new Error('Failed to fetch board pins');
    }
  }

  // Note: Pin count management is handled by PostgreSQL backend

  async getAllForUser(userId: string): Promise<Board[]> {
    return this.getUserBoards(userId, 100);
  }

  async search(query: string): Promise<Board[]> {
    // Para busca, por agora retornamos boards públicos
    // TODO: Implementar busca específica quando disponível no schema
    const publicBoards = await this.getPublicBoards(50);
    return publicBoards.filter(board => 
      board.name.toLowerCase().includes(query.toLowerCase()) ||
      (board.description && board.description.toLowerCase().includes(query.toLowerCase()))
    );
  }
} 