import { storageService } from './storageService';
import { PlaceService } from '@/modules/trading-points/services/placeService';

export interface PhotoOptimizationResult {
  success: boolean;
  photoUrl?: string;
  firebaseUrl?: string;
  error?: string;
  metadata?: {
    originalGoogleUrl: string;
    fileSize: number;
    dimensions: { width: number; height: number };
    uploadedAt: string;
  };
}

export interface GooglePhotoDownloadResult {
  blob: Blob;
  contentType: string;
  size: number;
  dimensions?: { width: number; height: number };
}

/**
 * Serviço para otimização de fotos do Trading Map
 * Responsável por baixar fotos do Google Places e armazenar no Firebase Storage
 */
export class TradingPointPhotoOptimizationService {
  private placeService: PlaceService;

  constructor() {
    this.placeService = PlaceService.getInstance();
  }

  /**
   * Captura uma foto do Google Places já exibida no DOM e armazena no Firebase Storage
   */
  async captureAndStoreGooglePhotoFromDOM(
    photoElement: HTMLImageElement,
    tradingPointId: string,
    photoReference: string
  ): Promise<PhotoOptimizationResult> {
    try {
      console.log('📸 Capturing and storing Google Places photo from DOM:', photoReference);
      
      // 1. Capturar a foto já carregada no DOM
      const captureResult = await this.captureGooglePhotoFromDOM(photoElement);
      
      // 2. Converter para File object
      const file = new File([captureResult.blob], `google_photo_${photoReference}.jpg`, {
        type: captureResult.contentType
      });
      
      // 3. Comprimir se necessário
      const compressedFile = await storageService.compressImage(file, 1200, 0.85);
      
      // 4. Upload para Firebase Storage
      const firebaseUrl = await storageService.uploadTradingPointPhoto(
        compressedFile,
        tradingPointId
      );
      
      console.log('✅ Photo captured and stored successfully:', firebaseUrl);
      
      return {
        success: true,
        photoUrl: firebaseUrl,
        firebaseUrl,
        metadata: {
          originalGoogleUrl: photoElement.src,
          fileSize: compressedFile.size,
          dimensions: captureResult.dimensions || { width: 0, height: 0 },
          uploadedAt: new Date().toISOString()
        }
      };
      
    } catch (error) {
      console.error('❌ Error capturing and storing Google photo from DOM:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Baixa uma foto do Google Places e armazena no Firebase Storage
   */
  async downloadAndStoreGooglePhoto(
    googlePhotoUrl: string,
    tradingPointId: string,
    photoReference: string
  ): Promise<PhotoOptimizationResult> {
    try {
      console.log('📸 Downloading Google Places photo:', photoReference);
      
      // 1. Baixar a foto do Google Places
      const downloadResult = await this.downloadGooglePhoto(googlePhotoUrl);
      
      // 2. Converter para File object
      const file = new File([downloadResult.blob], `google_photo_${photoReference}.jpg`, {
        type: downloadResult.contentType
      });
      
      // 3. Comprimir se necessário
      const compressedFile = await storageService.compressImage(file, 1200, 0.85);
      
      // 4. Upload para Firebase Storage
      const firebaseUrl = await storageService.uploadTradingPointPhoto(
        compressedFile,
        tradingPointId
      );
      
      console.log('✅ Photo downloaded and stored successfully:', firebaseUrl);
      
      return {
        success: true,
        photoUrl: firebaseUrl,
        firebaseUrl,
        metadata: {
          originalGoogleUrl: googlePhotoUrl,
          fileSize: compressedFile.size,
          dimensions: downloadResult.dimensions || { width: 0, height: 0 },
          uploadedAt: new Date().toISOString()
        }
      };
      
    } catch (error) {
      console.error('❌ Error downloading and storing Google photo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Captura uma foto do Google Places já carregada no DOM e converte para blob
   */
  async captureGooglePhotoFromDOM(photoElement: HTMLImageElement): Promise<GooglePhotoDownloadResult> {
    try {
      console.log('📸 Capturing Google photo from loaded DOM element');
      
      // Criar canvas para converter a imagem em blob
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        throw new Error('Failed to get canvas context');
      }
      
      // Definir dimensões do canvas baseado na imagem carregada
      canvas.width = photoElement.naturalWidth || photoElement.width;
      canvas.height = photoElement.naturalHeight || photoElement.height;
      
      // Desenhar a imagem no canvas
      ctx.drawImage(photoElement, 0, 0);
      
      // Converter canvas para blob
      const blob = await new Promise<Blob>((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to convert canvas to blob'));
          }
        }, 'image/jpeg', 0.9);
      });
      
      const dimensions = {
        width: canvas.width,
        height: canvas.height
      };
      
      console.log('✅ Google photo captured successfully from DOM:', {
        size: blob.size,
        dimensions
      });
      
      return {
        blob,
        contentType: 'image/jpeg',
        size: blob.size,
        dimensions
      };
      
    } catch (error) {
      console.error('❌ Error capturing Google photo from DOM:', error);
      throw error;
    }
  }

  /**
   * Baixa uma foto do Google Places API usando método oficial
   */
  private async downloadGooglePhoto(googlePhotoUrl: string): Promise<GooglePhotoDownloadResult> {
    try {
      console.log('📸 Loading Google photo via official API:', googlePhotoUrl);
      
      // Criar um elemento img temporário para carregar a imagem
      const img = new Image();
      
      // Promise para aguardar o carregamento da imagem
      const imageLoadPromise = new Promise<HTMLImageElement>((resolve, reject) => {
        img.onload = () => {
          console.log('✅ Google photo loaded successfully');
          resolve(img);
        };
        img.onerror = (error) => {
          console.error('❌ Failed to load Google photo:', error);
          reject(new Error('Failed to load Google photo'));
        };
      });
      
      // Carregar a imagem usando a URL oficial do Google
      img.src = googlePhotoUrl;
      const loadedImg = await imageLoadPromise;
      
      // Capturar a imagem já carregada
      return await this.captureGooglePhotoFromDOM(loadedImg);
      
    } catch (error) {
      console.error('❌ Error downloading Google photo:', error);
      throw error;
    }
  }

  /**
   * Obtém as dimensões de uma imagem a partir de um Blob
   */
  private async getImageDimensions(blob: Blob): Promise<{ width: number; height: number } | undefined> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
        URL.revokeObjectURL(img.src);
      };
      img.onerror = () => {
        resolve(undefined);
        URL.revokeObjectURL(img.src);
      };
      img.src = URL.createObjectURL(blob);
    });
  }

  /**
   * Processa múltiplas fotos do Google Places para um trading point
   */
  async processGooglePhotosForTradingPoint(
    tradingPointId: string,
    googlePhotos: Array<{
      name: string;
      photoUri: string;
      width: number;
      height: number;
    }>
  ): Promise<{
    processed: number;
    successful: number;
    failed: number;
    results: PhotoOptimizationResult[];
  }> {
    console.log(`🔄 Processing ${googlePhotos.length} Google photos for trading point:`, tradingPointId);
    
    const results: PhotoOptimizationResult[] = [];
    let successful = 0;
    let failed = 0;

    for (const [index, photo] of googlePhotos.entries()) {
      try {
        console.log(`📸 Processing photo ${index + 1}/${googlePhotos.length}:`, photo.name);
        
        const result = await this.downloadAndStoreGooglePhoto(
          photo.photoUri,
          tradingPointId,
          photo.name.replace(/[^a-zA-Z0-9]/g, '_')
        );
        
        results.push(result);
        
        if (result.success) {
          successful++;
          console.log(`✅ Photo ${index + 1} processed successfully`);
        } else {
          failed++;
          console.log(`❌ Photo ${index + 1} failed:`, result.error);
        }
        
        // Pequena pausa entre downloads para evitar rate limiting
        if (index < googlePhotos.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        
      } catch (error) {
        failed++;
        const errorResult: PhotoOptimizationResult = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
        results.push(errorResult);
        console.log(`❌ Photo ${index + 1} failed with exception:`, error);
      }
    }

    console.log(`🎉 Photo processing completed: ${successful} successful, ${failed} failed`);
    
    return {
      processed: googlePhotos.length,
      successful,
      failed,
      results
    };
  }

  /**
   * Valida se uma URL do Google Places é válida
   */
  async validateGooglePhotoUrl(googlePhotoUrl: string): Promise<boolean> {
    try {
      const response = await fetch(googlePhotoUrl, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Gera URL otimizada do Google Places com parâmetros específicos
   */
  generateOptimizedGooglePhotoUrl(
    photoReference: string,
    maxWidth: number = 800,
    maxHeight: number = 600
  ): string {
    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      throw new Error('Google Maps API key not found');
    }

    // Detectar formato da referência
    if (photoReference.startsWith('places/')) {
      // Novo formato da API
      return `https://places.googleapis.com/v1/${photoReference}/media?maxWidthPx=${maxWidth}&key=${apiKey}`;
    } else {
      // Formato legado
      return `https://maps.googleapis.com/maps/api/place/photo?photo_reference=${photoReference}&maxwidth=${maxWidth}&maxheight=${maxHeight}&key=${apiKey}`;
    }
  }

  /**
   * Limpa fotos antigas do Firebase Storage para um trading point
   * TODO: Implementar quando storageService tiver método listFiles
   */
  async cleanupOldPhotos(tradingPointId: string): Promise<void> {
    try {
      console.log('🧹 Cleanup old photos for trading point:', tradingPointId);
      console.log('ℹ️ Cleanup functionality not implemented yet - requires listFiles method');
      
      // TODO: Implementar quando storageService.listFiles() estiver disponível
      // const files = await storageService.listFiles(`trading-points/${tradingPointId}/`);
      // if (files.length > 0) {
      //   await storageService.deleteFiles(files);
      // }
      
    } catch (error) {
      console.error('❌ Error in cleanup process:', error);
      // Não falhar o processo principal por erro de limpeza
    }
  }

  /**
   * Migra um trading point existente para usar fotos otimizadas
   */
  async migrateTradingPointPhotos(
    tradingPointId: string,
    googlePhotoReference?: string,
    googlePhotos?: string
  ): Promise<PhotoOptimizationResult | null> {
    try {
      console.log('🔄 Migrating photos for trading point:', tradingPointId);
      
      // Se tem googlePhotos (JSON), usar o primeiro
      if (googlePhotos) {
        try {
          const photos = JSON.parse(googlePhotos);
          if (photos && photos.length > 0) {
            const firstPhoto = photos[0];
            const photoUrl = this.generateOptimizedGooglePhotoUrl(firstPhoto.name || firstPhoto);
            
            return await this.downloadAndStoreGooglePhoto(
              photoUrl,
              tradingPointId,
              firstPhoto.name || 'migrated_photo'
            );
          }
        } catch (parseError) {
          console.warn('Failed to parse googlePhotos JSON:', parseError);
        }
      }
      
      // Se tem googlePhotoReference, usar ele
      if (googlePhotoReference) {
        const photoUrl = this.generateOptimizedGooglePhotoUrl(googlePhotoReference);
        
        return await this.downloadAndStoreGooglePhoto(
          photoUrl,
          tradingPointId,
          googlePhotoReference
        );
      }
      
      console.log('ℹ️ No Google photos to migrate for trading point:', tradingPointId);
      return null;
      
    } catch (error) {
      console.error('❌ Error migrating trading point photos:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Migration failed'
      };
    }
  }
}

// Singleton instance
export const tradingPointPhotoOptimizationService = new TradingPointPhotoOptimizationService(); 