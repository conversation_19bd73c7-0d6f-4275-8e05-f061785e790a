/**
 * Users API Service - PostgreSQL Backend
 * Handles user search and management via PostgreSQL API
 */

interface User {
  id: string;
  email: string;
  username?: string;
  displayName?: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

interface SearchUsersResponse {
  users: User[];
}

class UsersApiService {
  private baseUrl = 'http://localhost:3001';

  /**
   * Search users by name, username or email
   */
  async searchUsers(searchQuery: string, limit: number = 20): Promise<User[]> {
    const maxRetries = 3;
    const retryDelay = 1000; // 1 second
    
    if (!searchQuery.trim()) {
      return [];
    }

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 Searching users (attempt ${attempt}/${maxRetries}): "${searchQuery}"`);
        
        const params = new URLSearchParams({
          q: searchQuery.trim(),
          limit: limit.toString()
        });

        const response = await fetch(`${this.baseUrl}/api/users/search?${params}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          const error = await response.json();
          
          // If it's a server error and we have retries left, try again
          if (response.status >= 500 && attempt < maxRetries) {
            console.log(`🔄 Server error, retrying in ${retryDelay}ms...`);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            continue;
          }
          
          throw new Error(error.error || 'Failed to search users');
        }

        const result: SearchUsersResponse = await response.json();
        
        console.log(`✅ Found ${result.users.length} users for query: "${searchQuery}"`);
        
        return result.users;
        
      } catch (error: any) {
        console.error(`❌ Error searching users (attempt ${attempt}/${maxRetries}):`, error);
        
        // If it's a network error and we have retries left, try again
        if (error instanceof TypeError && error.message.includes('fetch') && attempt < maxRetries) {
          console.log(`🌐 Network error, retrying in ${retryDelay}ms...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }
        
        // If it's the last attempt, throw the error
        if (attempt === maxRetries) {
          throw error;
        }
      }
    }
    
    // This should never be reached, but just in case
    return [];
  }

  /**
   * Get user by ID
   */
  async getUserById(userId: string): Promise<User | null> {
    try {
      const response = await fetch(`${this.baseUrl}/api/users/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 404) {
        return null;
      }

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get user');
      }

      const user: User = await response.json();
      return user;
      
    } catch (error) {
      console.error('❌ Error getting user by ID:', error);
      throw error;
    }
  }

  /**
   * Get recent contacts for a user (safer alternative to open search)
   */
  async getRecentContacts(userId: string, limit: number = 5): Promise<User[]> {
    try {
      if (!userId) {
        return [];
      }

      const params = new URLSearchParams({
        limit: limit.toString()
      });

      const response = await fetch(`${this.baseUrl}/api/users/${userId}/recent-contacts?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get recent contacts');
      }

      const result = await response.json();
      
      console.log(`✅ Found ${result.contacts.length} recent contacts for user: ${userId}`);
      
      return result.contacts;
      
    } catch (error) {
      console.error('❌ Error getting recent contacts:', error);
      return []; // Return empty array instead of throwing for this feature
    }
  }
}

// Export singleton instance
export const usersApiService = new UsersApiService();
export default usersApiService; 