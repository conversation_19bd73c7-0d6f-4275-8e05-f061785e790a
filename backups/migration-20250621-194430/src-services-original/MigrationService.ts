import { MigrationStrategy } from '../repositories/BaseRepository';
import { 
  boardsDataConnectService, 
  pinsDataConnectService, 
  tradingPointsDataConnectService
} from './dataconnect';
import type { 
  Pin as PostgreSQLPin,
  CreatePinData 
} from './dataconnect/pinsDataConnectService';
import type { 
  Board as PostgreSQLBoard,
  CreateBoardData 
} from './dataconnect/boardsDataConnectService';

export interface MigrationProgress {
  phase: MigrationPhase;
  entity: string;
  progress: number;
  total: number;
  status: MigrationStatus;
  startTime: Date;
  endTime?: Date;
  errors: string[];
}

export enum MigrationPhase {
  PREPARATION = 'preparation',
  USERS = 'users',
  PINS = 'pins',
  BOARDS = 'boards',
  TRADES = 'trades',
  MESSAGES = 'messages',
  VALIDATION = 'validation',
  CLEANUP = 'cleanup'
}

export enum MigrationStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  PAUSED = 'paused',
  CANCELLED = 'cancelled'
}

// Legacy Firebase types for compatibility
export interface FirestoreBoard {
  id: string;
  userId: string;
  name: string;
  description?: string;
  coverImage?: string;
  isPrivate: boolean;
  isCollaborative?: boolean;
  pins: string[];
  pinsCount: number;
  followersCount: number;
  createdAt: any; // Firestore Timestamp
  lastUpdated: any; // Firestore Timestamp
}

export interface FirestorePin {
  id: string;
  userId: string;
  title: string;
  description?: string;
  imageUrl: string;
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  country?: string;
  category: string;
  rarity?: string;
  condition?: string;
  isForTrade: boolean;
  isPublic: boolean;
  likesCount: number;
  viewsCount: number;
  tradesCount: number;
  createdAt: any;
  updatedAt: any;
}

// Adapter functions to convert between Firebase and PostgreSQL formats
export class MigrationService {
  private progress: Map<MigrationPhase, MigrationProgress> = new Map();
  private currentPhase: MigrationPhase | null = null;
  private isPaused = false;
  private isCancelled = false;

  constructor() {
    this.initializeProgress();
  }

  // ===== CONTROLE DE MIGRAÇÃO =====

  async startMigration(strategy: MigrationStrategy = MigrationStrategy.POSTGRES_WITH_FIRESTORE_FALLBACK): Promise<void> {
    console.log('🚀 Iniciando migração completa...');
    
    try {
      this.currentPhase = MigrationPhase.PREPARATION;
      await this.executePhase(MigrationPhase.PREPARATION, () => this.prepareMigration(strategy));
      
      if (this.shouldContinue()) {
        this.currentPhase = MigrationPhase.USERS;
        await this.executePhase(MigrationPhase.USERS, () => this.migrateUsers());
      }
      
      if (this.shouldContinue()) {
        this.currentPhase = MigrationPhase.PINS;
        await this.executePhase(MigrationPhase.PINS, () => this.migratePins());
      }
      
      if (this.shouldContinue()) {
        this.currentPhase = MigrationPhase.BOARDS;
        await this.executePhase(MigrationPhase.BOARDS, () => this.migrateBoards());
      }
      
      if (this.shouldContinue()) {
        this.currentPhase = MigrationPhase.TRADES;
        await this.executePhase(MigrationPhase.TRADES, () => this.migrateTrades());
      }
      
      if (this.shouldContinue()) {
        this.currentPhase = MigrationPhase.MESSAGES;
        await this.executePhase(MigrationPhase.MESSAGES, () => this.migrateMessages());
      }
      
      if (this.shouldContinue()) {
        this.currentPhase = MigrationPhase.VALIDATION;
        await this.executePhase(MigrationPhase.VALIDATION, () => this.validateMigration());
      }
      
      if (this.shouldContinue()) {
        this.currentPhase = MigrationPhase.CLEANUP;
        await this.executePhase(MigrationPhase.CLEANUP, () => this.cleanupMigration());
      }
      
      console.log('✅ Migração completa concluída com sucesso!');
      
    } catch (error) {
      console.error('❌ Migração falhou:', error);
      await this.handleMigrationFailure(error);
      throw error;
    }
  }

  async pauseMigration(): Promise<void> {
    console.log('⏸️ Pausando migração...');
    this.isPaused = true;
    
    if (this.currentPhase) {
      const progress = this.progress.get(this.currentPhase);
      if (progress) {
        progress.status = MigrationStatus.PAUSED;
      }
    }
  }

  async resumeMigration(): Promise<void> {
    console.log('▶️ Retomando migração...');
    this.isPaused = false;
    
    if (this.currentPhase) {
      const progress = this.progress.get(this.currentPhase);
      if (progress) {
        progress.status = MigrationStatus.RUNNING;
      }
    }
  }

  async cancelMigration(): Promise<void> {
    console.log('🛑 Cancelando migração...');
    this.isCancelled = true;
    
    if (this.currentPhase) {
      const progress = this.progress.get(this.currentPhase);
      if (progress) {
        progress.status = MigrationStatus.CANCELLED;
        progress.endTime = new Date();
      }
    }
  }

  async rollbackMigration(toPhase?: MigrationPhase): Promise<void> {
    console.log('🔄 Iniciando rollback da migração...');
    
    const phases = [
      MigrationPhase.CLEANUP,
      MigrationPhase.VALIDATION,
      MigrationPhase.MESSAGES,
      MigrationPhase.TRADES,
      MigrationPhase.BOARDS,
      MigrationPhase.PINS,
      MigrationPhase.USERS
    ];
    
    for (const phase of phases) {
      if (toPhase && phase === toPhase) break;
      
      const progress = this.progress.get(phase);
      if (progress && progress.status === MigrationStatus.COMPLETED) {
        console.log(`🔄 Fazendo rollback da fase: ${phase}`);
        await this.rollbackPhase(phase);
      }
    }
    
    console.log('✅ Rollback concluído');
  }

  // ===== FASES DE MIGRAÇÃO =====

  private async prepareMigration(strategy: MigrationStrategy): Promise<void> {
    console.log('🔧 Preparando migração...');
    
    // Verificar conexões
    await this.checkConnections();
    
    // Configurar estratégia
    await this.configureStrategy(strategy);
    
    // Criar backups
    await this.createBackups();
    
    // Validar schemas
    await this.validateSchemas();
    
    console.log('✅ Preparação concluída');
  }

  private async migrateUsers(): Promise<void> {
    console.log('👥 Migrando usuários...');
    
    // Implementar migração de usuários
    const { UserMigration } = await import('../../scripts/migrate-users');
    const migration = new UserMigration();
    
    const stats = await migration.migrateUsers();
    
    const progress = this.progress.get(MigrationPhase.USERS)!;
    progress.progress = stats.migrated;
    progress.total = stats.total;
    
    if (stats.failed > 0) {
      progress.errors.push(`${stats.failed} usuários falharam na migração`);
    }
    
    console.log('✅ Migração de usuários concluída');
  }

  private async migratePins(): Promise<void> {
    console.log('📌 Migrando pins...');
    // Implementar migração de pins
    await this.simulateMigration('pins', 5000);
    console.log('✅ Migração de pins concluída');
  }

  private async migrateBoards(): Promise<void> {
    console.log('📋 Migrando boards...');
    // Implementar migração de boards
    await this.simulateMigration('boards', 1200);
    console.log('✅ Migração de boards concluída');
  }

  private async migrateTrades(): Promise<void> {
    console.log('🔄 Migrando trades...');
    // Implementar migração de trades
    await this.simulateMigration('trades', 800);
    console.log('✅ Migração de trades concluída');
  }

  private async migrateMessages(): Promise<void> {
    console.log('💬 Migrando mensagens...');
    // Implementar migração de mensagens
    await this.simulateMigration('messages', 15000);
    console.log('✅ Migração de mensagens concluída');
  }

  private async validateMigration(): Promise<void> {
    console.log('🔍 Validando migração...');
    
    const validations = [
      this.validateUserCount(),
      this.validatePinCount(),
      this.validateBoardCount(),
      this.validateTradeCount(),
      this.validateMessageCount(),
      this.validateDataIntegrity()
    ];
    
    const results = await Promise.allSettled(validations);
    
    const failures = results.filter(r => r.status === 'rejected');
    if (failures.length > 0) {
      throw new Error(`Validação falhou: ${failures.length} verificações falharam`);
    }
    
    console.log('✅ Validação concluída - todos os dados migrados corretamente');
  }

  private async cleanupMigration(): Promise<void> {
    console.log('🧹 Limpando migração...');
    
    // Remover dados temporários
    await this.cleanupTempData();
    
    // Otimizar índices
    await this.optimizeIndexes();
    
    // Atualizar estatísticas
    await this.updateStatistics();
    
    console.log('✅ Limpeza concluída');
  }

  // ===== MÉTODOS AUXILIARES =====

  private async executePhase(phase: MigrationPhase, operation: () => Promise<void>): Promise<void> {
    const progress = this.progress.get(phase)!;
    progress.status = MigrationStatus.RUNNING;
    progress.startTime = new Date();
    
    try {
      await operation();
      progress.status = MigrationStatus.COMPLETED;
      progress.endTime = new Date();
    } catch (error) {
      progress.status = MigrationStatus.FAILED;
      progress.endTime = new Date();
      progress.errors.push(error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  private shouldContinue(): boolean {
    return !this.isPaused && !this.isCancelled;
  }

  private async handleMigrationFailure(error: any): Promise<void> {
    console.error('🚨 Tratando falha na migração...');
    
    // Salvar estado atual
    await this.saveProgressState();
    
    // Notificar administradores
    await this.notifyAdmins(error);
    
    // Preparar para rollback se necessário
    console.log('💡 Para fazer rollback, execute: migrationService.rollbackMigration()');
  }

  private async rollbackPhase(phase: MigrationPhase): Promise<void> {
    switch (phase) {
      case MigrationPhase.USERS:
        await this.rollbackUsers();
        break;
      case MigrationPhase.PINS:
        await this.rollbackPins();
        break;
      case MigrationPhase.BOARDS:
        await this.rollbackBoards();
        break;
      case MigrationPhase.TRADES:
        await this.rollbackTrades();
        break;
      case MigrationPhase.MESSAGES:
        await this.rollbackMessages();
        break;
    }
  }

  // ===== MÉTODOS DE VALIDAÇÃO =====

  private async checkConnections(): Promise<void> {
    // Verificar conexão com PostgreSQL
    // Verificar conexão com Firestore
    console.log('✅ Conexões verificadas');
  }

  private async configureStrategy(strategy: MigrationStrategy): Promise<void> {
    console.log(`🔧 Configurando estratégia: ${strategy}`);
  }

  private async createBackups(): Promise<void> {
    console.log('💾 Criando backups...');
  }

  private async validateSchemas(): Promise<void> {
    console.log('📋 Validando schemas...');
  }

  private async validateUserCount(): Promise<void> {
    // Comparar contagem de usuários
  }

  private async validatePinCount(): Promise<void> {
    // Comparar contagem de pins
  }

  private async validateBoardCount(): Promise<void> {
    // Comparar contagem de boards
  }

  private async validateTradeCount(): Promise<void> {
    // Comparar contagem de trades
  }

  private async validateMessageCount(): Promise<void> {
    // Comparar contagem de mensagens
  }

  private async validateDataIntegrity(): Promise<void> {
    // Verificar integridade referencial
  }

  private async cleanupTempData(): Promise<void> {
    console.log('🗑️ Removendo dados temporários...');
  }

  private async optimizeIndexes(): Promise<void> {
    console.log('⚡ Otimizando índices...');
  }

  private async updateStatistics(): Promise<void> {
    console.log('📊 Atualizando estatísticas...');
  }

  private async saveProgressState(): Promise<void> {
    // Salvar estado da migração
  }

  private async notifyAdmins(error: any): Promise<void> {
    // Notificar administradores sobre falha
  }

  // Rollback methods
  private async rollbackUsers(): Promise<void> {
    console.log('🔄 Rollback de usuários...');
  }

  private async rollbackPins(): Promise<void> {
    console.log('🔄 Rollback de pins...');
  }

  private async rollbackBoards(): Promise<void> {
    console.log('🔄 Rollback de boards...');
  }

  private async rollbackTrades(): Promise<void> {
    console.log('🔄 Rollback de trades...');
  }

  private async rollbackMessages(): Promise<void> {
    console.log('🔄 Rollback de mensagens...');
  }

  // ===== MÉTODOS PÚBLICOS =====

  getProgress(): Map<MigrationPhase, MigrationProgress> {
    return new Map(this.progress);
  }

  getCurrentPhase(): MigrationPhase | null {
    return this.currentPhase;
  }

  getOverallProgress(): { completed: number; total: number; percentage: number } {
    const phases = Array.from(this.progress.values());
    const completed = phases.filter(p => p.status === MigrationStatus.COMPLETED).length;
    const total = phases.length;
    const percentage = (completed / total) * 100;
    
    return { completed, total, percentage };
  }

  // ===== INICIALIZAÇÃO =====

  private initializeProgress(): void {
    const phases = Object.values(MigrationPhase);
    
    phases.forEach(phase => {
      this.progress.set(phase, {
        phase,
        entity: phase,
        progress: 0,
        total: 0,
        status: MigrationStatus.PENDING,
        startTime: new Date(),
        errors: []
      });
    });
  }

  private async simulateMigration(entity: string, total: number): Promise<void> {
    const phase = this.currentPhase!;
    const progress = this.progress.get(phase)!;
    progress.total = total;
    
    for (let i = 0; i <= total; i += Math.floor(total / 10)) {
      if (!this.shouldContinue()) break;
      
      progress.progress = i;
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    progress.progress = total;
  }

  // Board adapters
  static adaptFirestoreBoardToPostgreSQL(firestoreBoard: FirestoreBoard): CreateBoardData {
    return {
      userId: firestoreBoard.userId,
      name: firestoreBoard.name,
      description: firestoreBoard.description,
      coverImageUrl: firestoreBoard.coverImage,
      isPublic: !firestoreBoard.isPrivate,
      isCollaborative: firestoreBoard.isCollaborative || false
    };
  }

  static adaptPostgreSQLBoardToFirestore(pgBoard: PostgreSQLBoard): FirestoreBoard {
    return {
      id: pgBoard.id,
      userId: pgBoard.userId,
      name: pgBoard.name,
      description: pgBoard.description,
      coverImage: pgBoard.coverImageUrl,
      isPrivate: !pgBoard.isPublic,
      isCollaborative: pgBoard.isCollaborative,
      pins: [], // Will be populated separately
      pinsCount: pgBoard.pinsCount,
      followersCount: pgBoard.followersCount,
      createdAt: new Date(pgBoard.createdAt),
      lastUpdated: new Date(pgBoard.updatedAt)
    };
  }

  // Pin adapters
  static adaptFirestorePinToPostgreSQL(firestorePin: FirestorePin): CreatePinData {
    return {
      userId: firestorePin.userId,
      title: firestorePin.title,
      description: firestorePin.description,
      imageUrl: firestorePin.imageUrl,
      // Mapear campos antigos para novos quando possível
      origin: undefined, // Não existe no Firestore antigo
      releaseYear: undefined, // Não existe no Firestore antigo
      originalPrice: undefined, // Não existe no Firestore antigo
      pinNumber: undefined, // Não existe no Firestore antigo
      tradable: firestorePin.isForTrade,
      isPublic: firestorePin.isPublic
    };
  }

  static adaptPostgreSQLPinToFirestore(pgPin: PostgreSQLPin): FirestorePin {
    return {
      id: pgPin.id,
      userId: pgPin.userId,
      title: pgPin.title,
      description: pgPin.description,
      imageUrl: pgPin.imageUrl,
      // Campos removidos - usar valores padrão para compatibilidade
      latitude: 0,
      longitude: 0,
      address: undefined,
      city: undefined,
      country: undefined,
      category: 'OTHER', // Valor padrão
      rarity: 'COMMON', // Valor padrão
      condition: undefined,
      isForTrade: pgPin.tradable,
      isPublic: pgPin.isPublic,
      likesCount: pgPin.likesCount,
      viewsCount: pgPin.viewsCount,
      tradesCount: pgPin.tradesCount,
      createdAt: new Date(pgPin.createdAt),
      updatedAt: new Date(pgPin.updatedAt)
    };
  }

  // Compatibility service wrappers
  static createBoardsService() {
    return {
      async getUserBoards(userId: string, includePrivate: boolean = false): Promise<FirestoreBoard[]> {
        const pgBoards = await boardsDataConnectService.getUserBoards(userId);
        return pgBoards.map(board => MigrationService.adaptPostgreSQLBoardToFirestore(board));
      },

      async getById(id: string): Promise<FirestoreBoard | null> {
        const pgBoard = await boardsDataConnectService.getById(id);
        return pgBoard ? MigrationService.adaptPostgreSQLBoardToFirestore(pgBoard) : null;
      },

      async create(data: {
        name: string;
        description?: string;
        coverImage?: string;
        isPrivate: boolean;
        userId: string;
      }): Promise<FirestoreBoard> {
        const createData: CreateBoardData = {
          userId: data.userId,
          name: data.name,
          description: data.description,
          coverImageUrl: data.coverImage,
          isPublic: !data.isPrivate,
          isCollaborative: false
        };

        const result = await boardsDataConnectService.create(createData);
        const createdBoard = await boardsDataConnectService.getById(result.id);
        
        if (!createdBoard) {
          throw new Error('Failed to retrieve created board');
        }

        return MigrationService.adaptPostgreSQLBoardToFirestore(createdBoard);
      }
    };
  }
}