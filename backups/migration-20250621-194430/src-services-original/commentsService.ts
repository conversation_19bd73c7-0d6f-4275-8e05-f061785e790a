import { 
  collection, 
  doc, 
  getDocs, 
  addDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { db } from './firebase';

export interface Comment {
  id: string;
  pinId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface CommentInput {
  pinId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
}

class CommentsService {
  private collection = collection(db, 'comments');

  /**
   * Get comments for a specific pin
   */
  async getCommentsByPinId(pinId: string, maxResults: number = 50): Promise<Comment[]> {
    try {
      const q = query(
        this.collection,
        where('pinId', '==', pinId),
        orderBy('createdAt', 'desc'),
        limit(maxResults)
      );

      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Comment));

    } catch (error) {
      console.error('Error getting comments:', error);
      return [];
    }
  }

  /**
   * Add a new comment
   */
  async addComment(commentData: CommentInput): Promise<Comment | null> {
    try {
      const now = Timestamp.now();
      const newComment = {
        ...commentData,
        createdAt: now,
        updatedAt: now
      };

      const docRef = await addDoc(this.collection, newComment);
      
      return {
        id: docRef.id,
        ...newComment
      };

    } catch (error) {
      console.error('Error adding comment:', error);
      return null;
    }
  }

  /**
   * Delete a comment
   */
  async deleteComment(commentId: string): Promise<boolean> {
    try {
      await deleteDoc(doc(this.collection, commentId));
      return true;
    } catch (error) {
      console.error('Error deleting comment:', error);
      return false;
    }
  }

  /**
   * Get comments count for a pin
   */
  async getCommentsCount(pinId: string): Promise<number> {
    try {
      const q = query(
        this.collection,
        where('pinId', '==', pinId)
      );

      const snapshot = await getDocs(q);
      return snapshot.size;

    } catch (error) {
      console.error('Error getting comments count:', error);
      return 0;
    }
  }
}

export const commentsService = new CommentsService(); 