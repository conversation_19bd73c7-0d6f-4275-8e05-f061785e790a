export interface Notification {
  id: string;
  type: 'mention' | 'reply' | 'like' | 'follow' | 'comment' | 'pin_save' | 'board_follow';
  fromUser: {
    id: string;
    name: string;
    username: string;
    avatar?: string;
  };
  title: string;
  content: string;
  pinId?: string;
  commentId?: string;
  boardId?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  timestamp: string;
  isRead: boolean;
  readAt?: string;
}

class NotificationService {
  private baseUrl = 'http://localhost:3001/api';
  private listeners: ((notifications: Notification[]) => void)[] = [];

  // Buscar notificações do usuário
  async getUserNotifications(
    userId: string, 
    options: {
      limit?: number;
      offset?: number;
      type?: string;
      unreadOnly?: boolean;
    } = {}
  ): Promise<Notification[]> {
    try {
      const params = new URLSearchParams();
      if (options.limit) params.append('limit', options.limit.toString());
      if (options.offset) params.append('offset', options.offset.toString());
      if (options.type) params.append('type', options.type);
      if (options.unreadOnly) params.append('unread_only', 'true');

      const response = await fetch(`${this.baseUrl}/notifications/${userId}?${params}`);
      if (!response.ok) throw new Error('Failed to fetch notifications');
      
      const rawNotifications = await response.json();
      
      // Map snake_case data to camelCase format expected by frontend
      const notifications: Notification[] = rawNotifications.map((raw: any) => ({
        id: raw.id,
        type: raw.type,
        fromUser: {
          id: raw.from_user_id,
          name: `${raw.from_user_first_name || ''} ${raw.from_user_last_name || ''}`.trim() || raw.from_user_username || 'Unknown User',
          username: raw.from_user_username || '',
          avatar: raw.from_user_avatar_url
        },
        title: raw.title,
        content: raw.content,
        pinId: raw.pin_id,
        commentId: raw.comment_id,
        boardId: raw.board_id,
        actionUrl: raw.action_url,
        metadata: raw.metadata,
        timestamp: raw.created_at,
        isRead: raw.is_read,
        readAt: raw.read_at
      }));
      
      return notifications;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }

  // Criar nova notificação
  async createNotification(data: {
    userId: string;
    type: string;
    fromUserId: string;
    title: string;
    content: string;
    pinId?: string;
    commentId?: string;
    boardId?: string;
    actionUrl?: string;
    metadata?: Record<string, any>;
  }): Promise<Notification | null> {
    try {
      const response = await fetch(`${this.baseUrl}/notifications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) throw new Error('Failed to create notification');
      
      const notification = await response.json();
      this.notifyListeners();
      
      // Mostrar notificação do browser
      this.showBrowserNotification(notification);
      
      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      return null;
    }
  }

  // Marcar notificação como lida
  async markAsRead(notificationId: string, userId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) throw new Error('Failed to mark as read');
      
      this.notifyListeners();
      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  }

  // Marcar todas como lidas
  async markAllAsRead(userId: string): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/notifications/mark-all-read/${userId}`, {
        method: 'PUT',
      });

      if (!response.ok) throw new Error('Failed to mark all as read');
      
      const result = await response.json();
      this.notifyListeners();
      return result.updated || 0;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return 0;
    }
  }

  // Contar notificações não lidas
  async getUnreadCount(userId: string): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/notifications/${userId}/unread-count`);
      if (!response.ok) throw new Error('Failed to get unread count');
      
      const result = await response.json();
      return result.unreadCount || 0;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  // Deletar notificação específica
  async deleteNotification(notificationId: string, userId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) throw new Error('Failed to delete notification');
      
      this.notifyListeners();
      return true;
    } catch (error) {
      console.error('Error deleting notification:', error);
      return false;
    }
  }

  // Deletar todas as notificações do usuário
  async deleteAllNotifications(userId: string): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/notifications/user/${userId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete all notifications');
      
      const result = await response.json();
      this.notifyListeners();
      return result.deleted || 0;
    } catch (error) {
      console.error('Error deleting all notifications:', error);
      return 0;
    }
  }

  // Deletar múltiplas notificações
  async deleteBulkNotifications(notificationIds: string[], userId: string): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/notifications/bulk`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notificationIds, userId }),
      });

      if (!response.ok) throw new Error('Failed to delete bulk notifications');
      
      const result = await response.json();
      this.notifyListeners();
      return result.deleted || 0;
    } catch (error) {
      console.error('Error deleting bulk notifications:', error);
      return 0;
    }
  }

  // Métodos de conveniência para criar tipos específicos de notificações
  async createLikeNotification(
    fromUserId: string,
    toUserId: string,
    pinId: string,
    commentId?: string
  ): Promise<Notification | null> {
    const title = commentId ? 'New like on your comment' : 'New like on your pin';
    const content = commentId ? 'liked your comment' : 'liked your pin';
    const actionUrl = `/pin/${pinId}${commentId ? `#comment-${commentId}` : ''}`;

    return this.createNotification({
      userId: toUserId,
      type: 'like',
      fromUserId,
      title,
      content,
      pinId,
      commentId,
      actionUrl,
    });
  }

  async createCommentNotification(
    fromUserId: string,
    toUserId: string,
    pinId: string,
    commentId: string,
    commentText: string
  ): Promise<Notification | null> {
    const title = 'New comment on your pin';
    const content = `commented: "${commentText.substring(0, 50)}${commentText.length > 50 ? '...' : ''}"`;
    const actionUrl = `/pin/${pinId}#comment-${commentId}`;

    return this.createNotification({
      userId: toUserId,
      type: 'comment',
      fromUserId,
      title,
      content,
      pinId,
      commentId,
      actionUrl,
    });
  }

  async createFollowNotification(
    fromUserId: string,
    toUserId: string
  ): Promise<Notification | null> {
    const title = 'New follower';
    const content = 'started following you';
    const actionUrl = `/profile/${fromUserId}`;

    return this.createNotification({
      userId: toUserId,
      type: 'follow',
      fromUserId,
      title,
      content,
      actionUrl,
    });
  }

  async createMentionNotification(
    fromUserId: string,
    toUserId: string,
    pinId: string,
    commentId: string,
    mentionText: string
  ): Promise<Notification | null> {
    const title = 'You were mentioned';
    const content = `mentioned you: "${mentionText.substring(0, 50)}${mentionText.length > 50 ? '...' : ''}"`;
    const actionUrl = `/pin/${pinId}#comment-${commentId}`;

    return this.createNotification({
      userId: toUserId,
      type: 'mention',
      fromUserId,
      title,
      content,
      pinId,
      commentId,
      actionUrl,
    });
  }

  // Adicionar listener para mudanças
  addListener(callback: (notifications: Notification[]) => void): () => void {
    this.listeners.push(callback);
    return () => {
      this.listeners = this.listeners.filter(l => l !== callback);
    };
  }

  private notifyListeners(): void {
    // Notificar listeners que houve mudança (eles devem refetch os dados)
    this.listeners.forEach(listener => listener([]));
  }

  private showBrowserNotification(notification: Notification): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.content,
        icon: notification.fromUser.avatar || '/default-avatar.png',
        tag: notification.id
      });
    }
  }

  // Solicitar permissão para notificações
  async requestPermission(): Promise<boolean> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }

  // Extrair menções de texto (para uso em comentários)
  extractMentions(text: string): string[] {
    const mentionRegex = /@([a-zA-Z0-9_]+)/g;
    const mentions: string[] = [];
    let match;
    
    while ((match = mentionRegex.exec(text)) !== null) {
      mentions.push(match[1]);
    }
    
    return mentions;
  }
}

// Exportar instância singleton
export const notificationService = new NotificationService();
export default notificationService; 