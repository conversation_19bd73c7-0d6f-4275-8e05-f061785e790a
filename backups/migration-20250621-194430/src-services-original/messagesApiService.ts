// Serviço para APIs de mensagens com correções para fotos, nomes e operações de conversa
export interface MessageUser {
  id: string;
  username: string;
  displayName: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
}

export interface ConversationParticipant {
  id: string;
  username: string;
  displayName: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  lastReadAt?: string;
  isArchived: boolean;
  isMuted: boolean;
  isBlocked: boolean;
  joinedAt: string;
  leftAt?: string;
}

export interface LastMessage {
  id: string;
  content: string;
  contentType: string;
  timestamp: string;
  senderId: string;
  sender: MessageUser;
}

export interface ConversationWithParticipants {
  id: string;
  type: string;
  title?: string;
  lastMessageAt?: string;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
  unreadCount: number;
  participants: ConversationParticipant[];
  lastMessage?: LastMessage;
}

export interface MessageItem {
  id: string;
  content: string;
  contentType: string;
  attachmentUrl?: string;
  pinId?: string;
  tradeId?: string;
  status: string;
  isEdited: boolean;
  editedAt?: string;
  isDeleted: boolean;
  deletedAt?: string;
  createdAt: string;
  updatedAt: string;
  senderId: string;
  sender: MessageUser;
}

class MessagesApiService {
  private baseUrl = 'http://localhost:3001';

  // Buscar conversas do usuário com informações completas dos participantes
  async getUserConversations(userId: string, options?: { limit?: number; offset?: number }): Promise<ConversationWithParticipants[]> {
    try {
      const params = new URLSearchParams();
      if (options?.limit) params.append('limit', options.limit.toString());
      if (options?.offset) params.append('offset', options.offset.toString());
      
      const response = await fetch(`${this.baseUrl}/api/messages/conversations/user/${userId}?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get conversations');
      }
      
      const result = await response.json();
      return result.conversations;
    } catch (error) {
      console.error('Error getting user conversations:', error);
      throw error;
    }
  }

  // Buscar mensagens de uma conversa com informações completas do remetente
  async getConversationMessages(conversationId: string, options?: { limit?: number; offset?: number; before?: string }): Promise<MessageItem[]> {
    try {
      const params = new URLSearchParams();
      if (options?.limit) params.append('limit', options.limit.toString());
      if (options?.offset) params.append('offset', options.offset.toString());
      if (options?.before) params.append('before', options.before);
      
      const response = await fetch(`${this.baseUrl}/api/messages/conversations/${conversationId}/messages?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get messages');
      }
      
      const result = await response.json();
      return result.messages.map((msg: any) => ({
        id: msg.id,
        content: msg.content,
        contentType: msg.contentType,
        attachmentUrl: msg.attachmentUrl,
        pinId: msg.pinId,
        tradeId: msg.tradeId,
        status: msg.status,
        isEdited: msg.isEdited,
        editedAt: msg.editedAt,
        isDeleted: msg.isDeleted,
        deletedAt: msg.deletedAt,
        createdAt: msg.createdAt,
        updatedAt: msg.updatedAt,
        senderId: msg.senderId,
        sender: msg.sender
      }));
    } catch (error) {
      console.error('Error getting conversation messages:', error);
      throw error;
    }
  }

  // Arquivar conversa para o usuário
  async archiveConversation(conversationId: string, userId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/messages/conversations/${conversationId}/archive/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to archive conversation');
      }
    } catch (error) {
      console.error('Error archiving conversation:', error);
      throw error;
    }
  }

  // Desarquivar conversa para o usuário
  async unarchiveConversation(conversationId: string, userId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/messages/conversations/${conversationId}/unarchive/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to unarchive conversation');
      }
    } catch (error) {
      console.error('Error unarchiving conversation:', error);
      throw error;
    }
  }

  // Deletar conversa para o usuário (sair da conversa)
  async deleteConversation(conversationId: string, userId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/messages/conversations/${conversationId}/user/${userId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete conversation');
      }
    } catch (error) {
      console.error('Error deleting conversation:', error);
      throw error;
    }
  }

  // Deletar mensagem
  async deleteMessage(messageId: string, userId: string): Promise<{ deletedMessageId: string; conversationId: string; newLastMessage?: any }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/messages/${messageId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete message');
      }
      
      const result = await response.json();
      return {
        deletedMessageId: result.deletedMessageId,
        conversationId: result.conversationId,
        newLastMessage: result.newLastMessage
      };
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }

  // Enviar mensagem
  async sendMessage(data: {
    conversationId: string;
    senderId: string;
    content: string;
    contentType?: string;
    attachmentUrl?: string;
    pinId?: string;
    tradeId?: string;
  }): Promise<MessageItem> {
    try {
      const response = await fetch(`${this.baseUrl}/api/messages/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to send message');
      }
      
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Criar conversa
  async createConversation(data: {
    type?: string;
    title?: string;
    participants: string[];
  }): Promise<{ id: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/messages/conversations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create conversation');
      }
      
      const result = await response.json();
      return { id: result.id };
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  }

  // Buscar conversa por participantes
  async findConversationByParticipants(participants: string[]): Promise<ConversationWithParticipants | null> {
    try {
      const params = new URLSearchParams({
        participants: participants.join(',')
      });
      
      const response = await fetch(`${this.baseUrl}/api/messages/conversations/find?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.status === 404) {
        return null; // No conversation found
      }
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to find conversation');
      }
      
      const result = await response.json();
      return result.conversation;
    } catch (error) {
      console.error('Error finding conversation:', error);
      throw error;
    }
  }

  // Marcar conversa como lida
  async markConversationAsRead(conversationId: string, userId: string, lastMessageId?: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/messages/${conversationId}/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          lastMessageId
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to mark conversation as read');
      }
    } catch (error) {
      console.error('Error marking conversation as read:', error);
      throw error;
    }
  }

  // Buscar contagem de mensagens não lidas
  async getUnreadCount(userId: string): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/api/messages/unread-count/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get unread count');
      }
      
      const result = await response.json();
      return result.unreadCount || 0;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }
}

// Export singleton instance
export const messagesApiService = new MessagesApiService(); 