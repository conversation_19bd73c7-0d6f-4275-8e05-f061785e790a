import { getAnalytics, logEvent, setUserProperties, setUserId } from 'firebase/analytics';
import { auth } from './firebase';
import { User } from 'firebase/auth';

// Inicializar Firebase Analytics
let analytics: any = null;

// Verificar se estamos no browser e se Analytics está disponível
if (typeof window !== 'undefined') {
  try {
    analytics = getAnalytics();
    console.log('✅ Firebase Analytics initialized');
  } catch (error) {
    console.warn('⚠️ Firebase Analytics not available:', error);
  }
}

// Tipos para eventos customizados
export interface AuthEvent {
  event_name: string;
  user_id?: string;
  email?: string;
  provider?: string;
  verification_status?: boolean;
  user_role?: string;
  timestamp?: number;
}

export interface UserEngagementEvent {
  event_name: string;
  user_id?: string;
  page_location?: string;
  engagement_time_msec?: number;
  session_id?: string;
}

// Classe principal do serviço de Analytics
export class FirebaseAnalyticsService {
  private static instance: FirebaseAnalyticsService;

  public static getInstance(): FirebaseAnalyticsService {
    if (!FirebaseAnalyticsService.instance) {
      FirebaseAnalyticsService.instance = new FirebaseAnalyticsService();
    }
    return FirebaseAnalyticsService.instance;
  }

  // Verificar se Analytics está disponível
  private isAvailable(): boolean {
    return analytics !== null && typeof window !== 'undefined';
  }

  // Configurar usuário no Analytics
  async setUser(user: User | null) {
    if (!this.isAvailable() || !user) return;

    try {
      setUserId(analytics, user.uid);
      setUserProperties(analytics, {
        email_verified: user.emailVerified,
        provider: user.providerData[0]?.providerId || 'unknown',
        creation_time: user.metadata.creationTime,
        last_sign_in: user.metadata.lastSignInTime
      });
      
      console.log('📊 Analytics user configured:', user.uid);
    } catch (error) {
      console.error('❌ Error setting Analytics user:', error);
    }
  }

  // Eventos de autenticação
  async trackAuthEvent(eventData: AuthEvent) {
    if (!this.isAvailable()) return;

    try {
      logEvent(analytics, eventData.event_name, {
        user_id: eventData.user_id,
        email: eventData.email,
        provider: eventData.provider,
        verification_status: eventData.verification_status,
        user_role: eventData.user_role,
        timestamp: eventData.timestamp || Date.now()
      });
      
      console.log('📊 Auth event tracked:', eventData.event_name);
    } catch (error) {
      console.error('❌ Error tracking auth event:', error);
    }
  }

  // Eventos de engajamento
  async trackEngagement(eventData: UserEngagementEvent) {
    if (!this.isAvailable()) return;

    try {
      logEvent(analytics, eventData.event_name, {
        user_id: eventData.user_id,
        page_location: eventData.page_location,
        engagement_time_msec: eventData.engagement_time_msec,
        session_id: eventData.session_id
      });
    } catch (error) {
      console.error('❌ Error tracking engagement:', error);
    }
  }

  // Eventos específicos para administração
  async trackAdminAction(action: string, details: Record<string, any> = {}) {
    if (!this.isAvailable()) return;

    const currentUser = auth.currentUser;
    if (!currentUser) return;

    try {
      logEvent(analytics, 'admin_action', {
        action,
        admin_user_id: currentUser.uid,
        admin_email: currentUser.email,
        timestamp: Date.now(),
        ...details
      });
      
      console.log('📊 Admin action tracked:', action);
    } catch (error) {
      console.error('❌ Error tracking admin action:', error);
    }
  }

  // Eventos de sincronização Auth/Firestore
  async trackSyncEvent(syncType: string, results: Record<string, any>) {
    await this.trackAdminAction('auth_sync', {
      sync_type: syncType,
      ...results
    });
  }

  // Eventos de operações em lote
  async trackBulkOperation(operation: string, userCount: number, success: boolean) {
    await this.trackAdminAction('bulk_operation', {
      operation,
      user_count: userCount,
      success,
      timestamp: Date.now()
    });
  }

  // Métricas de performance da área admin
  async trackAdminPageView(pageName: string, loadTime?: number) {
    if (!this.isAvailable()) return;

    const currentUser = auth.currentUser;
    if (!currentUser) return;

    try {
      logEvent(analytics, 'page_view', {
        page_title: `Admin - ${pageName}`,
        page_location: window.location.href,
        user_id: currentUser.uid,
        load_time: loadTime,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('❌ Error tracking admin page view:', error);
    }
  }

  // Eventos customizados para estatísticas
  async trackCustomEvent(eventName: string, parameters: Record<string, any> = {}) {
    if (!this.isAvailable()) return;

    try {
      logEvent(analytics, eventName, {
        timestamp: Date.now(),
        ...parameters
      });
    } catch (error) {
      console.error('❌ Error tracking custom event:', error);
    }
  }
}

// Instância singleton
export const analyticsService = FirebaseAnalyticsService.getInstance();

// Hooks para facilitar o uso
export const useFirebaseAnalytics = () => {
  return {
    trackAuthEvent: (eventData: AuthEvent) => analyticsService.trackAuthEvent(eventData),
    trackEngagement: (eventData: UserEngagementEvent) => analyticsService.trackEngagement(eventData),
    trackAdminAction: (action: string, details?: Record<string, any>) => 
      analyticsService.trackAdminAction(action, details),
    trackSyncEvent: (syncType: string, results: Record<string, any>) => 
      analyticsService.trackSyncEvent(syncType, results),
    trackBulkOperation: (operation: string, userCount: number, success: boolean) => 
      analyticsService.trackBulkOperation(operation, userCount, success),
    trackPageView: (pageName: string, loadTime?: number) => 
      analyticsService.trackAdminPageView(pageName, loadTime),
    trackCustomEvent: (eventName: string, parameters?: Record<string, any>) => 
      analyticsService.trackCustomEvent(eventName, parameters),
    setUser: (user: User | null) => analyticsService.setUser(user)
  };
};

// Eventos pré-definidos para facilitar o uso
export const AUTH_EVENTS = {
  USER_LOGIN: 'user_login',
  USER_LOGOUT: 'user_logout',
  USER_SIGNUP: 'user_signup',
  EMAIL_VERIFICATION_SENT: 'email_verification_sent',
  EMAIL_VERIFIED: 'email_verified',
  PASSWORD_RESET_SENT: 'password_reset_sent',
  USER_DISABLED: 'user_disabled',
  USER_ENABLED: 'user_enabled',
  USER_DELETED: 'user_deleted'
} as const;

export const ADMIN_EVENTS = {
  AUTH_SYNC_STARTED: 'auth_sync_started',
  AUTH_SYNC_COMPLETED: 'auth_sync_completed',
  BULK_EMAIL_VERIFICATION: 'bulk_email_verification',
  BULK_PASSWORD_RESET: 'bulk_password_reset',
  USER_MANAGEMENT_VIEW: 'user_management_view',
  STATISTICS_VIEW: 'statistics_view'
} as const; 