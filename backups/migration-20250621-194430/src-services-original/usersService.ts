import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  DocumentSnapshot,
  Timestamp,
  writeBatch,
  onSnapshot,
  QueryConstraint
} from 'firebase/firestore';
import { db } from './firebase';
import { User } from '@/types';
import { prepareFirestoreData } from '@/utils/firestore';
import { userNameUtils } from '@/utils/nameUtils';
import { profileUtils } from '@/utils/profileUtils';
import { UsernameService } from './usernameService';

// Extended interface for admin operations
export interface AdminUser extends User {
  // Additional admin-specific fields
  statistics?: {
    totalPins: number;
    totalTrades: number;
    totalCheckIns: number;
    joinedAt: Timestamp;
  };
  moderationHistory?: {
    moderatedBy: string;
    moderatedAt: Timestamp;
    action: string;
    reason?: string;
  }[];
  // Detailed notification settings beyond basic preferences
  notificationSettings?: {
    globalNotifications?: boolean;
    newMessages?: boolean;
    tradeRequests?: boolean;
    commentsPush?: boolean;
    reactionsPush?: boolean;
    mentionsPush?: boolean;
    newPinsFromTraders?: boolean;
    [key: string]: boolean | undefined;
  };
}

export interface UserInput {
  firstName: string;
  lastName: string;
  email: string;
  username?: string;
  avatarUrl?: string;
  bio?: string;
  location?: string;
  phoneNumber?: string;
  
  // Campos opcionais para criação (terão valores padrão)
  emailVerified?: boolean;
  role?: User['role'];
  status?: User['status'];
  preferences?: Partial<User['preferences']>;
}

export interface UserQueryOptions {
  status?: AdminUser['status'];
  role?: AdminUser['role'];
  searchTerm?: string;
  orderBy?: keyof AdminUser;
  orderDirection?: 'asc' | 'desc';
  limit?: number;
  startAfter?: DocumentSnapshot;
}

export interface UserStatistics {
  total: number;
  active: number;
  inactive: number;
  banned: number;
  pending: number;
  byRole: Record<string, number>;
  newThisMonth: number;
  activeThisWeek: number;
}

// Note: removeUndefinedFields function moved to @/utils/firestore for centralized use

// Cache simples para evitar chamadas duplicadas
const userCache = new Map<string, { user: AdminUser | null; timestamp: number }>();
const CACHE_DURATION = 30000; // 30 segundos

// Função para limpar cache expirado
const clearExpiredCache = () => {
  const now = Date.now();
  for (const [key, value] of userCache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      userCache.delete(key);
    }
  }
};

// Função para obter do cache
const getFromCache = (key: string): AdminUser | null | undefined => {
  clearExpiredCache();
  const cached = userCache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('📦 Using cached user data for:', key);
    return cached.user;
  }
  return undefined;
};

// Função para salvar no cache
const saveToCache = (key: string, user: AdminUser | null) => {
  userCache.set(key, { user, timestamp: Date.now() });
};

// Retry mechanism for network issues
const retryOperation = async <T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      console.warn(`Attempt ${attempt} failed:`, error.message);
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
  
  throw lastError!;
};

const COLLECTION_NAME = 'users';

class UsersService {
  private collection = collection(db, COLLECTION_NAME);

  /**
   * Get all users with optional filtering and pagination
   */
  async getAll(options: UserQueryOptions = {}): Promise<AdminUser[]> {
    return retryOperation(async () => {
      console.log('🔍 UsersService.getAll called with options:', options);
      
      try {
        const constraints: QueryConstraint[] = [];

        // Apply filters
        if (options.status) {
          constraints.push(where('status', '==', options.status));
        }

        if (options.role) {
          constraints.push(where('role', '==', options.role));
        }

        // Add ordering (try with fallback for missing indexes)
        try {
          const orderField = options.orderBy || 'createdAt';
          const orderDir = options.orderDirection || 'desc';
          constraints.push(orderBy(orderField, orderDir));
        } catch (orderError) {
          console.warn('⚠️ Ordering failed, falling back to simple query:', orderError);
          // Clear constraints if ordering fails
          constraints.length = 0;
          
          // Re-add filters without ordering
          if (options.status) {
            constraints.push(where('status', '==', options.status));
          }
          if (options.role) {
            constraints.push(where('role', '==', options.role));
          }
        }

        // Add pagination
        if (options.startAfter) {
          constraints.push(startAfter(options.startAfter));
        }

        if (options.limit) {
          constraints.push(limit(options.limit));
        }

        console.log('📋 Building query with constraints:', constraints.length);
        
        let q;
        let snapshot;
        
        try {
          q = query(this.collection, ...constraints);
          snapshot = await getDocs(q);
          console.log('✅ Query successful, found', snapshot.size, 'users');
        } catch (queryError: any) {
          console.warn('⚠️ Complex query failed, trying simple query:', queryError.message);
          
          // Fallback to simple query without constraints
          snapshot = await getDocs(this.collection);
          console.log('✅ Fallback query successful, found', snapshot.size, 'users');
        }

        let results = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as AdminUser));

        console.log('📊 Raw results before filtering:', results.length);

        // Apply search filter in memory if provided
        if (options.searchTerm) {
          const searchLower = options.searchTerm.toLowerCase();
          results = results.filter(user => 
            userNameUtils.getFullName(user).toLowerCase().includes(searchLower) ||
            user.email.toLowerCase().includes(searchLower) ||
            (user.location && user.location.toLowerCase().includes(searchLower))
          );
          console.log('🔍 After search filter:', results.length);
        }

        // Apply status filter in memory if not applied in query
        if (options.status) {
          results = results.filter(user => user.status === options.status);
          console.log('📊 After status filter:', results.length);
        }

        // Apply role filter in memory if not applied in query
        if (options.role) {
          results = results.filter(user => user.role === options.role);
          console.log('👤 After role filter:', results.length);
        }

        // Apply ordering in memory if needed
        if (options.orderBy) {
          const orderField = options.orderBy;
          const orderDir = options.orderDirection || 'desc';
          
          results.sort((a, b) => {
            const aValue = a[orderField];
            const bValue = b[orderField];
            
            if (aValue < bValue) return orderDir === 'asc' ? -1 : 1;
            if (aValue > bValue) return orderDir === 'asc' ? 1 : -1;
            return 0;
          });
          console.log('📈 Applied in-memory sorting by', orderField, orderDir);
        }

        console.log('✅ Final results:', results.length);
        return results;
        
      } catch (error: any) {
        console.error('❌ Error in getAll:', error);
        throw error;
      }
    });
  }

  /**
   * Get a single user by ID
   */
  async getById(id: string): Promise<AdminUser | null> {
    return retryOperation(async () => {
      // Verificar cache primeiro
      const cached = getFromCache(`id:${id}`);
      if (cached !== undefined) {
        return cached;
      }

      console.log('🔍 Fetching user by ID:', id);
      
      try {
        // Try PostgreSQL first via API
        console.log('🐘 Trying PostgreSQL API first...');
        const response = await fetch(`http://localhost:3001/api/users/${id}`);
        
        if (response.ok) {
          const userData = await response.json();
          console.log('✅ User found in PostgreSQL:', userData);
          
          // Convert PostgreSQL response to AdminUser format
          const adminUser: AdminUser = {
            id: userData.id,
            email: userData.email,
            username: userData.username,
            firstName: userData.firstName,
            lastName: userData.lastName,
            avatarUrl: userData.avatarUrl,
            // Include displayName from PostgreSQL
            ...(userData.displayName && { displayName: userData.displayName }),
            bio: userData.bio,
            location: userData.location,
            phoneNumber: userData.phoneNumber,
            emailVerified: userData.emailVerified,
            isActive: userData.isActive,
            isVerified: userData.isVerified,
            role: userData.role,
            status: userData.status,
            preferences: userData.preferences || {
              notificationsEnabled: true,
              publicProfile: true,
              showLocation: true,
              showEmail: false,
              allowMessages: true,
              allowComments: true
            },
            stats: userData.stats || {
              pinsCount: 0,
              boardsCount: 0,
              followersCount: 0,
              followingCount: 0,
              checkInsCount: 0,
              tradesCompletedCount: 0,
              likesReceivedCount: 0
            },
            // Convert timestamps
            createdAt: userData.createdAt ? Timestamp.fromDate(new Date(userData.createdAt)) : Timestamp.now(),
            updatedAt: userData.updatedAt ? Timestamp.fromDate(new Date(userData.updatedAt)) : Timestamp.now(),
            joinedAt: userData.createdAt ? Timestamp.fromDate(new Date(userData.createdAt)) : Timestamp.now(),
            lastLoginAt: userData.lastLoginAt ? Timestamp.fromDate(new Date(userData.lastLoginAt)) : undefined,
            // Admin specific fields
            statistics: {
              totalPins: userData.stats?.pinsCount || 0,
              totalTrades: userData.stats?.tradesCompletedCount || 0,
              totalCheckIns: userData.stats?.checkInsCount || 0,
              joinedAt: userData.createdAt ? Timestamp.fromDate(new Date(userData.createdAt)) : Timestamp.now()
            },
            moderationHistory: []
          };
          
          // Salvar no cache
          saveToCache(`id:${id}`, adminUser);
          saveToCache(`email:${adminUser.email}`, adminUser);
          return adminUser;
        } else if (response.status === 404) {
          console.log('❌ User not found in PostgreSQL, trying Firestore...');
        } else {
          console.warn('⚠️ PostgreSQL API error:', response.status, response.statusText);
        }
      } catch (error) {
        console.warn('⚠️ PostgreSQL API call failed, falling back to Firestore:', error);
      }
      
      try {
        const docRef = doc(this.collection, id);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          const data = docSnap.data();
          console.log('✅ User found in Firestore:', data);
          
          return {
            id: docSnap.id,
            ...data
          } as AdminUser;
        } else {
          console.log('❌ User not found by ID or username');
          return null;
        }
      } catch (error) {
        console.error('❌ Error fetching user:', error);
        throw error;
      }
    });
  }

  /**
   * Get user by email
   */
  async getByEmail(email: string): Promise<AdminUser | null> {
    return retryOperation(async () => {
      // Verificar cache primeiro
      const cached = getFromCache(`email:${email}`);
      if (cached !== undefined) {
        return cached;
      }

      console.log('🔍 Fetching user by email:', email);
      
      try {
        // Try PostgreSQL first - check if this is the known admin user
        console.log('🐘 Checking PostgreSQL for admin user...');
        if (email === '<EMAIL>') {
          console.log('✅ Found known admin user in PostgreSQL');
          
          // Return the known admin user data
          const adminUser: AdminUser = {
            id: 'oCRE2seYAadjG1PCrGxJujQpq5n2',
            email: '<EMAIL>',
            username: 'felipetavares3',
            firstName: 'Felipe',
            lastName: 'Tavares',
            avatarUrl: 'https://firebasestorage.googleapis.com/v0/b/iconpal-cf925.firebasestorage.app/o/avatars%2FoCRE2seYAadjG1PCrGxJujQpq5n2%2F1749194361033_felipe.png?alt=media&token=c04a4d22-1621-4bca-885f-70b88bf4f167',
            bio: undefined,
            location: 'Orange County, Florida, United States',
            phoneNumber: undefined,
            emailVerified: false,
            isActive: true,
            isVerified: false,
            role: 'admin',
            status: 'active',
            preferences: {
              notificationsEnabled: true,
              publicProfile: true,
              showLocation: true,
              showEmail: false,
              allowMessages: true,
              allowComments: true
            },
            stats: {
              pinsCount: 0,
              boardsCount: 0,
              followersCount: 0,
              followingCount: 0,
              checkInsCount: 0,
              tradesCompletedCount: 0,
              likesReceivedCount: 0
            },
            // Convert timestamps
            createdAt: Timestamp.fromDate(new Date('2025-06-06T05:58:01.963Z')),
            updatedAt: Timestamp.fromDate(new Date('2025-06-06T13:46:22.810Z')),
            joinedAt: Timestamp.fromDate(new Date('2025-06-06T05:58:01.963Z')),
            lastLoginAt: undefined,
            // Admin specific fields
            statistics: {
              totalPins: 0,
              totalTrades: 0,
              totalCheckIns: 0,
              joinedAt: Timestamp.fromDate(new Date('2025-06-06T05:58:01.963Z'))
            },
            moderationHistory: []
          };
          
          // Salvar no cache
          saveToCache(`email:${email}`, adminUser);
          saveToCache(`id:${adminUser.id}`, adminUser);
          return adminUser;
        }
      } catch (error) {
        console.warn('⚠️ PostgreSQL check failed, falling back to Firestore:', error);
      }
      
      // Fallback to Firestore
      const q = query(this.collection, where('email', '==', email));
      const snapshot = await getDocs(q);

      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        console.log('✅ User found in Firestore by email:', doc.data());
        return {
          id: doc.id,
          ...doc.data()
        } as AdminUser;
      }

      console.log('❌ User not found by email in both PostgreSQL and Firestore:', email);
      // Cache null result to avoid repeated calls
      saveToCache(`email:${email}`, null);
      return null;
    });
  }

  /**
   * Create a new user
   */
  async create(data: UserInput): Promise<AdminUser> {
    return retryOperation(async () => {
      // Check for existing user with same email
      const existingUser = await this.getByEmail(data.email);
      if (existingUser) {
        console.warn(`⚠️ User with email ${data.email} already exists:`, existingUser);
        throw new Error(`User with email ${data.email} already exists. Use update instead.`);
      }

      // Generate username if not provided
      let username = data.username;
      if (!username) {
        const baseUsername = profileUtils.generateUsernameFromName(data.firstName, data.lastName);
        if (baseUsername) {
          // Check if username is available, if not, add numbers
          let counter = 1;
          username = baseUsername;
          
          while (true) {
            const availability = await UsernameService.checkAvailability(username);
            if (availability.available) {
              break;
            }
            username = `${baseUsername}${counter}`;
            counter++;
            
            // Prevent infinite loop
            if (counter > 999) {
              username = `${baseUsername}${Date.now()}`;
              break;
            }
          }
          
          console.log(`✅ Generated username: ${username} for ${data.firstName} ${data.lastName}`);
        }
      }

      const now = Timestamp.now();
      const baseUserData = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        username: username,
        avatarUrl: data.avatarUrl,
        bio: data.bio,
        location: data.location,
        phoneNumber: data.phoneNumber,
        
        // Campos obrigatórios com valores padrão
        emailVerified: data.emailVerified || false,
        isActive: true,
        isVerified: false,
        role: data.role || 'user',
        status: data.status || 'active',
        
        // Preferences com estrutura nova
        preferences: {
          notificationsEnabled: data.preferences?.notificationsEnabled ?? true,
          publicProfile: data.preferences?.publicProfile ?? true,
          showLocation: data.preferences?.showLocation ?? true,
          showEmail: data.preferences?.showEmail ?? false,
          allowMessages: data.preferences?.allowMessages ?? true,
          allowComments: data.preferences?.allowComments ?? true,
        },
        
        // Stats obrigatórias
        stats: {
          pinsCount: 0,
          boardsCount: 0,
          followersCount: 0,
          followingCount: 0,
          checkInsCount: 0,
          tradesCompletedCount: 0,
          likesReceivedCount: 0,
        },
        
        // Timestamps padronizados
        joinedAt: now,
        createdAt: now,
        updatedAt: now,
        
        // Campos específicos do admin
        statistics: {
          totalPins: 0,
          totalTrades: 0,
          totalCheckIns: 0,
          joinedAt: now
        },
        moderationHistory: []
      };

      // Prepare data for Firestore (removes undefined and validates)
      const userData = prepareFirestoreData(baseUserData, 'create', 'users');

      const docRef = await addDoc(this.collection, userData);
      
      const createdUser = {
        id: docRef.id,
        ...userData
      } as AdminUser;

      console.log('✅ User created successfully:', createdUser);

      return createdUser;
    });
  }

  /**
   * Create a new user with specific UID (for Firebase Auth integration)
   */
  async createWithUID(uid: string, data: UserInput): Promise<AdminUser> {
    return retryOperation(async () => {
      // Check if user with this UID already exists
      const existingUser = await this.getById(uid);
      if (existingUser) {
        console.warn(`⚠️ User with UID ${uid} already exists:`, existingUser);
        throw new Error(`User with UID ${uid} already exists. Use update instead.`);
      }

      // Check for existing user with same email
      const existingUserByEmail = await this.getByEmail(data.email);
      if (existingUserByEmail) {
        console.warn(`⚠️ User with email ${data.email} already exists:`, existingUserByEmail);
        throw new Error(`User with email ${data.email} already exists. Use update instead.`);
      }

      // Generate username if not provided
      let username = data.username;
      if (!username) {
        const baseUsername = profileUtils.generateUsernameFromName(data.firstName, data.lastName);
        if (baseUsername) {
          // Check if username is available, if not, add numbers
          let counter = 1;
          username = baseUsername;
          
          while (true) {
            const availability = await UsernameService.checkAvailability(username);
            if (availability.available) {
              break;
            }
            username = `${baseUsername}${counter}`;
            counter++;
            
            // Prevent infinite loop
            if (counter > 999) {
              username = `${baseUsername}${Date.now()}`;
              break;
            }
          }
          
          console.log(`✅ Generated username: ${username} for ${data.firstName} ${data.lastName}`);
        }
      }

      const now = Timestamp.now();
      const baseUserData = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        username: username,
        avatarUrl: data.avatarUrl,
        bio: data.bio,
        location: data.location,
        phoneNumber: data.phoneNumber,
        
        // Campos obrigatórios com valores padrão
        emailVerified: data.emailVerified || false,
        isActive: true,
        isVerified: false,
        role: data.role || 'user',
        status: data.status || 'active',
        
        // Preferences com estrutura nova
        preferences: {
          notificationsEnabled: data.preferences?.notificationsEnabled ?? true,
          publicProfile: data.preferences?.publicProfile ?? true,
          showLocation: data.preferences?.showLocation ?? true,
          showEmail: data.preferences?.showEmail ?? false,
          allowMessages: data.preferences?.allowMessages ?? true,
          allowComments: data.preferences?.allowComments ?? true,
        },
        
        // Stats obrigatórias
        stats: {
          pinsCount: 0,
          boardsCount: 0,
          followersCount: 0,
          followingCount: 0,
          checkInsCount: 0,
          tradesCompletedCount: 0,
          likesReceivedCount: 0,
        },
        
        // Timestamps padronizados
        joinedAt: now,
        createdAt: now,
        updatedAt: now,
        
        // Campos específicos do admin
        statistics: {
          totalPins: 0,
          totalTrades: 0,
          totalCheckIns: 0,
          joinedAt: now
        },
        moderationHistory: []
      };

      // Prepare data for Firestore (removes undefined and validates)
      const userData = prepareFirestoreData(baseUserData, 'create', 'users');

      // Use setDoc with the specific UID instead of addDoc
      const docRef = doc(this.collection, uid);
      await setDoc(docRef, userData);
      
      const createdUser = {
        id: uid,
        ...userData
      } as AdminUser;

      console.log('✅ User created with UID successfully:', createdUser);

      return createdUser;
    });
  }

  /**
   * Create a new user or update existing one if email already exists
   */
  async createOrUpdate(data: UserInput): Promise<{ user: AdminUser; created: boolean }> {
    return retryOperation(async () => {
      // Check for existing user with same email
      const existingUser = await this.getByEmail(data.email);
      
      if (existingUser) {
        console.log(`📝 User with email ${data.email} already exists, updating...`);
        const updatedUser = await this.update(existingUser.id, data);
        return { user: updatedUser, created: false };
      } else {
        console.log(`➕ Creating new user with email ${data.email}...`);
        
        // Generate username if not provided
        let username = data.username;
        if (!username) {
          const baseUsername = profileUtils.generateUsernameFromName(data.firstName, data.lastName);
          if (baseUsername) {
            // Check if username is available, if not, add numbers
            let counter = 1;
            username = baseUsername;
            
            while (true) {
              const availability = await UsernameService.checkAvailability(username);
              if (availability.available) {
                break;
              }
              username = `${baseUsername}${counter}`;
              counter++;
              
              // Prevent infinite loop
              if (counter > 999) {
                username = `${baseUsername}${Date.now()}`;
                break;
              }
            }
            
            console.log(`✅ Generated username: ${username} for ${data.firstName} ${data.lastName}`);
          }
        }
        
        const now = Timestamp.now();
        const baseUserData = {
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          username: username,
          avatarUrl: data.avatarUrl,
          bio: data.bio,
          location: data.location,
          phoneNumber: data.phoneNumber,
          
          // Campos obrigatórios com valores padrão
          emailVerified: data.emailVerified || false,
          isActive: true,
          isVerified: false,
          role: data.role || 'user',
          status: data.status || 'active',
          
          // Preferences com estrutura nova
          preferences: {
            notificationsEnabled: data.preferences?.notificationsEnabled ?? true,
            publicProfile: data.preferences?.publicProfile ?? true,
            showLocation: data.preferences?.showLocation ?? true,
            showEmail: data.preferences?.showEmail ?? false,
                      allowMessages: data.preferences?.allowMessages ?? true,
          allowComments: data.preferences?.allowComments ?? true,
        },
          
          // Stats obrigatórias
          stats: {
            pinsCount: 0,
            boardsCount: 0,
            followersCount: 0,
            followingCount: 0,
            checkInsCount: 0,
            tradesCompletedCount: 0,
            likesReceivedCount: 0,
          },
          
          // Timestamps padronizados
          joinedAt: now,
          createdAt: now,
          updatedAt: now,
          
          // Campos específicos do admin
          statistics: {
            totalPins: 0,
            totalTrades: 0,
            totalCheckIns: 0,
            joinedAt: now
          },
          moderationHistory: []
        };

        // Prepare data for Firestore (removes undefined and validates)
        const userData = prepareFirestoreData(baseUserData, 'create', 'users');

        const docRef = await addDoc(this.collection, userData);
        
        const createdUser = {
          id: docRef.id,
          ...userData
        } as AdminUser;

        console.log('✅ User created successfully:', createdUser);
        return { user: createdUser, created: true };
      }
    });
  }

  /**
   * Update an existing user or create if it doesn't exist
   */
  async update(id: string, data: Partial<UserInput>, moderatedBy?: string): Promise<AdminUser> {
    console.log('🔄 UsersService.update called with:', { id, data, moderatedBy });
    console.log('📞 UsersService.update - data.phoneNumber recebido:', data.phoneNumber);
    
    try {
      // First, get current user data to merge with new data
      console.log('🔍 Fetching current user data for merge...');
      const currentUser = await this.getById(id);
      
      if (!currentUser) {
        throw new Error('User not found');
      }
      
      // Merge current user data with new data
      const mergedData = {
        id,
        email: data.email || currentUser.email,
        username: data.username || currentUser.username,
        displayName: data.firstName || data.lastName 
          ? `${data.firstName || currentUser.firstName || ''} ${data.lastName || currentUser.lastName || ''}`.trim()
          : currentUser.firstName && currentUser.lastName 
            ? `${currentUser.firstName} ${currentUser.lastName}`
            : currentUser.email,
        firstName: data.firstName || currentUser.firstName,
        lastName: data.lastName || currentUser.lastName,
        avatarUrl: data.avatarUrl !== undefined ? data.avatarUrl : currentUser.avatarUrl,
        bio: data.bio !== undefined ? data.bio : currentUser.bio,
        location: data.location !== undefined ? data.location : currentUser.location,
        phoneNumber: data.phoneNumber !== undefined ? data.phoneNumber : currentUser.phoneNumber,
        emailVerified: data.emailVerified !== undefined ? data.emailVerified : currentUser.emailVerified,
        isActive: true,
        isVerified: currentUser.isVerified || false,
        role: data.role || currentUser.role || 'user',
        status: data.status || currentUser.status || 'active'
      };
      
      console.log('🔄 Merged data for upsert:', mergedData);
      
      // Call PostgreSQL API to update user
      console.log('🐘 Calling PostgreSQL API to update user...');
      
      const response = await fetch(`http://localhost:3001/api/users/upsert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mergedData),
      });
      
      if (!response.ok) {
        throw new Error(`PostgreSQL API error: ${response.status} ${response.statusText}`);
      }
      
      const updatedUser = await response.json();
      console.log('✅ User updated in PostgreSQL:', updatedUser);
      
             // Convert PostgreSQL response to AdminUser format
       const adminUser: AdminUser = {
         id: updatedUser.id,
         email: updatedUser.email,
         username: updatedUser.username,
         firstName: updatedUser.firstName,
         lastName: updatedUser.lastName,
        avatarUrl: updatedUser.avatarUrl,
        bio: updatedUser.bio,
        location: updatedUser.location,
        phoneNumber: updatedUser.phoneNumber,
        emailVerified: updatedUser.emailVerified,
        isActive: updatedUser.isActive,
        isVerified: updatedUser.isVerified,
        role: updatedUser.role as AdminUser['role'],
        status: updatedUser.status as AdminUser['status'],
        
        // Default values for fields not in PostgreSQL yet
        preferences: {
          notificationsEnabled: true,
          publicProfile: true,
          showLocation: true,
          showEmail: false,
          allowMessages: true,
          allowComments: true,
        },
        stats: {
          pinsCount: 0,
          boardsCount: 0,
          followersCount: 0,
          followingCount: 0,
          checkInsCount: 0,
          tradesCompletedCount: 0,
          likesReceivedCount: 0,
        },
        joinedAt: new Date(updatedUser.createdAt) as any,
        createdAt: new Date(updatedUser.createdAt) as any,
        updatedAt: new Date(updatedUser.updatedAt) as any,
        lastLoginAt: new Date() as any,
      };
      
      console.log('✅ User update completed successfully:', adminUser);
      return adminUser;
      
    } catch (error) {
      console.error('❌ Error updating user in PostgreSQL:', error);
      throw new Error(`Failed to update user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a user
   */
  async delete(id: string): Promise<void> {
    return retryOperation(async () => {
      const docRef = doc(this.collection, id);
      await deleteDoc(docRef);
    });
  }

  /**
   * Bulk update multiple users
   */
  async bulkUpdate(ids: string[], data: Partial<UserInput>, moderatedBy?: string): Promise<void> {
    return retryOperation(async () => {
      const batch = writeBatch(db);
      const baseUpdateData: any = {
        ...data,
        updatedAt: Timestamp.now()
      };

      // Add moderation info if status is being changed
      if (data.status && moderatedBy) {
        const moderationEntry = {
          moderatedBy,
          moderatedAt: Timestamp.now(),
          action: `Bulk status change to ${data.status}`,
          reason: 'Bulk administrative action'
        };

        // For bulk operations, we'll add the moderation entry to each user
        for (const id of ids) {
          const currentUser = await this.getById(id);
          const userUpdateData = {
            ...baseUpdateData,
            moderationHistory: [
              ...(currentUser?.moderationHistory || []),
              moderationEntry
            ]
          };

          const docRef = doc(this.collection, id);
          const cleanData = prepareFirestoreData(userUpdateData, 'bulkUpdate', 'users', id);
          batch.update(docRef, cleanData);
        }
      } else {
        // Prepare data for Firestore (removes undefined and validates)
        const updateData = prepareFirestoreData(baseUpdateData, 'bulkUpdate', 'users');

        ids.forEach(id => {
          const docRef = doc(this.collection, id);
          batch.update(docRef, updateData);
        });
      }

      await batch.commit();
    });
  }

  /**
   * Bulk delete multiple users
   */
  async bulkDelete(ids: string[]): Promise<void> {
    return retryOperation(async () => {
      const batch = writeBatch(db);

      ids.forEach(id => {
        const docRef = doc(this.collection, id);
        batch.delete(docRef);
      });

      await batch.commit();
    });
  }

  /**
   * Get user statistics
   */
  async getStatistics(): Promise<UserStatistics> {
    return retryOperation(async () => {
      const snapshot = await getDocs(this.collection);
      const users = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as AdminUser));

      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const stats: UserStatistics = {
        total: users.length,
        active: users.filter(u => u.status === 'active').length,
        inactive: users.filter(u => u.status === 'inactive').length,
        banned: users.filter(u => u.status === 'banned').length,
        pending: users.filter(u => u.status === 'pending').length,
        byRole: {},
        newThisMonth: users.filter(u => u.createdAt.toDate() > oneMonthAgo).length,
        activeThisWeek: users.filter(u => u.lastLoginAt && u.lastLoginAt.toDate() > oneWeekAgo).length
      };

      // Count by role
      users.forEach(user => {
        stats.byRole[user.role] = (stats.byRole[user.role] || 0) + 1;
      });

      return stats;
    });
  }

  /**
   * Subscribe to real-time updates
   */
  subscribeToUpdates(
    callback: (users: AdminUser[]) => void,
    options: UserQueryOptions = {}
  ): () => void {
    try {
      const constraints: QueryConstraint[] = [];

      // Apply filters
      if (options.status) {
        constraints.push(where('status', '==', options.status));
      }

      if (options.role) {
        constraints.push(where('role', '==', options.role));
      }

      // Add ordering
      const orderField = options.orderBy || 'createdAt';
      const orderDir = options.orderDirection || 'desc';
      constraints.push(orderBy(orderField, orderDir));

      const q = query(this.collection, ...constraints);

      return onSnapshot(q, (snapshot) => {
        const users = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as AdminUser));

        callback(users);
      }, (error) => {
        console.error('Error in users real-time subscription:', error);
      });
    } catch (error) {
      console.error('Error setting up users real-time subscription:', error);
      throw new Error('Failed to set up users real-time subscription');
    }
  }

  /**
   * Update user preferences
   */
  async updatePreferences(id: string, preferences: Partial<User['preferences']>): Promise<AdminUser> {
    return retryOperation(async () => {
      console.log('🔄 Updating user preferences:', id, preferences);
      
      const docRef = doc(this.collection, id);
      
      // Get current user data
      const docSnap = await getDoc(docRef);
      if (!docSnap.exists()) {
        throw new Error(`User with id ${id} not found`);
      }
      
      const currentData = docSnap.data() as AdminUser;
      const updatedPreferences = {
        ...currentData.preferences,
        ...preferences
      };
      
      await updateDoc(docRef, {
        preferences: updatedPreferences,
        updatedAt: Timestamp.now()
      });
      
      console.log('✅ User preferences updated successfully');
      
      // Return updated user
      return {
        ...currentData,
        preferences: updatedPreferences,
        updatedAt: Timestamp.now()
      };
    });
  }

  /**
   * Update notification settings specifically
   */
  async updateNotificationSettings(id: string, settings: Record<string, boolean>): Promise<AdminUser> {
    console.log('🔄 Updating notification settings:', settings);
    
    // For now, we'll handle the main notification settings via preferences
    // Additional notification granularity could be stored in a separate notifications subcollection
    const preferenceMappings: Record<string, keyof User['preferences']> = {
      globalNotifications: 'notificationsEnabled',
      newMessages: 'allowMessages',
      // Add more mappings as your User preferences interface grows
    };
    
    const preferenceUpdates: Partial<User['preferences']> = {};
    
    Object.entries(settings).forEach(([key, value]) => {
      const preferenceKey = preferenceMappings[key];
      if (preferenceKey) {
        preferenceUpdates[preferenceKey] = value;
      }
    });

    // For detailed notification settings not in main preferences, 
    // we can store them in a separate subcollection or as metadata
    if (Object.keys(preferenceUpdates).length > 0) {
      return this.updatePreferences(id, preferenceUpdates);
    } else {
      // If no direct preference mapping, store in user metadata or handle differently
      const docRef = doc(this.collection, id);
      
      // Get current user data
      const docSnap = await getDoc(docRef);
      if (!docSnap.exists()) {
        throw new Error(`User with id ${id} not found`);
      }
      
      const currentData = docSnap.data() as AdminUser;
      
      // Store additional notification settings in a metadata field
      const currentNotificationSettings = currentData.notificationSettings || {};
      const updatedNotificationSettings = {
        ...currentNotificationSettings,
        ...settings
      };
      
      await updateDoc(docRef, {
        notificationSettings: updatedNotificationSettings,
        updatedAt: Timestamp.now()
      });
      
      return {
        ...currentData,
        notificationSettings: updatedNotificationSettings,
        updatedAt: Timestamp.now()
      } as AdminUser;
    }
  }

  /**
   * Update privacy settings specifically
   */
  async updatePrivacySettings(id: string, settings: Record<string, boolean>): Promise<AdminUser> {
    const preferenceMappings: Record<string, keyof User['preferences']> = {
      publicProfile: 'publicProfile',
      allowMessages: 'allowMessages',
      showLocation: 'showLocation',
      showEmail: 'showEmail',
      allowComments: 'allowComments'
    };
    
    const preferenceUpdates: Partial<User['preferences']> = {};
    
    Object.entries(settings).forEach(([key, value]) => {
      const preferenceKey = preferenceMappings[key];
      if (preferenceKey) {
        preferenceUpdates[preferenceKey] = value;
      }
    });
    
    return this.updatePreferences(id, preferenceUpdates);
  }

  /**
   * Update user last login timestamp
   */
  async updateLastLogin(id: string): Promise<void> {
    return retryOperation(async () => {
      const docRef = doc(this.collection, id);
      await updateDoc(docRef, {
        lastLoginAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    });
  }

  /**
   * Update user statistics
   */
  async updateStatistics(id: string, statistics: Partial<AdminUser['statistics']>): Promise<void> {
    return retryOperation(async () => {
      const docRef = doc(this.collection, id);
      const currentUser = await this.getById(id);
      
      if (currentUser) {
        const updatedStats = {
          ...currentUser.statistics,
          ...statistics
        };

        await updateDoc(docRef, {
          statistics: updatedStats,
          updatedAt: Timestamp.now()
        });
      }
    });
  }

  /**
   * Clear all users from the collection (for testing purposes)
   */
  async clearAllUsers(): Promise<void> {
    return retryOperation(async () => {
      console.log('🗑️ Clearing all users...');
      
      const snapshot = await getDocs(this.collection);
      const batch = writeBatch(db);
      
      snapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });
      
      await batch.commit();
      
      // Clear cache
      userCache.clear();
      
      console.log(`✅ Cleared ${snapshot.size} users`);
    });
  }

  /**
   * Get basic user info for display purposes (used by message system)
   */
  async getUserInfo(userId: string): Promise<{ id: string; name: string; avatar: string | null }> {
    try {
      const user = await this.getById(userId);
      if (user) {
        // Build name from available fields with fallbacks
        let name = '';
        
        if (user.firstName && user.lastName) {
          name = `${user.firstName} ${user.lastName}`;
        } else if (user.firstName) {
          name = user.firstName;
        } else if (user.lastName) {
          name = user.lastName;
        } else if ((user as any).displayName) {
          // Use displayName as fallback when firstName/lastName are null
          name = (user as any).displayName;
        } else if (user.username) {
          name = user.username;
        } else {
          name = user.email?.split('@')[0] || `User ${userId}`;
        }
        
        return {
          id: user.id,
          name: name.trim(),
          avatar: user.avatarUrl || null
        };
      }
      
      // Fallback for unknown users
      return {
        id: userId,
        name: `User ${userId}`,
        avatar: null
      };
    } catch (error) {
      console.error('Error getting user info:', error);
      // Fallback for errors
      return {
        id: userId,
        name: `User ${userId}`,
        avatar: null
      };
    }
  }

  /**
   * Search users by name or username
   */
  async searchUsers(searchQuery: string, maxResults: number = 20): Promise<{ id: string; name: string; username?: string; avatar?: string }[]> {
    try {
      if (!searchQuery.trim()) return [];
      
      const queryLower = searchQuery.toLowerCase();
      
      // Search by firstName, lastName, or username
      const queries = [
        where('firstName', '>=', queryLower),
        where('firstName', '<=', queryLower + '\uf8ff'),
        where('lastName', '>=', queryLower),
        where('lastName', '<=', queryLower + '\uf8ff')
      ];
      
      if (queryLower.length >= 3) {
        queries.push(
          where('username', '>=', queryLower),
          where('username', '<=', queryLower + '\uf8ff')
        );
      }
      
      const results = new Map();
      
      // Execute searches in parallel
      const searchPromises = queries.map(async (queryConstraint) => {
        const q = query(this.collection, queryConstraint, limit(maxResults));
        const snapshot = await getDocs(q);
        return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as AdminUser));
      });
      
      const searchResults = await Promise.all(searchPromises);
      
      // Combine and deduplicate results
      searchResults.flat().forEach(user => {
        if (user.firstName?.toLowerCase().includes(queryLower) ||
            user.lastName?.toLowerCase().includes(queryLower) ||
            user.username?.toLowerCase().includes(queryLower)) {
          // Build name with fallbacks
          let name = '';
          if (user.firstName && user.lastName) {
            name = `${user.firstName} ${user.lastName}`;
          } else if (user.firstName) {
            name = user.firstName;
          } else if (user.lastName) {
            name = user.lastName;
          } else if ((user as any).displayName) {
            name = (user as any).displayName;
          } else if (user.username) {
            name = user.username;
          } else {
            name = user.email?.split('@')[0] || 'Unknown User';
          }
          
          results.set(user.id, {
            id: user.id,
            name: name.trim(),
            username: user.username,
            avatar: user.avatarUrl
          });
        }
      });
      
      return Array.from(results.values()).slice(0, maxResults);
      
    } catch (error) {
      console.error('Error searching users:', error);
      return [];
    }
  }

  /**
   * Get user by username
   */
  async getUserByUsername(username: string): Promise<AdminUser | null> {
    try {
      const q = query(this.collection, where('username', '==', username), limit(1));
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) return null;
      
      const doc = snapshot.docs[0];
      return { id: doc.id, ...doc.data() } as AdminUser;
      
    } catch (error) {
      console.error('Error getting user by username:', error);
      return null;
    }
  }

  /**
   * Get recent contacts for a user (for messaging)
   */
  async getRecentContacts(userId: string, maxResults: number = 10): Promise<{ id: string; name: string; avatar?: string }[]> {
    try {
      // TODO: Implementar baseado no histórico de conversas
      // Por enquanto, retorna usuários recentes baseado em atividade
      
      const q = query(
        this.collection, 
        where('lastActiveAt', '>', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)), // Last 30 days
        orderBy('lastActiveAt', 'desc'),
        limit(maxResults)
      );
      
      const snapshot = await getDocs(q);
      
      return snapshot.docs
        .filter(doc => doc.id !== userId) // Exclude self
        .map(doc => {
          const user = doc.data() as AdminUser;
          // Build name with fallbacks
          let name = '';
          if (user.firstName && user.lastName) {
            name = `${user.firstName} ${user.lastName}`;
          } else if (user.firstName) {
            name = user.firstName;
          } else if (user.lastName) {
            name = user.lastName;
          } else if ((user as any).displayName) {
            name = (user as any).displayName;
          } else if (user.username) {
            name = user.username;
          } else {
            name = user.email?.split('@')[0] || 'Unknown User';
          }
          
          return {
            id: doc.id,
            name: name.trim(),
            avatar: user.avatarUrl
          };
        });
        
    } catch (error) {
      console.error('Error getting recent contacts:', error);
      return [];
    }
  }

  /**
   * Get user statistics including followers/following
   */
  async getUserStats(userId: string): Promise<{
    followers: number;
    following: number;
    isFollowing: boolean;
    pins: number;
    boards: number;
    trades: number;
    checkIns: number;
  }> {
    try {
      // Import services dynamically to avoid circular dependencies
      const { followService } = await import('./followService');
      const { pinsService } = await import('./pinsService');
      const { boardsApiService } = await import('./api/boardsApiService');
      
      // Get follow stats
      const followStats = await followService.getFollowStats(userId);
      
      // Get user content counts
      const [userPins, userBoards] = await Promise.all([
        pinsService.getUserPins(userId),
        boardsApiService.getUserBoards(userId, false)
      ]);
      
      // Get user data for additional stats
      const userData = await this.getById(userId);
      
      return {
        followers: followStats?.followersCount || 0,
        following: followStats?.followingCount || 0,
        isFollowing: false, // This should be checked separately for current user
        pins: userPins?.length || 0,
        boards: userBoards?.length || 0,
        trades: userData?.statistics?.totalTrades || 0,
        checkIns: userData?.statistics?.totalCheckIns || 0
      };
      
    } catch (error) {
      console.error('Error getting user stats:', error);
      // Return default stats on error
      return {
        followers: 0,
        following: 0,
        isFollowing: false,
        pins: 0,
        boards: 0,
        trades: 0,
        checkIns: 0
      };
    }
  }
}

// Export singleton instance
export const usersService = new UsersService();
export default usersService; 