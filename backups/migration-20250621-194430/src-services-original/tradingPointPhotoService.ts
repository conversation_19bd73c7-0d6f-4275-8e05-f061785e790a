import { storage } from './firebase';
import { ref, uploadBytes, getDownloadURL, deleteObject, listAll } from 'firebase/storage';
import { getFirebaseImagePath, invalidateTradingPointImageCache } from '../hooks/useFirebaseImages';

export interface TradingPointPhotoUploadResult {
  url: string;
  path: string;
  uploadedAt: string;
}

class TradingPointPhotoService {
  private readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private readonly ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/heic'];

  /**
   * Validate image file before upload (including minimum dimensions)
   */
  async validateImage(
    file: File, 
    minWidth: number = 300, 
    minHeight: number = 300
  ): Promise<{ valid: boolean; error?: string }> {
    const { validateImageRequirements } = await import('@/utils/imageProcessor');
    
    const requirements = {
      minWidth,
      minHeight,
      maxSizeMB: this.MAX_FILE_SIZE / (1024 * 1024),
      allowedTypes: this.ALLOWED_IMAGE_TYPES
    };

    const validation = await validateImageRequirements(file, requirements);
    return {
      valid: validation.valid,
      error: validation.error
    };
  }

  /**
   * Upload main photo for trading point
   */
  async uploadMainPhoto(
    tradingPointId: string,
    file: File
  ): Promise<TradingPointPhotoUploadResult> {
    // Validate file
    const validation = await this.validateImage(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    try {
      // ✅ IMPORTANTE: Remover foto antiga antes de fazer upload da nova
      await this.removeOldMainPhoto(tradingPointId);

      // Create storage reference for main photo
      const imagePath = getFirebaseImagePath(tradingPointId, 'main');
      const storageRef = ref(storage, imagePath);

      console.log(`📸 Uploading main photo for trading point ${tradingPointId}...`);

      // Upload file
      const snapshot = await uploadBytes(storageRef, file);
      
      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);

      // ✅ Invalidar cache para forçar reload da nova imagem
      invalidateTradingPointImageCache(tradingPointId);

      console.log(`✅ Main photo uploaded successfully: ${downloadURL}`);

      return {
        url: downloadURL,
        path: imagePath,
        uploadedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error('Photo upload error:', error);
      throw new Error('Failed to upload photo. Please try again.');
    }
  }

  /**
   * Remove old main photo from Firebase Storage
   */
  async removeOldMainPhoto(tradingPointId: string): Promise<void> {
    try {
      const imagePath = getFirebaseImagePath(tradingPointId, 'main');
      const storageRef = ref(storage, imagePath);
      
      console.log(`🗑️ Attempting to remove old photo: ${imagePath}`);
      
      await deleteObject(storageRef);
      console.log(`✅ Old photo removed successfully: ${imagePath}`);
      
    } catch (error) {
      // ✅ Não tratar como erro se a foto não existir
      if (error instanceof Error && error.message.includes('object-not-found')) {
        console.log(`ℹ️ No old photo to remove for trading point: ${tradingPointId}`);
      } else {
        console.warn('Error removing old photo:', error);
        // Não lançar erro para não interromper o upload da nova foto
      }
    }
  }

  /**
   * Remove all photos for a trading point
   */
  async removeAllPhotos(tradingPointId: string): Promise<void> {
    try {
      const folderRef = ref(storage, `trading-points/${tradingPointId}/`);
      
      console.log(`🗑️ Removing all photos for trading point: ${tradingPointId}`);
      
      // List all files in the folder
      const listResult = await listAll(folderRef);
      
      // Delete all files
      const deletePromises = listResult.items.map(itemRef => deleteObject(itemRef));
      await Promise.all(deletePromises);
      
      // ✅ Invalidar cache
      invalidateTradingPointImageCache(tradingPointId);
      
      console.log(`✅ All photos removed for trading point: ${tradingPointId}`);
      
    } catch (error) {
      console.error('Error removing all photos:', error);
      throw new Error('Failed to remove photos');
    }
  }

  /**
   * Convert blob to file for upload
   */
  blobToFile(blob: Blob, fileName: string): File {
    return new File([blob], fileName, { type: blob.type });
  }

  /**
   * Optimize image before upload (with HEIC support and advanced compression)
   */
  async optimizeImage(file: File, maxWidth: number = 1200, quality: number = 0.8): Promise<File> {
    try {
      // Use the image processor utility for comprehensive processing
      const { processImage } = await import('@/utils/imageProcessor');
      const processed = await processImage(file);
      
      return processed.file;
    } catch (error) {
      console.error('Error optimizing image:', error);
      
      // Fallback to basic canvas optimization for non-HEIC files
      if (!file.name.toLowerCase().endsWith('.heic') && !file.name.toLowerCase().endsWith('.heif')) {
        return this.basicOptimizeImage(file, maxWidth, quality);
      }
      
      throw new Error('Failed to process image. Please try a different format.');
    }
  }

  /**
   * Basic canvas-based image optimization (fallback method)
   */
  private async basicOptimizeImage(file: File, maxWidth: number = 1200, quality: number = 0.8): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const optimizedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now()
              });
              resolve(optimizedFile);
            } else {
              resolve(file); // Fallback to original
            }
          },
          'image/jpeg',
          quality
        );
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Get photo URL from Firebase Storage
   */
  async getPhotoUrl(tradingPointId: string): Promise<string | null> {
    try {
      const imagePath = getFirebaseImagePath(tradingPointId, 'main');
      const storageRef = ref(storage, imagePath);
      return await getDownloadURL(storageRef);
    } catch (error) {
      console.log(`Photo not found in Firebase Storage: ${tradingPointId}`);
      return null;
    }
  }

  /**
   * ✅ NOVA: Download Google photo and upload to Firebase Storage
   */
  async downloadAndUploadGooglePhoto(
    tradingPointId: string,
    googlePhotoUrl: string,
    photoReference: string
  ): Promise<TradingPointPhotoUploadResult> {
    try {
      console.log(`📸 Downloading Google photo: ${photoReference}`);
      
      // Download da foto do Google
      const response = await fetch(googlePhotoUrl);
      if (!response.ok) {
        throw new Error(`Failed to download Google photo: ${response.status}`);
      }
      
      const blob = await response.blob();
      
      // Verificar se é uma imagem válida
      if (!blob.type.startsWith('image/')) {
        throw new Error('Downloaded file is not a valid image');
      }
      
      // Converter blob para file
      const fileName = `google-${photoReference}.jpg`;
      const file = this.blobToFile(blob, fileName);
      
      // Otimizar imagem
      const optimizedFile = await this.optimizeImage(file);
      
      console.log(`📸 Google photo downloaded, uploading to Firebase Storage...`);
      
      // Upload para Firebase Storage usando a função existente
      const uploadResult = await this.uploadMainPhoto(tradingPointId, optimizedFile);
      
      console.log(`✅ Google photo uploaded to Firebase Storage: ${uploadResult.url}`);
      
      return uploadResult;
      
    } catch (error) {
      console.error('Error downloading and uploading Google photo:', error);
      throw new Error('Failed to process Google photo. Please try again.');
    }
  }
}

export const tradingPointPhotoService = new TradingPointPhotoService(); 