import { User as FirebaseUser } from 'firebase/auth';
import { usersApiService } from './usersApiService';

interface SimpleUser {
  id: string;
  email: string;
  username?: string;
  displayName: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

interface UserCreateData {
  id: string;
  email: string;
  username: string;
  displayName: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
}

class AuthApiService {
  /**
   * Sync Firebase Auth user with PostgreSQL via API
   */
  async syncUserWithPostgreSQL(firebaseUser: FirebaseUser, additionalData?: {
    username?: string;
    firstName?: string;
    lastName?: string;
  }): Promise<SimpleUser> {
    try {
      // Get user by ID first
      let user = await usersApiService.getCurrentUser(firebaseUser.uid);

      if (!user) {
        // Create new user in PostgreSQL
        const displayName = firebaseUser.displayName || `${additionalData?.firstName || ''} ${additionalData?.lastName || ''}`.trim() || firebaseUser.email?.split('@')[0] || 'User';
        
        const userData: UserCreateData = {
          id: firebaseUser.uid,
          email: firebaseUser.email!,
          username: additionalData?.username || displayName.toLowerCase().replace(/\s+/g, '-'),
          displayName: displayName,
          firstName: additionalData?.firstName || firebaseUser.displayName?.split(' ')[0] || 'User',
          lastName: additionalData?.lastName || firebaseUser.displayName?.split(' ').slice(1).join(' ') || '',
          avatarUrl: firebaseUser.photoURL || ''
        };

        user = await usersApiService.upsertUser(userData);
        console.log('✅ User created in PostgreSQL:', user.id);
      } else {
        console.log('✅ User already exists in PostgreSQL:', user.id);
      }

      return {
        id: user.id,
        email: user.email,
        username: additionalData?.username || user.username || user.displayName.toLowerCase().replace(/\s+/g, '-'),
        displayName: user.displayName,
        firstName: additionalData?.firstName || user.displayName.split(' ')[0],
        lastName: additionalData?.lastName || user.displayName.split(' ').slice(1).join(' '),
        avatarUrl: user.avatarUrl || firebaseUser.photoURL || '',
        emailVerified: firebaseUser.emailVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      };
    } catch (error) {
      console.error('❌ Error syncing user with PostgreSQL:', error);
      throw error;
    }
  }

  /**
   * Check if username is available
   */
  async isUsernameAvailable(username: string, excludeUserId?: string): Promise<boolean> {
    try {
      return await usersApiService.isUsernameAvailable(username, excludeUserId);
    } catch (error) {
      console.error('❌ Error checking username availability:', error);
      return false;
    }
  }

  /**
   * Update user settings
   */
  async updateUserSettings(userId: string, settings: any): Promise<void> {
    try {
      await usersApiService.updateUserSettings(userId, settings);
      console.log('✅ User settings updated:', userId);
    } catch (error) {
      console.error('❌ Error updating user settings:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const authApiService = new AuthApiService();
export type { SimpleUser, UserCreateData }; 