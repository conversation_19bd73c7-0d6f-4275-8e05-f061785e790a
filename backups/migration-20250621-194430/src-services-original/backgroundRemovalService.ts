// Importação dinâmica para evitar problemas de ESM
let imglyModule: any = null;

async function loadImglyModule() {
  if (!imglyModule) {
    try {
      imglyModule = await import('@imgly/background-removal');
      console.log('✅ IMGLY module loaded successfully');
    } catch (error) {
      console.error('❌ Failed to load IMGLY module:', error);
      throw new Error('Failed to load background removal library');
    }
  }
  return imglyModule;
}

export interface BackgroundRemovalProgress {
  key: string;
  current: number;
  total: number;
  percentage: number;
}

export interface BackgroundRemovalResult {
  originalBlob: Blob;
  processedBlob: Blob;
  originalUrl: string;
  processedUrl: string;
}

export interface BackgroundRemovalConfig {
  model?: 'isnet' | 'isnet_fp16' | 'isnet_quint8';
  device?: 'cpu' | 'gpu';
  quality?: number;
  debug?: boolean;
  onProgress?: (progress: BackgroundRemovalProgress) => void;
}

class BackgroundRemovalService {
  private isInitialized = false;
  private isPreloading = false;
  private preloadPromise: Promise<void> | null = null;

  /**
   * Configuração padrão otimizada para o projeto
   */
  private getDefaultConfig(userConfig?: BackgroundRemovalConfig): any {
    return {
      // Usar modelo mais preciso para preservar detalhes
      model: userConfig?.model || 'isnet',
      device: userConfig?.device || 'gpu',
      output: {
        format: 'image/png',
        quality: userConfig?.quality || 0.95
      },
      // Configurações específicas para melhor detecção de objetos
      publicPath: undefined, // Usar CDN padrão
      debug: userConfig?.debug || false,
      // Configurações de processamento mais conservadoras
      proxyToWorker: true,
      fetchArgs: {
        mode: 'cors'
      },
      progress: userConfig?.onProgress ? (key: string, current: number, total: number) => {
        userConfig.onProgress!({
          key,
          current,
          total,
          percentage: Math.round((current / total) * 100)
        });
      } : undefined
    };
  }

  /**
   * Configuração específica para pins - mais conservadora para preservar detalhes
   */
  private getPinOptimizedConfig(userConfig?: BackgroundRemovalConfig): any {
    return {
      // Usar modelo isnet_quint8 que pode ser menos agressivo
      model: userConfig?.model || 'isnet_quint8',
      device: userConfig?.device || 'gpu',
      output: {
        format: 'image/png',
        quality: userConfig?.quality || 1.0, // Qualidade máxima
        type: 'foreground' // Garantir que estamos removendo apenas o fundo
      },
      // Configurações otimizadas para preservar detalhes
      publicPath: undefined,
      debug: userConfig?.debug || true, // Ativar debug para ver o que está acontecendo
      proxyToWorker: true,
      fetchArgs: {
        mode: 'cors'
      },
      progress: userConfig?.onProgress ? (key: string, current: number, total: number) => {
        userConfig.onProgress!({
          key,
          current,
          total,
          percentage: Math.round((current / total) * 100)
        });
      } : undefined
    };
  }

  /**
   * Precarrega os assets necessários para melhor performance
   */
  async preloadAssets(config?: BackgroundRemovalConfig): Promise<void> {
    if (this.isInitialized || this.isPreloading) {
      return this.preloadPromise || Promise.resolve();
    }

    this.isPreloading = true;
    
    try {
      const imgly = await loadImglyModule();
      const imglyConfig = this.getDefaultConfig(config);

      this.preloadPromise = imgly.preload(imglyConfig);
      await this.preloadPromise;
      this.isInitialized = true;
      console.log('✅ Background removal assets preloaded successfully');
    } catch (error) {
      console.error('❌ Failed to preload background removal assets:', error);
      throw new Error('Failed to initialize background removal service');
    } finally {
      this.isPreloading = false;
    }
  }

  /**
   * Remove o fundo de uma imagem
   */
  async removeBg(
    imageSource: File | Blob | string | ImageData | ArrayBuffer | Uint8Array,
    config?: BackgroundRemovalConfig
  ): Promise<Blob> {
    try {
      console.log('🔄 Starting background removal process...');
      
      const imgly = await loadImglyModule();
      const imglyConfig = this.getDefaultConfig(config);
      
      // Se não foi inicializado, fazer preload primeiro
      if (!this.isInitialized && !this.isPreloading) {
        console.log('📦 Preloading assets on first use...');
        await this.preloadAssets(config);
      }

      const startTime = Date.now();
      const processedBlob = await imgly.removeBackground(imageSource, imglyConfig);
      const processingTime = Date.now() - startTime;

      console.log(`✅ Background removal completed in ${processingTime}ms`);
      console.log(`📊 Processed image size: ${(processedBlob.size / 1024 / 1024).toFixed(2)}MB`);

      return processedBlob;
    } catch (error) {
      console.error('❌ Background removal failed:', error);
      throw new Error(`Background removal failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Remove o fundo de uma imagem com configuração otimizada para pins
   */
  async removeBgForPin(
    imageSource: File | Blob | string | ImageData | ArrayBuffer | Uint8Array,
    config?: BackgroundRemovalConfig
  ): Promise<Blob> {
    try {
      console.log('🎯 Starting pin-optimized background removal with isnet_quint8 model...');
      
      const imgly = await loadImglyModule();
      const imglyConfig = this.getPinOptimizedConfig(config);
      
      console.log('🔧 Using config:', imglyConfig);
      
      if (!this.isInitialized && !this.isPreloading) {
        console.log('📦 Preloading isnet_quint8 assets for pin processing...');
        await this.preloadAssets(config);
      }

      const startTime = Date.now();
      const processedBlob = await imgly.removeBackground(imageSource, imglyConfig);
      const processingTime = Date.now() - startTime;

      console.log(`✅ Pin background removal (isnet_quint8) completed in ${processingTime}ms`);
      console.log(`📊 Processed pin size: ${(processedBlob.size / 1024 / 1024).toFixed(2)}MB`);

      return processedBlob;
    } catch (error) {
      console.error('❌ Pin background removal failed:', error);
      throw new Error(`Pin background removal failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Processa uma imagem e retorna tanto a original quanto a processada
   */
  async processImageWithBackgroundRemoval(
    file: File,
    config?: BackgroundRemovalConfig
  ): Promise<BackgroundRemovalResult> {
    try {
      console.log('🖼️ Processing image with background removal...');
      console.log('📁 Original file:', {
        name: file.name,
        size: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
        type: file.type
      });

      // Validar arquivo
      if (!this.isValidImageFile(file)) {
        throw new Error('Invalid image file format');
      }

      // Processar remoção de fundo
      const processedBlob = await this.removeBg(file, config);

      // Criar URLs para preview
      const originalUrl = URL.createObjectURL(file);
      const processedUrl = URL.createObjectURL(processedBlob);

      return {
        originalBlob: file,
        processedBlob,
        originalUrl,
        processedUrl
      };
    } catch (error) {
      console.error('❌ Failed to process image with background removal:', error);
      throw error;
    }
  }

  /**
   * Processa uma imagem de pin com configuração otimizada para preservar detalhes
   */
  async processPinWithBackgroundRemoval(
    file: File,
    config?: BackgroundRemovalConfig
  ): Promise<BackgroundRemovalResult> {
    try {
      console.log('🎯 Processing pin image with optimized background removal...');
      console.log('📁 Original pin file:', {
        name: file.name,
        size: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
        type: file.type
      });

      // Validar arquivo
      if (!this.isValidImageFile(file)) {
        throw new Error('Invalid image file format');
      }

      // Processar remoção de fundo com configuração otimizada para pins
      const processedBlob = await this.removeBgForPin(file, config);

      // Criar URLs para preview
      const originalUrl = URL.createObjectURL(file);
      const processedUrl = URL.createObjectURL(processedBlob);

      return {
        originalBlob: file,
        processedBlob,
        originalUrl,
        processedUrl
      };
    } catch (error) {
      console.error('❌ Failed to process pin with background removal:', error);
      throw error;
    }
  }

  /**
   * Valida se o arquivo é uma imagem suportada
   */
  private isValidImageFile(file: File): boolean {
    const supportedTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/webp',
      'image/gif'
    ];
    return supportedTypes.includes(file.type);
  }

  /**
   * Limpa URLs de objeto para evitar vazamentos de memória
   */
  cleanupUrls(...urls: string[]): void {
    urls.forEach(url => {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url);
      }
    });
  }

  /**
   * Verifica se o browser suporta as funcionalidades necessárias
   */
  static checkBrowserSupport(): { supported: boolean; issues: string[] } {
    const issues: string[] = [];

    // Verificar WebAssembly
    if (typeof WebAssembly === 'undefined') {
      issues.push('WebAssembly not supported');
    }

    // Verificar SharedArrayBuffer (para melhor performance)
    if (typeof SharedArrayBuffer === 'undefined') {
      issues.push('SharedArrayBuffer not available (performance may be reduced)');
    }

    // Verificar WebGL
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) {
      issues.push('WebGL not supported (GPU acceleration unavailable)');
    }

    return {
      supported: issues.length === 0 || issues.every(issue => issue.includes('performance')),
      issues
    };
  }

  /**
   * Obtém informações sobre o status do serviço
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isPreloading: this.isPreloading,
      browserSupport: BackgroundRemovalService.checkBrowserSupport()
    };
  }
}

// Exportar instância singleton
export const backgroundRemovalService = new BackgroundRemovalService();
export default backgroundRemovalService;
 