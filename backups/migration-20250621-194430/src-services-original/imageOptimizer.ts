/**
 * Image Optimizer Service
 * Generates optimized image variations in WebP/AVIF formats
 * with different resolutions for responsive images
 */

import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage } from './firebase';
import imageCompression from 'browser-image-compression';

export interface ImageVariation {
  url: string;
  width: number;
  format: 'webp' | 'avif' | 'jpeg';
  size: number;
}

export interface OptimizedImageSet {
  original: string;
  variations: ImageVariation[];
  srcSet: string;
  sizes: string;
}

export class ImageOptimizerService {
  private static readonly SUPPORTED_FORMATS = ['webp', 'avif', 'jpeg'] as const;
  private static readonly BREAKPOINTS = [640, 768, 1024, 1280, 1536] as const;
  private static readonly QUALITY_SETTINGS = {
    webp: 0.8,
    avif: 0.7,
    jpeg: 0.85,
  };

  /**
   * Optimizes an image file and generates multiple variations
   */
  static async optimizeImage(
    file: File,
    options: {
      maxWidth?: number;
      generateResponsive?: boolean;
      formats?: Array<'webp' | 'avif' | 'jpeg'>;
      basePath?: string;
    } = {}
  ): Promise<OptimizedImageSet> {
    const {
      maxWidth = 1920,
      generateResponsive = true,
      formats = ['webp', 'avif', 'jpeg'],
      basePath = 'optimized-images',
    } = options;

    try {
      const variations: ImageVariation[] = [];
      const fileNameWithoutExt = file.name.replace(/\.[^/.]+$/, '');
      const timestamp = Date.now();

      // Generate variations for each format and size
      for (const format of formats) {
        if (generateResponsive) {
          // Generate responsive variations
          for (const width of this.BREAKPOINTS) {
            if (width <= maxWidth) {
              const variation = await this.createVariation(
                file,
                width,
                format,
                `${basePath}/${fileNameWithoutExt}_${width}w_${timestamp}.${format}`
              );
              if (variation) {
                variations.push(variation);
              }
            }
          }
        } else {
          // Generate single optimized version
          const variation = await this.createVariation(
            file,
            maxWidth,
            format,
            `${basePath}/${fileNameWithoutExt}_${timestamp}.${format}`
          );
          if (variation) {
            variations.push(variation);
          }
        }
      }

      // Generate srcSet and sizes attributes
      const srcSet = this.generateSrcSet(variations);
      const sizes = this.generateSizes();

      // Use the best quality variation as original
      const original = variations.find(v => v.format === 'webp') || variations[0];

      return {
        original: original?.url || '',
        variations,
        srcSet,
        sizes,
      };
    } catch (error) {
      console.error('Error optimizing image:', error);
      throw new Error('Failed to optimize image');
    }
  }

  /**
   * Creates a single image variation
   */
  private static async createVariation(
    file: File,
    width: number,
    format: 'webp' | 'avif' | 'jpeg',
    storagePath: string
  ): Promise<ImageVariation | null> {
    try {
      // Compress and resize image
      const compressedFile = await imageCompression(file, {
        maxWidthOrHeight: width,
        useWebWorker: true,
        fileType: `image/${format}`,
        initialQuality: this.QUALITY_SETTINGS[format],
      });

      // Upload to Firebase Storage with cache headers
      const storageRef = ref(storage, storagePath);
      const metadata = {
        cacheControl: 'public,max-age=31536000,immutable',
        contentType: `image/${format}`,
        customMetadata: {
          width: width.toString(),
          format,
          optimized: 'true',
          timestamp: Date.now().toString(),
        },
      };

      await uploadBytes(storageRef, compressedFile, metadata);
      const url = await getDownloadURL(storageRef);

      return {
        url,
        width,
        format,
        size: compressedFile.size,
      };
    } catch (error) {
      console.error(`Error creating ${format} variation at ${width}px:`, error);
      return null;
    }
  }

  /**
   * Generates srcSet attribute for responsive images
   */
  private static generateSrcSet(variations: ImageVariation[]): string {
    return variations
      .sort((a, b) => a.width - b.width)
      .map(variation => `${variation.url} ${variation.width}w`)
      .join(', ');
  }

  /**
   * Generates sizes attribute for responsive images
   */
  private static generateSizes(): string {
    return [
      '(max-width: 640px) 100vw',
      '(max-width: 768px) 90vw',
      '(max-width: 1024px) 80vw',
      '(max-width: 1280px) 70vw',
      '60vw',
    ].join(', ');
  }

  /**
   * Optimizes an existing image URL by creating WebP/AVIF versions
   */
  static async optimizeExistingImage(
    imageUrl: string,
    options: {
      maxWidth?: number;
      formats?: Array<'webp' | 'avif' | 'jpeg'>;
    } = {}
  ): Promise<OptimizedImageSet | null> {
    try {
      // Fetch the image
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error('Failed to fetch image');
      }

      const blob = await response.blob();
      const file = new File([blob], 'image.jpg', { type: blob.type });

      return await this.optimizeImage(file, options);
    } catch (error) {
      console.error('Error optimizing existing image:', error);
      return null;
    }
  }

  /**
   * Preloads critical images for better LCP
   */
  static preloadCriticalImage(imageSet: OptimizedImageSet): void {
    if (typeof document === 'undefined') return;

    // Create preload link for the best format
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = imageSet.original;
    
    if (imageSet.srcSet) {
      link.setAttribute('imagesrcset', imageSet.srcSet);
      link.setAttribute('imagesizes', imageSet.sizes);
    }

    document.head.appendChild(link);
  }

  /**
   * Gets the best image format supported by the browser
   */
  static async getBestSupportedFormat(): Promise<'webp' | 'avif' | 'jpeg'> {
    // Check AVIF support
    if (await this.supportsFormat('avif')) {
      return 'avif';
    }
    
    // Check WebP support
    if (await this.supportsFormat('webp')) {
      return 'webp';
    }
    
    // Fallback to JPEG
    return 'jpeg';
  }

  /**
   * Checks if browser supports a specific image format
   */
  private static supportsFormat(format: 'webp' | 'avif'): Promise<boolean> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      
      const testImages = {
        webp: 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA',
        avif: 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A='
      };
      
      img.src = testImages[format];
    });
  }
}

export default ImageOptimizerService; 