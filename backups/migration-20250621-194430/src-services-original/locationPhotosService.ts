/**
 * Location Photos Service
 * Serviço para buscar fotos de localidades usando a API do TradingMap
 */

export interface LocationPhotoResponse {
  photoUrl: string;
  proxyUrl?: string; // URL de proxy para contornar problemas de CORS/referrer
  placeName: string;
  photoResourceName: string;
  searchTerm: string;
  dimensions: {
    width: number;
    height: number;
  };
  originalDimensions?: {
    width: number;
    height: number;
  };
  authorAttributions?: Array<{
    displayName?: string;
    uri?: string;
    photoUri?: string;
  }>;
}

export interface LocationPhotoError {
  error: string;
  term: string;
}

class LocationPhotosService {
  private baseUrl: string;
  private cache: Map<string, LocationPhotoResponse> = new Map();
  private lastRequestTime: number = 0;
  private minDelayBetweenRequests: number = 3000; // 3 segundos entre requisições

  constructor() {
    // Usar localhost:3001 para desenvolvimento, pode ser configurado via env
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
    console.log('🔧 LocationPhotosService initialized with baseUrl:', this.baseUrl);
    
    // Verificar se estamos no ambiente correto
    if (typeof window !== 'undefined') {
      console.log('🌐 Current location:', window.location.origin);
      console.log('🔗 API will call:', this.baseUrl);
    }
  }

  /**
   * Busca foto de uma localidade pelo nome/termo
   */
  async getLocationPhoto(
    term: string, 
    width: number = 800, 
    height: number = 600
  ): Promise<LocationPhotoResponse> {
    const cacheKey = `${term}-${width}x${height}`;
    
    // Verificar cache primeiro
    if (this.cache.has(cacheKey)) {
      console.log(`📦 Cache hit for "${term}"`);
      return this.cache.get(cacheKey)!;
    }

    // Implementar delay entre requisições para evitar rate limiting
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.minDelayBetweenRequests) {
      const delayNeeded = this.minDelayBetweenRequests - timeSinceLastRequest;
      console.log(`⏰ Waiting ${delayNeeded}ms before next request for "${term}"`);
      await new Promise(resolve => setTimeout(resolve, delayNeeded));
    }
    
    this.lastRequestTime = Date.now();

    try {
      const params = new URLSearchParams({
        term: term.trim(),
        width: width.toString(),
        height: height.toString()
      });

      const url = `${this.baseUrl}/api/tradingmap/fotos?${params}`;
      console.log(`🔍 Fetching photo for "${term}" from:`, url);

      const response = await fetch(url);
      
      console.log(`📡 Response status for "${term}":`, response.status, response.statusText);
      
      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`;
        
        try {
          const errorData: LocationPhotoError = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          console.warn('Failed to parse error response:', parseError);
        }
        
        // ✅ CORREÇÃO: Tratamento específico para API key não configurada
        if (response.status === 500 && errorMessage.includes('Google Maps API key not configured')) {
          console.warn(`⚠️ Google Maps API key not configured - using fallback for "${term}"`);
          // Retornar uma resposta de fallback em vez de erro
          const fallbackResponse: LocationPhotoResponse = {
            photoUrl: '/images/system/location-placeholder.svg', // Imagem placeholder SVG
            placeName: term,
            photoResourceName: 'fallback',
            searchTerm: term,
            dimensions: { width, height },
            authorAttributions: []
          };
          
          // Armazenar no cache para evitar requisições repetidas
          this.cache.set(cacheKey, fallbackResponse);
          return fallbackResponse;
        }
        
        // Se for rate limit, aumentar o delay para próximas requisições
        if (response.status === 429) {
          this.minDelayBetweenRequests = Math.min(this.minDelayBetweenRequests * 2, 10000); // Max 10s
          console.log(`⚠️ Rate limited! Increasing delay to ${this.minDelayBetweenRequests}ms`);
        }
        
        throw new Error(errorMessage);
      }

      const data: LocationPhotoResponse = await response.json();
      console.log(`✅ Photo fetched successfully for "${term}":`, data.placeName);
      
      // Armazenar no cache
      this.cache.set(cacheKey, data);
      
      // Se a requisição foi bem-sucedida, reduzir gradualmente o delay
      if (this.minDelayBetweenRequests > 3000) {
        this.minDelayBetweenRequests = Math.max(this.minDelayBetweenRequests * 0.8, 3000);
        console.log(`✅ Reducing delay to ${this.minDelayBetweenRequests}ms`);
      }
      
      return data;
    } catch (error) {
      console.error(`❌ Error fetching photo for "${term}":`, error);
      throw error;
    }
  }

  /**
   * Busca múltiplas fotos para diferentes termos (com delay sequencial)
   */
  async getMultipleLocationPhotos(
    terms: string[], 
    width: number = 800, 
    height: number = 600
  ): Promise<Record<string, LocationPhotoResponse | null>> {
    const results: Record<string, LocationPhotoResponse | null> = {};

    // Processar sequencialmente para evitar rate limiting
    for (const term of terms) {
      try {
        const photo = await this.getLocationPhoto(term, width, height);
        results[term] = photo;
      } catch (error) {
        console.warn(`Failed to get photo for "${term}":`, error);
        results[term] = null;
      }
    }

    return results;
  }

  /**
   * Limpa o cache de fotos
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Remove uma entrada específica do cache
   */
  removeCacheEntry(term: string, width: number = 800, height: number = 600): void {
    const cacheKey = `${term}-${width}x${height}`;
    this.cache.delete(cacheKey);
  }

  /**
   * Verifica se uma foto está no cache
   */
  isCached(term: string, width: number = 800, height: number = 600): boolean {
    const cacheKey = `${term}-${width}x${height}`;
    return this.cache.has(cacheKey);
  }

  /**
   * Pré-carrega fotos para termos comuns (com delay)
   */
  async preloadCommonLocations(locations: string[]): Promise<void> {
    console.log('🔄 Pre-loading photos for common locations...');
    
    try {
      await this.getMultipleLocationPhotos(locations, 400, 300);
      console.log('✅ Common locations photos pre-loaded');
    } catch (error) {
      console.warn('⚠️ Failed to pre-load some location photos:', error);
    }
  }

  /**
   * Reseta o delay para o valor padrão
   */
  resetDelay(): void {
    this.minDelayBetweenRequests = 3000;
    console.log('🔄 Delay reset to default (3000ms)');
  }
}

// Singleton instance
export const locationPhotosService = new LocationPhotosService();

// Export default
export default locationPhotosService; 