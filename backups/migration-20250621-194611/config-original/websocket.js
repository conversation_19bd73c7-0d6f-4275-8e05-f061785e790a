import { WebSocketServer } from 'ws';

// Store active WebSocket connections by userId (used by admin monitoring)
export const activeConnections = new Map();

export function setupWebSocket(server) {
  const wss = new WebSocketServer({ server });
  
  wss.on('connection', (ws, req) => {
    console.log('WebSocket connection established');
    
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        
        if (data.type === 'auth') {
          const { userId, token } = data;
          if (userId && token) {
            ws.userId = userId;
            activeConnections.set(userId, ws);
            console.log(`User ${userId} connected via WebSocket`);
            
            // Broadcast user online status
            broadcastToAllUsers({
              type: 'user_online',
              userId: userId
            }, userId);
          }
        }
        
        if (data.type === 'ping') {
          ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
        }
        
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    });
    
    ws.on('close', () => {
      if (ws.userId) {
        activeConnections.delete(ws.userId);
        console.log(`User ${ws.userId} disconnected from WebSocket`);
        
        // Broadcast user offline status
        broadcastToAllUsers({
          type: 'user_offline',
          userId: ws.userId
        }, ws.userId);
      }
    });
    
    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
    });
  });
  
  return wss;
}

export async function broadcastToConversation(conversationId, message, excludeUserId = null) {
  try {
    const { getPool } = await import('./database.js');
    const pool = await getPool();
    
    // Get all participants in the conversation
    const participantsResult = await pool.query(`
      SELECT user_id 
      FROM conversation_participant 
      WHERE conversation_id = $1 AND is_left = false
    `, [conversationId]);
    
    const participants = participantsResult.rows.map(row => row.user_id);
    
    // Send message to all participants except the sender
    participants.forEach(userId => {
      if (userId !== excludeUserId) {
        const ws = activeConnections.get(userId);
        if (ws && ws.readyState === ws.OPEN) {
          ws.send(JSON.stringify(message));
        }
      }
    });
    
  } catch (error) {
    console.error('Error broadcasting to conversation:', error);
  }
}

export function sendNotificationToUser(userId, notification) {
  const ws = activeConnections.get(userId);
  if (ws && ws.readyState === ws.OPEN) {
    ws.send(JSON.stringify({
      type: 'notification',
      ...notification
    }));
  }
}

export function broadcastToAllUsers(message, excludeUserId = null) {
  activeConnections.forEach((ws, userId) => {
    if (userId !== excludeUserId && ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify(message));
    }
  });
} 