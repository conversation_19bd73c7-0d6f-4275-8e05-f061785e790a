import express from 'express';
import { getPool } from '../config/database.js';

const router = express.Router();

// Get user's boards
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        b.*,
        COALESCE(COUNT(bp.pin_id), 0) as pin_count
      FROM board b
      LEFT JOIN board_pin bp ON b.id = bp.board_id
      WHERE b.user_id = $1
      GROUP BY b.id
      ORDER BY b.sort_order ASC, b.updated_at DESC
    `, [userId]);
    
    res.json(result.rows);
    
  } catch (error) {
    console.error('Error getting user boards:', error);
    res.status(500).json({ error: 'Failed to get boards' });
  }
});

// Get board by ID
router.get('/:boardId', async (req, res) => {
  try {
    const { boardId } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        b.*,
        COALESCE(COUNT(bp.pin_id), 0) as pin_count,
        u.first_name, u.last_name, u.username, u.avatar_url
      FROM board b
      LEFT JOIN board_pin bp ON b.id = bp.board_id
      LEFT JOIN "user" u ON b.user_id = u.id
      WHERE b.id = $1
      GROUP BY b.id, u.id
    `, [boardId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Board not found' });
    }
    
    res.json(result.rows[0]);
    
  } catch (error) {
    console.error('Error getting board:', error);
    res.status(500).json({ error: 'Failed to get board' });
  }
});

// Create new board
router.post('/', async (req, res) => {
  try {
    const { name, description, isPrivate, coverImageUrl, userId } = req.body;
    
    if (!name || !userId) {
      return res.status(400).json({ error: 'Name and userId are required' });
    }
    
    const pool = await getPool();
    
    // Get next sort order
    const sortResult = await pool.query(
      'SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order FROM board WHERE user_id = $1',
      [userId]
    );
    const sortOrder = sortResult.rows[0].next_order;
    
    const result = await pool.query(`
      INSERT INTO board (name, description, is_private, cover_image_url, user_id, sort_order, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [name, description, isPrivate || false, coverImageUrl, userId, sortOrder]);
    
    res.status(201).json(result.rows[0]);
    
  } catch (error) {
    console.error('Error creating board:', error);
    res.status(500).json({ error: 'Failed to create board' });
  }
});

// Update board
router.put('/:boardId', async (req, res) => {
  try {
    const { boardId } = req.params;
    const { name, description, isPrivate, coverImageUrl } = req.body;
    
    const pool = await getPool();
    
    const result = await pool.query(`
      UPDATE board 
      SET name = $1, description = $2, is_private = $3, cover_image_url = $4, updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
      RETURNING *
    `, [name, description, isPrivate, coverImageUrl, boardId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Board not found' });
    }
    
    res.json(result.rows[0]);
    
  } catch (error) {
    console.error('Error updating board:', error);
    res.status(500).json({ error: 'Failed to update board' });
  }
});

// Delete board
router.delete('/:boardId', async (req, res) => {
  try {
    const { boardId } = req.params;
    const pool = await getPool();
    
    // Delete board and all associated pins (cascade)
    const result = await pool.query('DELETE FROM board WHERE id = $1', [boardId]);
    
    if (result.rowCount === 0) {
      return res.status(404).json({ error: 'Board not found' });
    }
    
    res.json({ success: true, message: 'Board deleted successfully' });
    
  } catch (error) {
    console.error('Error deleting board:', error);
    res.status(500).json({ error: 'Failed to delete board' });
  }
});

// Update board order for user
router.put('/user/:userId/order', async (req, res) => {
  try {
    const { userId } = req.params;
    const { boardIds } = req.body;
    
    if (!Array.isArray(boardIds)) {
      return res.status(400).json({ error: 'boardIds must be an array' });
    }
    
    const pool = await getPool();
    
    // Update sort order for each board
    for (let i = 0; i < boardIds.length; i++) {
      await pool.query(
        'UPDATE board SET sort_order = $1 WHERE id = $2 AND user_id = $3',
        [i + 1, boardIds[i], userId]
      );
    }
    
    res.json({ success: true, message: 'Board order updated successfully' });
    
  } catch (error) {
    console.error('Error updating board order:', error);
    res.status(500).json({ error: 'Failed to update board order' });
  }
});

export default router; 