import express from 'express';
import { getPool } from '../config/database.js';

const router = express.Router();

// GET /api/trading-points
router.get('/', async (req, res) => {
  try {
    const { 
      lat, 
      lng, 
      radius = 50, // km
      limit = 100, 
      offset = 0,
      type = null,
      search = null 
    } = req.query;

    const pool = await getPool();
    
    let query = `
      SELECT 
        tp.*,
        u.first_name as owner_first_name,
        u.last_name as owner_last_name,
        u.username as owner_username,
        u.avatar_url as owner_avatar_url,
        COUNT(DISTINCT tpr.id) as reviews_count,
        COALESCE(AVG(tpr.rating), 0) as average_rating
      FROM trading_point tp
      LEFT JOIN "user" u ON tp.user_id = u.uid
      LEFT JOIN trading_point_reviews tpr ON tp.id = tpr.trading_point_id
      WHERE tp.is_active = true
    `;
    
    const params = [];
    let paramIndex = 1;

    // Location-based filtering
    if (lat && lng) {
      query += ` AND (
        6371 * acos(
          cos(radians($${paramIndex})) * cos(radians(tp.latitude)) *
          cos(radians(tp.longitude) - radians($${paramIndex + 1})) +
          sin(radians($${paramIndex})) * sin(radians(tp.latitude))
        )
      ) <= $${paramIndex + 2}`;
      params.push(parseFloat(lat), parseFloat(lng), parseFloat(radius));
      paramIndex += 3;
    }

    // Type filtering
    if (type) {
      query += ` AND tp.type = $${paramIndex}`;
      params.push(type);
      paramIndex++;
    }

    // Search filtering
    if (search) {
      query += ` AND (
        tp.name ILIKE $${paramIndex} OR 
        tp.description ILIKE $${paramIndex} OR
        tp.address ILIKE $${paramIndex}
      )`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    query += `
      GROUP BY tp.id, u.first_name, u.last_name, u.username, u.avatar_url
      ORDER BY 
        CASE WHEN tp.is_verified THEN 0 ELSE 1 END,
        average_rating DESC,
        tp.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(parseInt(limit), parseInt(offset));

    const result = await pool.query(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(DISTINCT tp.id) as total
      FROM trading_point tp
      WHERE tp.is_active = true
    `;
    
    const countParams = [];
    let countParamIndex = 1;

    if (lat && lng) {
      countQuery += ` AND (
        6371 * acos(
          cos(radians($${countParamIndex})) * cos(radians(tp.latitude)) *
          cos(radians(tp.longitude) - radians($${countParamIndex + 1})) +
          sin(radians($${countParamIndex})) * sin(radians(tp.latitude))
        )
      ) <= $${countParamIndex + 2}`;
      countParams.push(parseFloat(lat), parseFloat(lng), parseFloat(radius));
      countParamIndex += 3;
    }

    if (type) {
      countQuery += ` AND tp.type = $${countParamIndex}`;
      countParams.push(type);
      countParamIndex++;
    }

    if (search) {
      countQuery += ` AND (
        tp.name ILIKE $${countParamIndex} OR 
        tp.description ILIKE $${countParamIndex} OR
        tp.address ILIKE $${countParamIndex}
      )`;
      countParams.push(`%${search}%`);
    }

    const countResult = await pool.query(countQuery, countParams);

    res.json({
      tradingPoints: result.rows,
      total: parseInt(countResult.rows[0].total),
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

  } catch (error) {
    console.error('Error getting trading points:', error);
    res.status(500).json({ error: 'Failed to get trading points' });
  }
});

// GET /api/trading-points/:id
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        tp.*,
        u.first_name as owner_first_name,
        u.last_name as owner_last_name,
        u.username as owner_username,
        u.avatar_url as owner_avatar_url,
        COUNT(DISTINCT tpr.id) as reviews_count,
        COALESCE(AVG(tpr.rating), 0) as average_rating
      FROM trading_point tp
      LEFT JOIN "user" u ON tp.user_id = u.uid
      LEFT JOIN trading_point_reviews tpr ON tp.id = tpr.trading_point_id
      WHERE tp.id = $1 AND tp.is_active = true
      GROUP BY tp.id, u.first_name, u.last_name, u.username, u.avatar_url
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Trading point not found' });
    }

    // Get recent reviews
    const reviewsResult = await pool.query(`
      SELECT 
        tpr.*,
        u.first_name,
        u.last_name,
        u.username,
        u.avatar_url
      FROM trading_point_reviews tpr
      JOIN "user" u ON tpr.user_id = u.uid
      WHERE tpr.trading_point_id = $1
      ORDER BY tpr.created_at DESC
      LIMIT 10
    `, [id]);

    const tradingPoint = {
      ...result.rows[0],
      recent_reviews: reviewsResult.rows
    };

    res.json(tradingPoint);

  } catch (error) {
    console.error('Error getting trading point:', error);
    res.status(500).json({ error: 'Failed to get trading point' });
  }
});

// POST /api/trading-points
router.post('/', async (req, res) => {
  try {
    const {
      userId,
      name,
      description,
      type,
      latitude,
      longitude,
      address,
      photoUrl = null,
      googlePhotoReference = null,
      operatingHours = null,
      contactInfo = null
    } = req.body;

    if (!userId || !name || !type || !latitude || !longitude) {
      return res.status(400).json({ 
        error: 'User ID, name, type, latitude, and longitude are required' 
      });
    }

    // Validate coordinates
    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      return res.status(400).json({ error: 'Invalid coordinates' });
    }

    // Validate type
    const validTypes = ['store', 'park', 'event', 'home', 'other'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({ 
        error: `Invalid type. Must be one of: ${validTypes.join(', ')}` 
      });
    }

    const pool = await getPool();
    
    const result = await pool.query(`
      INSERT INTO trading_point (
        user_id, name, description, type, latitude, longitude, 
        address, photo_url, google_photo_reference, operating_hours, 
        contact_info, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
      RETURNING *
    `, [
      userId, name, description, type, latitude, longitude,
      address, photoUrl, googlePhotoReference, 
      operatingHours ? JSON.stringify(operatingHours) : null,
      contactInfo ? JSON.stringify(contactInfo) : null
    ]);

    res.status(201).json(result.rows[0]);

  } catch (error) {
    console.error('Error creating trading point:', error);
    res.status(500).json({ error: 'Failed to create trading point' });
  }
});

// PUT /api/trading-points/:id
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      userId,
      name,
      description,
      type,
      latitude,
      longitude,
      address,
      photoUrl,
      googlePhotoReference,
      operatingHours,
      contactInfo,
      isActive
    } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const pool = await getPool();
    
    // Check if user owns this trading point or is admin
    const ownershipCheck = await pool.query(
      'SELECT * FROM trading_point WHERE id = $1 AND user_id = $2',
      [id, userId]
    );

    if (ownershipCheck.rows.length === 0) {
      // Check if user is admin
      const adminCheck = await pool.query(
        'SELECT * FROM "user" WHERE uid = $1 AND role = $2',
        [userId, 'admin']
      );

      if (adminCheck.rows.length === 0) {
        return res.status(403).json({ 
          error: 'You can only edit your own trading points' 
        });
      }
    }

    // Build update query dynamically
    const updates = [];
    const params = [];
    let paramIndex = 1;

    if (name !== undefined) {
      updates.push(`name = $${paramIndex}`);
      params.push(name);
      paramIndex++;
    }

    if (description !== undefined) {
      updates.push(`description = $${paramIndex}`);
      params.push(description);
      paramIndex++;
    }

    if (type !== undefined) {
      const validTypes = ['store', 'park', 'event', 'home', 'other'];
      if (!validTypes.includes(type)) {
        return res.status(400).json({ 
          error: `Invalid type. Must be one of: ${validTypes.join(', ')}` 
        });
      }
      updates.push(`type = $${paramIndex}`);
      params.push(type);
      paramIndex++;
    }

    if (latitude !== undefined && longitude !== undefined) {
      if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
        return res.status(400).json({ error: 'Invalid coordinates' });
      }
      updates.push(`latitude = $${paramIndex}, longitude = $${paramIndex + 1}`);
      params.push(latitude, longitude);
      paramIndex += 2;
    }

    if (address !== undefined) {
      updates.push(`address = $${paramIndex}`);
      params.push(address);
      paramIndex++;
    }

    if (photoUrl !== undefined) {
      updates.push(`photo_url = $${paramIndex}`);
      params.push(photoUrl);
      paramIndex++;
    }

    if (googlePhotoReference !== undefined) {
      updates.push(`google_photo_reference = $${paramIndex}`);
      params.push(googlePhotoReference);
      paramIndex++;
    }

    if (operatingHours !== undefined) {
      updates.push(`operating_hours = $${paramIndex}`);
      params.push(operatingHours ? JSON.stringify(operatingHours) : null);
      paramIndex++;
    }

    if (contactInfo !== undefined) {
      updates.push(`contact_info = $${paramIndex}`);
      params.push(contactInfo ? JSON.stringify(contactInfo) : null);
      paramIndex++;
    }

    if (isActive !== undefined) {
      updates.push(`is_active = $${paramIndex}`);
      params.push(isActive);
      paramIndex++;
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updates.push(`updated_at = NOW()`);
    params.push(id);

    const query = `
      UPDATE trading_point 
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await pool.query(query, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Trading point not found' });
    }

    res.json(result.rows[0]);

  } catch (error) {
    console.error('Error updating trading point:', error);
    res.status(500).json({ error: 'Failed to update trading point' });
  }
});

// Get trading points near location
router.get('/near', async (req, res) => {
  try {
    const { lat, lng, radius = 10000 } = req.query; // radius in meters
    
    if (!lat || !lng) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }
    
    const latitude = parseFloat(lat);
    const longitude = parseFloat(lng);
    const radiusMeters = parseInt(radius);
    
    if (isNaN(latitude) || isNaN(longitude) || isNaN(radiusMeters)) {
      return res.status(400).json({ error: 'Invalid coordinates or radius' });
    }
    
    const pool = await getPool();
    
    // Use PostGIS to find nearby trading points
    const result = await pool.query(`
      SELECT 
        tp.*,
        ST_Distance(
          ST_Transform(ST_SetSRID(ST_MakePoint(longitude, latitude), 4326), 3857),
          ST_Transform(ST_SetSRID(ST_MakePoint($2, $1), 4326), 3857)
        ) as distance_meters,
        COUNT(DISTINCT tpr.id) as review_count,
        AVG(tpr.rating) as avg_rating
      FROM trading_points tp
      LEFT JOIN trading_point_reviews tpr ON tp.id = tpr.trading_point_id
      WHERE tp.is_active = true
      AND ST_DWithin(
        ST_Transform(ST_SetSRID(ST_MakePoint(tp.longitude, tp.latitude), 4326), 3857),
        ST_Transform(ST_SetSRID(ST_MakePoint($2, $1), 4326), 3857),
        $3
      )
      GROUP BY tp.id
      ORDER BY distance_meters ASC
      LIMIT 50
    `, [latitude, longitude, radiusMeters]);
    
    const tradingPoints = result.rows.map(row => ({
      ...row,
      distance_meters: Math.round(row.distance_meters),
      avg_rating: row.avg_rating ? parseFloat(row.avg_rating).toFixed(1) : null,
      review_count: parseInt(row.review_count)
    }));
    
    res.json(tradingPoints);
    
  } catch (error) {
    console.error('Error getting nearby trading points:', error);
    res.status(500).json({ error: 'Failed to get trading points' });
  }
});

export default router; 