import express from 'express';
import { getPool } from '../config/database.js';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { broadcastToConversation } from '../config/websocket.js';
import { upload } from '../middleware/upload.js';

const router = express.Router();

// Configure multer for message attachments
const messageUpload = multer({
  dest: 'public/message-attachments/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'video/mp4', 'video/quicktime', 'video/webm',
      'audio/mpeg', 'audio/wav', 'audio/ogg',
      'application/pdf'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type'), false);
    }
  }
});

// Upload message attachment
router.post('/upload', messageUpload.single('attachment'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    
    const { originalname, mimetype, size, path } = req.file;
    
    // Move file to permanent location (implement your file storage logic here)
    const attachmentUrl = `/message-attachments/${req.file.filename}`;
    
    res.json({
      url: attachmentUrl,
      name: originalname,
      type: mimetype,
      size
    });
    
  } catch (error) {
    console.error('Error uploading attachment:', error);
    res.status(500).json({ error: 'Failed to upload attachment' });
  }
});

// Create or get conversation
router.post('/conversations', async (req, res) => {
  try {
    const { participants, title, isGroup = false } = req.body;
    
    if (!participants || participants.length < 2) {
      return res.status(400).json({ error: 'At least 2 participants required' });
    }
    
    const pool = await getPool();
    const conversationType = isGroup ? 'GROUP' : 'DIRECT';
    
    // For 1-on-1 conversations, check if one already exists
    if (!isGroup && participants.length === 2) {
      const existingConv = await pool.query(`
        SELECT c.id, c.title, c.type, c.created_at, c.updated_at
        FROM conversation c
        WHERE c.type = 'DIRECT'
        AND EXISTS (
          SELECT 1 FROM conversation_participant cp1 
          WHERE cp1.conversation_id = c.id AND cp1.user_id = $1 AND cp1.left_at IS NULL
        )
        AND EXISTS (
          SELECT 1 FROM conversation_participant cp2 
          WHERE cp2.conversation_id = c.id AND cp2.user_id = $2 AND cp2.left_at IS NULL
        )
        AND (
          SELECT COUNT(*) FROM conversation_participant cp 
          WHERE cp.conversation_id = c.id AND cp.left_at IS NULL
        ) = 2
      `, participants);
      
      if (existingConv.rows.length > 0) {
        return res.json(existingConv.rows[0]);
      }
    }
    
    // Create new conversation
    const convResult = await pool.query(`
      INSERT INTO conversation (title, type, created_at, updated_at)
      VALUES ($1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [title, conversationType]);
    
    const conversationId = convResult.rows[0].id;
    
    // Add participants
    for (const userId of participants) {
      await pool.query(`
        INSERT INTO conversation_participant (conversation_id, user_id, joined_at)
        VALUES ($1, $2, CURRENT_TIMESTAMP)
      `, [conversationId, userId]);
    }
    
    res.status(201).json(convResult.rows[0]);
    
  } catch (error) {
    console.error('Error creating conversation:', error);
    res.status(500).json({ error: 'Failed to create conversation' });
  }
});

// Get user's conversations
router.get('/conversations/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 20, offset = 0 } = req.query;
    
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        c.id,
        c.title,
        c.type,
        c.created_at,
        c.updated_at,
        cp.is_archived,
        lm.content as last_message_content,
        lm.created_at as last_message_time,
        lm.content_type as last_message_type,
        sender.first_name as last_sender_first_name,
        sender.last_name as last_sender_last_name,
        (
          SELECT COUNT(*) 
          FROM message m2 
          WHERE m2.conversation_id = c.id 
          AND m2.created_at > COALESCE(cp.last_read_at, '1970-01-01')
          AND m2.sender_id != $1
        ) as unread_count,
        (
          SELECT json_agg(
            json_build_object(
              'id', u.id,
              'firstName', u.first_name,
              'lastName', u.last_name,
              'username', u.username,
              'avatarUrl', u.avatar_url
            )
          )
          FROM conversation_participant cp2
          JOIN "user" u ON cp2.user_id = u.id
          WHERE cp2.conversation_id = c.id 
          AND cp2.left_at IS NULL
          AND cp2.user_id != $1
        ) as other_participants
      FROM conversation c
      JOIN conversation_participant cp ON c.id = cp.conversation_id
      LEFT JOIN LATERAL (
        SELECT m.content, m.created_at, m.content_type, m.sender_id
        FROM message m 
        WHERE m.conversation_id = c.id
        ORDER BY m.created_at DESC
        LIMIT 1
      ) lm ON true
      LEFT JOIN "user" sender ON lm.sender_id = sender.id
      WHERE cp.user_id = $1 
      AND cp.left_at IS NULL
      ORDER BY lm.created_at DESC NULLS LAST
      LIMIT $2 OFFSET $3
    `, [userId, limit, offset]);
    
    // Return in the format expected by the frontend service
    res.json({ 
      conversations: result.rows.map(row => ({
        id: row.id,
        title: row.title,
        type: row.type,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        isArchived: row.is_archived || false,
        unreadCount: parseInt(row.unread_count) || 0,
        participants: row.other_participants || [],
        lastMessage: row.last_message_content ? {
          content: row.last_message_content,
          timestamp: row.last_message_time,
          contentType: row.last_message_type,
          sender: {
            firstName: row.last_sender_first_name,
            lastName: row.last_sender_last_name
          }
        } : null
      }))
    });
    
  } catch (error) {
    console.error('Error getting conversations:', error);
    res.status(500).json({ error: 'Failed to get conversations' });
  }
});

// Send message
router.post('/send', async (req, res) => {
  try {
    const { conversationId, senderId, content, messageType = 'TEXT', attachmentUrl } = req.body;
    
    if (!conversationId || !senderId || !content) {
      return res.status(400).json({ error: 'Conversation ID, sender ID, and content are required' });
    }
    
    const pool = await getPool();
    
    // Insert message
    const result = await pool.query(`
      INSERT INTO message (conversation_id, sender_id, content, content_type, attachment_url, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [conversationId, senderId, content, messageType, attachmentUrl]);
    
    const message = result.rows[0];
    
    // Update conversation timestamp
    await pool.query(
      'UPDATE conversation SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [conversationId]
    );
    
    // Get sender info for broadcasting
    const senderResult = await pool.query(
      'SELECT first_name, last_name, username, avatar_url FROM "user" WHERE id = $1',
      [senderId]
    );
    
    const messageWithSender = {
      ...message,
      sender: senderResult.rows[0]
    };
    
    // Broadcast to conversation participants
    await broadcastToConversation(conversationId, {
      type: 'new_message',
      message: messageWithSender
    }, senderId);
    
    res.status(201).json(messageWithSender);
    
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
});

// Get conversation messages
router.get('/conversations/:conversationId/messages', async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { limit = 30, offset = 0, beforeId } = req.query;
    
    const pool = await getPool();
    
    let query = `
      SELECT 
        m.*,
        u.first_name, u.last_name, u.username, u.avatar_url,
        COALESCE(
          (
            SELECT json_agg(
              json_build_object(
                'emoji', mr.emoji,
                'count', mr.count,
                'users', mr.user_list
              )
            )
            FROM (
              SELECT 
                mr.emoji,
                COUNT(*) as count,
                json_agg(
                  json_build_object(
                    'id', u2.id,
                    'firstName', u2.first_name,
                    'lastName', u2.last_name
                  )
                ) as user_list
              FROM message_reactions mr
              JOIN "user" u2 ON mr.user_id = u2.id
              WHERE mr.message_id = m.id
              GROUP BY mr.emoji
            ) mr
          ),
          '[]'::json
        ) as reactions
      FROM message m
      JOIN "user" u ON m.sender_id = u.id
      WHERE m.conversation_id = $1
    `;
    
    const params = [conversationId];
    
    if (beforeId) {
      query += ` AND m.id < $${params.length + 1}`;
      params.push(beforeId);
    }
    
    query += ` ORDER BY m.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit, offset);
    
    const result = await pool.query(query, params);
    
    // Return in the format expected by the frontend service
    res.json({
      messages: result.rows.reverse().map(row => ({
        id: row.id,
        content: row.content,
        contentType: row.content_type,
        attachmentUrl: row.attachment_url,
        pinId: row.pin_id,
        tradeId: row.trade_id,
        status: 'sent', // Default status
        isEdited: false,
        editedAt: null,
        isDeleted: false,
        deletedAt: null,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        sender: {
          id: row.sender_id,
          username: row.username,
          displayName: `${row.first_name} ${row.last_name}`.trim()
        },
        reactions: row.reactions || []
      }))
    });
    
  } catch (error) {
    console.error('Error getting messages:', error);
    res.status(500).json({ error: 'Failed to get messages' });
  }
});

// Archive conversation
router.put('/conversations/:conversationId/archive/:userId', async (req, res) => {
  try {
    const { conversationId, userId } = req.params;
    const pool = await getPool();
    
    await pool.query(
      'UPDATE conversation_participant SET is_archived = true WHERE conversation_id = $1 AND user_id = $2',
      [conversationId, userId]
    );
    
    res.json({ success: true, message: 'Conversation archived' });
    
  } catch (error) {
    console.error('Error archiving conversation:', error);
    res.status(500).json({ error: 'Failed to archive conversation' });
  }
});

// Unarchive conversation
router.put('/conversations/:conversationId/unarchive/:userId', async (req, res) => {
  try {
    const { conversationId, userId } = req.params;
    const pool = await getPool();
    
    await pool.query(
      'UPDATE conversation_participant SET is_archived = false WHERE conversation_id = $1 AND user_id = $2',
      [conversationId, userId]
    );
    
    res.json({ success: true, message: 'Conversation unarchived' });
    
  } catch (error) {
    console.error('Error unarchiving conversation:', error);
    res.status(500).json({ error: 'Failed to unarchive conversation' });
  }
});

// Mark conversation as read (PUT method)
router.put('/conversations/:conversationId/read', async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    const pool = await getPool();
    
    await pool.query(
      'UPDATE conversation_participant SET last_read_at = CURRENT_TIMESTAMP WHERE conversation_id = $1 AND user_id = $2',
      [conversationId, userId]
    );
    
    res.json({ success: true, message: 'Conversation marked as read' });
    
  } catch (error) {
    console.error('Error marking conversation as read:', error);
    res.status(500).json({ error: 'Failed to mark conversation as read' });
  }
});

// Mark conversation as read (POST method - for compatibility with messageStore)
router.post('/conversations/:conversationId/read', async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    const pool = await getPool();
    
    await pool.query(
      'UPDATE conversation_participant SET last_read_at = CURRENT_TIMESTAMP WHERE conversation_id = $1 AND user_id = $2',
      [conversationId, userId]
    );
    
    res.json({ success: true, message: 'Conversation marked as read' });
    
  } catch (error) {
    console.error('Error marking conversation as read:', error);
    res.status(500).json({ error: 'Failed to mark conversation as read' });
  }
});

// Delete conversation
router.delete('/conversations/:conversationId/user/:userId', async (req, res) => {
  try {
    const { conversationId, userId } = req.params;
    const pool = await getPool();
    
    // Mark user as left
    await pool.query(
      'UPDATE conversation_participant SET left_at = CURRENT_TIMESTAMP WHERE conversation_id = $1 AND user_id = $2',
      [conversationId, userId]
    );
    
    // Check if all participants have left
    const remainingResult = await pool.query(
      'SELECT COUNT(*) as count FROM conversation_participant WHERE conversation_id = $1 AND left_at IS NULL',
      [conversationId]
    );
    
    if (remainingResult.rows[0].count === 0) {
      // Delete the entire conversation if no participants remain
      await pool.query('DELETE FROM conversation WHERE id = $1', [conversationId]);
    }
    
    res.json({ success: true, message: 'Conversation deleted' });
    
  } catch (error) {
    console.error('Error deleting conversation:', error);
    res.status(500).json({ error: 'Failed to delete conversation' });
  }
});

export default router; 