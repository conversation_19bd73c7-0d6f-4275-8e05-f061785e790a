import express from 'express';
import { getPool } from '../config/database.js';
import { upload } from '../middleware/upload.js';
import heicConvert from 'heic-convert';
import fs from 'fs';
import path from 'path';

const router = express.Router();

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    const pool = await getPool();
    
    // Test database connection
    const dbResult = await pool.query('SELECT NOW() as timestamp');
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: {
          status: 'connected',
          timestamp: dbResult.rows[0].timestamp
        },
        server: {
          status: 'running',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: process.version
        }
      }
    };
    
    res.json(health);
    
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      services: {
        database: {
          status: 'disconnected',
          error: error.message
        },
        server: {
          status: 'running',
          uptime: process.uptime()
        }
      }
    });
  }
});

// Convert HEIC to JPEG
router.post('/convert-heic', upload.single('heicFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No HEIC file uploaded' });
    }
    
    const { originalname, path: filePath, mimetype } = req.file;
    
    // Check if file is HEIC
    if (mimetype !== 'image/heic' && !originalname.toLowerCase().endsWith('.heic')) {
      return res.status(400).json({ error: 'File must be HEIC format' });
    }
    
    // Read HEIC file
    const heicBuffer = fs.readFileSync(filePath);
    
    // Convert to JPEG
    const jpegBuffer = await heicConvert({
      buffer: heicBuffer,
      format: 'JPEG',
      quality: 0.9
    });
    
    // Generate output filename
    const outputFilename = originalname.replace(/\.heic$/i, '.jpg');
    const outputPath = path.join('uploads/converted', outputFilename);
    
    // Ensure output directory exists
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Save converted file
    fs.writeFileSync(outputPath, jpegBuffer);
    
    // Clean up original file
    fs.unlinkSync(filePath);
    
    res.json({
      success: true,
      originalFile: originalname,
      convertedFile: outputFilename,
      convertedPath: outputPath,
      size: jpegBuffer.length,
      message: 'HEIC file converted to JPEG successfully'
    });
    
  } catch (error) {
    console.error('Error converting HEIC file:', error);
    
    // Clean up files on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    res.status(500).json({ error: 'Failed to convert HEIC file' });
  }
});

// File upload endpoint
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // In a real implementation, you would:
    // 1. Move the file to a permanent location (S3, CDN, etc.)
    // 2. Generate a public URL
    // 3. Possibly resize/optimize images
    // 4. Store file metadata in database

    const fileInfo = {
      originalName: req.file.originalname,
      filename: req.file.filename,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: req.file.path,
      url: `/uploads/temp/${req.file.filename}` // Temporary URL
    };

    res.json({
      success: true,
      file: fileInfo,
      message: 'File uploaded successfully'
    });

  } catch (error) {
    console.error('Error uploading file:', error);
    res.status(500).json({ error: 'Failed to upload file' });
  }
});

// System info endpoint (for debugging)
router.get('/system-info', (req, res) => {
  const info = {
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    env: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString()
  };

  res.json(info);
});

// Error test endpoint (for testing error handling)
router.get('/error-test', (req, res) => {
  throw new Error('This is a test error');
});

// Simple test endpoint
router.get('/test', (req, res) => {
  res.json({
    message: 'PinPal API is working!',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

export default router; 