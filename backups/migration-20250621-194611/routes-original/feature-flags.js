import express from 'express';
import { getPool } from '../config/database.js';

const router = express.Router();

// GET /api/feature-flags/user/:userId
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    const pool = await getPool();
    
    // Get user's feature flags
    const result = await pool.query(`
      SELECT 
        ff.flag_name,
        ff.is_enabled,
        ff.description,
        ff.created_at,
        ff.updated_at
      FROM user_feature_flags uff
      JOIN feature_flags ff ON uff.flag_name = ff.flag_name
      WHERE uff.user_id = $1 AND ff.is_active = true
      ORDER BY ff.flag_name
    `, [userId]);

    // Get default feature flags for features not specifically set for user
    const defaultResult = await pool.query(`
      SELECT 
        ff.flag_name,
        ff.default_value as is_enabled,
        ff.description,
        ff.created_at,
        ff.updated_at
      FROM feature_flags ff
      WHERE ff.is_active = true 
      AND ff.flag_name NOT IN (
        SELECT flag_name FROM user_feature_flags WHERE user_id = $1
      )
      ORDER BY ff.flag_name
    `, [userId]);

    // Combine user-specific and default flags
    const allFlags = [
      ...result.rows,
      ...defaultResult.rows
    ];

    // Convert to object format for easier frontend consumption
    const flagsObject = allFlags.reduce((acc, flag) => {
      acc[flag.flag_name] = {
        enabled: flag.is_enabled,
        description: flag.description,
        updatedAt: flag.updated_at
      };
      return acc;
    }, {});

    res.json({
      userId,
      flags: flagsObject,
      totalFlags: allFlags.length
    });

  } catch (error) {
    console.error('Error getting user feature flags:', error);
    res.status(500).json({ error: 'Failed to get feature flags' });
  }
});

// PUT /api/feature-flags/user/:userId
router.put('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { flagName, isEnabled } = req.body;

    if (!flagName || typeof isEnabled !== 'boolean') {
      return res.status(400).json({ 
        error: 'Flag name and isEnabled (boolean) are required' 
      });
    }

    const pool = await getPool();
    
    // Verify the feature flag exists and is active
    const flagCheck = await pool.query(
      'SELECT * FROM feature_flags WHERE flag_name = $1 AND is_active = true',
      [flagName]
    );

    if (flagCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Feature flag not found or inactive' });
    }

    // Check if user is admin (only admins can modify feature flags)
    const adminCheck = await pool.query(
      'SELECT * FROM "user" WHERE uid = $1 AND role = $2',
      [userId, 'admin']
    );

    if (adminCheck.rows.length === 0) {
      return res.status(403).json({ 
        error: 'Only administrators can modify feature flags' 
      });
    }

    // Update or insert user feature flag
    const result = await pool.query(`
      INSERT INTO user_feature_flags (user_id, flag_name, is_enabled, created_at, updated_at)
      VALUES ($1, $2, $3, NOW(), NOW())
      ON CONFLICT (user_id, flag_name) 
      DO UPDATE SET 
        is_enabled = EXCLUDED.is_enabled,
        updated_at = NOW()
      RETURNING *
    `, [userId, flagName, isEnabled]);

    res.json({
      message: 'Feature flag updated successfully',
      flag: result.rows[0]
    });

  } catch (error) {
    console.error('Error updating user feature flag:', error);
    res.status(500).json({ error: 'Failed to update feature flag' });
  }
});

// GET /api/feature-flags (Admin only - get all feature flags)
router.get('/', async (req, res) => {
  try {
    const { adminUserId } = req.query;

    if (!adminUserId) {
      return res.status(400).json({ error: 'Admin user ID is required' });
    }

    const pool = await getPool();
    
    // Check if user is admin
    const adminCheck = await pool.query(
      'SELECT * FROM "user" WHERE uid = $1 AND role = $2',
      [adminUserId, 'admin']
    );

    if (adminCheck.rows.length === 0) {
      return res.status(403).json({ 
        error: 'Only administrators can view all feature flags' 
      });
    }

    // Get all feature flags with usage statistics
    const result = await pool.query(`
      SELECT 
        ff.*,
        COUNT(DISTINCT uff.user_id) as users_with_override,
        COUNT(DISTINCT CASE WHEN uff.is_enabled = true THEN uff.user_id END) as users_enabled,
        COUNT(DISTINCT CASE WHEN uff.is_enabled = false THEN uff.user_id END) as users_disabled
      FROM feature_flags ff
      LEFT JOIN user_feature_flags uff ON ff.flag_name = uff.flag_name
      GROUP BY ff.flag_name, ff.default_value, ff.description, ff.is_active, ff.created_at, ff.updated_at
      ORDER BY ff.flag_name
    `);

    res.json({
      featureFlags: result.rows,
      total: result.rows.length
    });

  } catch (error) {
    console.error('Error getting all feature flags:', error);
    res.status(500).json({ error: 'Failed to get feature flags' });
  }
});

// POST /api/feature-flags (Admin only - create new feature flag)
router.post('/', async (req, res) => {
  try {
    const { adminUserId, flagName, description, defaultValue = false } = req.body;

    if (!adminUserId || !flagName) {
      return res.status(400).json({ 
        error: 'Admin user ID and flag name are required' 
      });
    }

    const pool = await getPool();
    
    // Check if user is admin
    const adminCheck = await pool.query(
      'SELECT * FROM "user" WHERE uid = $1 AND role = $2',
      [adminUserId, 'admin']
    );

    if (adminCheck.rows.length === 0) {
      return res.status(403).json({ 
        error: 'Only administrators can create feature flags' 
      });
    }

    // Validate flag name format (alphanumeric, underscores, hyphens)
    if (!/^[a-zA-Z0-9_-]+$/.test(flagName)) {
      return res.status(400).json({ 
        error: 'Flag name can only contain letters, numbers, underscores, and hyphens' 
      });
    }

    // Create new feature flag
    const result = await pool.query(`
      INSERT INTO feature_flags (flag_name, description, default_value, is_active, created_at, updated_at)
      VALUES ($1, $2, $3, true, NOW(), NOW())
      RETURNING *
    `, [flagName, description || '', defaultValue]);

    res.status(201).json({
      message: 'Feature flag created successfully',
      featureFlag: result.rows[0]
    });

  } catch (error) {
    if (error.code === '23505') { // Unique constraint violation
      return res.status(409).json({ error: 'Feature flag already exists' });
    }
    
    console.error('Error creating feature flag:', error);
    res.status(500).json({ error: 'Failed to create feature flag' });
  }
});

// Get feature flag value for a specific user
router.get('/:key/user/:userId', async (req, res) => {
  try {
    const { key, userId } = req.params;
    const { logUsage = 'true', sessionId } = req.query;
    
    const pool = await getPool();
    
    // Get flag value using PostgreSQL function
    const result = await pool.query(
      'SELECT get_feature_flag_value($1, $2) as value',
      [key, userId]
    );
    
    const value = result.rows[0]?.value || 'false';
    
    // Log usage if requested
    if (logUsage === 'true') {
      await pool.query(
        'SELECT log_feature_flag_usage($1, $2, $3, $4, $5)',
        [key, userId, value, JSON.stringify({ endpoint: 'single-flag' }), sessionId]
      );
    }
    
    res.json({
      key,
      value: value === 'true',
      userId,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error getting feature flag:', error);
    res.status(500).json({ error: 'Failed to get feature flag' });
  }
});

// Get multiple feature flag values for a user (batch)
router.post('/user/:userId/batch', async (req, res) => {
  try {
    const { userId } = req.params;
    const { keys, logUsage = true, sessionId } = req.body;
    
    if (!Array.isArray(keys) || keys.length === 0) {
      return res.status(400).json({ error: 'Keys array is required' });
    }
    
    const pool = await getPool();
    const results = {};
    
    // Get values for all requested flags
    for (const key of keys) {
      const result = await pool.query(
        'SELECT get_feature_flag_value($1, $2) as value',
        [key, userId]
      );
      
      const value = result.rows[0]?.value || 'false';
      results[key] = value === 'true';
      
      // Log usage if requested
      if (logUsage) {
        await pool.query(
          'SELECT log_feature_flag_usage($1, $2, $3, $4, $5)',
          [key, userId, value, JSON.stringify({ endpoint: 'batch' }), sessionId]
        );
      }
    }
    
    res.json({
      userId,
      flags: results,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error getting feature flags batch:', error);
    res.status(500).json({ error: 'Failed to get feature flags' });
  }
});

// Set feature flag override for a user
router.post('/user/:userId/override', async (req, res) => {
  try {
    const { userId } = req.params;
    const { key, value, expiresAt } = req.body;
    
    if (!key || value === undefined) {
      return res.status(400).json({ error: 'Key and value are required' });
    }
    
    const pool = await getPool();
    
    await pool.query(`
      INSERT INTO feature_flag_user_overrides (flag_key, user_id, value, expires_at, created_at)
      VALUES ($1, $2, $3, $4, NOW())
      ON CONFLICT (flag_key, user_id) DO UPDATE SET
        value = EXCLUDED.value,
        expires_at = EXCLUDED.expires_at,
        updated_at = NOW()
    `, [key, userId, value.toString(), expiresAt || null]);
    
    res.json({
      success: true,
      key,
      userId,
      value,
      expiresAt
    });
    
  } catch (error) {
    console.error('Error setting feature flag override:', error);
    res.status(500).json({ error: 'Failed to set feature flag override' });
  }
});

// Remove feature flag override for a user
router.delete('/user/:userId/override/:key', async (req, res) => {
  try {
    const { userId, key } = req.params;
    
    const pool = await getPool();
    
    const result = await pool.query(
      'DELETE FROM feature_flag_user_overrides WHERE flag_key = $1 AND user_id = $2 RETURNING *',
      [key, userId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Override not found' });
    }
    
    res.json({
      success: true,
      message: 'Override removed successfully'
    });
    
  } catch (error) {
    console.error('Error removing feature flag override:', error);
    res.status(500).json({ error: 'Failed to remove feature flag override' });
  }
});

// Check if user has specific feature access
router.get('/:key/user/:userId/check', async (req, res) => {
  try {
    const { key, userId } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(
      'SELECT get_feature_flag_value($1, $2) as value',
      [key, userId]
    );
    
    const hasAccess = result.rows[0]?.value === 'true';
    
    res.json({
      key,
      userId,
      hasAccess,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error checking feature access:', error);
    res.status(500).json({ error: 'Failed to check feature access' });
  }
});

// Get all feature flags for a user
router.get('/user/:userId/all', async (req, res) => {
  try {
    const { userId } = req.params;
    const pool = await getPool();
    
    // Get all active feature flags
    const flagsResult = await pool.query(`
      SELECT key FROM feature_flags 
      WHERE is_active = true
      ORDER BY key
    `);
    
    const flags = {};
    
    // Get value for each flag
    for (const flag of flagsResult.rows) {
      const result = await pool.query(
        'SELECT get_feature_flag_value($1, $2) as value',
        [flag.key, userId]
      );
      
      flags[flag.key] = result.rows[0]?.value === 'true';
    }
    
    res.json({
      userId,
      flags,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error getting all feature flags:', error);
    res.status(500).json({ error: 'Failed to get feature flags' });
  }
});

export default router; 