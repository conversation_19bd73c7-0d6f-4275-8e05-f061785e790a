import express from 'express';
import { getPool } from '../config/database.js';

const router = express.Router();

// Rate limiting for user search
const searchRateLimit = new Map();

function isRateLimited(term) {
  if (term.length < 3) return false;
  
  const key = `search_${term.toLowerCase()}`;
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute
  const maxRequests = 10;
  
  if (!searchRateLimit.has(key)) {
    searchRateLimit.set(key, { count: 1, resetTime: now + windowMs });
    return false;
  }
  
  const data = searchRateLimit.get(key);
  if (now > data.resetTime) {
    searchRateLimit.set(key, { count: 1, resetTime: now + windowMs });
    return false;
  }
  
  if (data.count >= maxRequests) {
    return true;
  }
  
  data.count++;
  return false;
}

// Ensure user exists in both PostgreSQL and Firestore
router.post('/ensure', async (req, res) => {
  try {
    const { id, email, username, displayName } = req.body;
    
    if (!id || !email || !username || !displayName) {
      return res.status(400).json({ 
        error: 'Missing required fields: id, email, username, displayName' 
      });
    }
    
    console.log('🔄 Ensuring user exists in both PostgreSQL and Firestore:', { id, email, username, displayName });
    
    const pool = await getPool();
    
    const query = `
      INSERT INTO "user" (id, email, username, display_name, created_at, updated_at) 
      VALUES ($1, $2, $3, $4, NOW(), NOW()) 
      ON CONFLICT (id) DO NOTHING;
    `;
    
    const values = [id, email, username, displayName];
    await pool.query(query, values);
    
    console.log('✅ User ensured in PostgreSQL:', id);
    
    // Ensure user exists in Firestore for search functionality
    try {
      const { adminFirestore } = await import('../../src/services/firebaseAdmin.js');
      
      const userDoc = await adminFirestore.collection('users').doc(id).get();
      
      if (!userDoc.exists) {
        const nameParts = displayName.split(' ');
        const firestoreUserData = {
          firstName: nameParts[0] || 'User',
          lastName: nameParts.slice(1).join(' ') || '',
          email: email,
          username: username,
          displayName: displayName,
          avatarUrl: '',
          bio: '',
          location: '',
          phoneNumber: '',
          emailVerified: true,
          isActive: true,
          isVerified: false,
          role: 'user',
          status: 'active',
          preferences: {
            notificationsEnabled: true,
            publicProfile: true,
            showLocation: false,
            showEmail: false,
            allowMessages: true,
            allowComments: true
          },
          stats: {
            pinsCount: 0,
            boardsCount: 0,
            followersCount: 0,
            followingCount: 0,
            checkInsCount: 0,
            tradesCompletedCount: 0,
            likesReceivedCount: 0
          },
          statistics: {
            totalPins: 0,
            totalTrades: 0,
            totalCheckIns: 0,
            joinedAt: new Date()
          },
          moderationHistory: [],
          joinedAt: new Date(),
          createdAt: new Date(),
          lastLoginAt: new Date(),
          updatedAt: new Date()
        };
        
        await adminFirestore.collection('users').doc(id).set(firestoreUserData);
        console.log('✅ User created in Firestore:', id);
      } else {
        console.log('✅ User already exists in Firestore:', id);
      }
      
    } catch (firestoreError) {
      console.warn('⚠️ Failed to sync user with Firestore (PostgreSQL user created):', firestoreError);
    }
    
    res.status(200).json({ 
      success: true, 
      message: 'User ensured in both PostgreSQL and Firestore',
      userId: id 
    });
    
  } catch (error) {
    console.error('❌ Error ensuring user:', error);
    res.status(500).json({ 
      error: 'Failed to ensure user in database',
      details: error.message 
    });
  }
});

// Check if user exists
router.get('/:id/exists', async (req, res) => {
  try {
    const { id } = req.params;
    
    const pool = await getPool();
    const query = 'SELECT id FROM "user" WHERE id = $1';
    const result = await pool.query(query, [id]);
    
    const exists = result.rows.length > 0;
    
    res.status(200).json({ 
      exists,
      userId: id 
    });
    
  } catch (error) {
    console.error('❌ Error checking user existence:', error);
    res.status(500).json({ 
      error: 'Failed to check user existence',
      details: error.message 
    });
  }
});

// Search users with anti-spam protection
router.get('/search', async (req, res) => {
  try {
    const { q: query, currentUserId } = req.query;
    
    if (!query || query.trim().length < 3) {
      return res.status(400).json({ error: 'Query must be at least 3 characters' });
    }
    
    const searchTerm = query.trim();
    
    if (isRateLimited(searchTerm)) {
      return res.status(429).json({ error: 'Rate limit exceeded. Please wait before searching again.' });
    }
    
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT id, first_name, last_name, username, avatar_url
      FROM "user"
      WHERE (
        LOWER(first_name) LIKE LOWER($1) OR
        LOWER(last_name) LIKE LOWER($1) OR
        LOWER(username) LIKE LOWER($1)
      )
      AND id != $2
      ORDER BY 
        CASE 
          WHEN LOWER(username) = LOWER($3) THEN 1
          WHEN LOWER(first_name) = LOWER($3) THEN 2
          WHEN LOWER(last_name) = LOWER($3) THEN 3
          ELSE 4
        END
      LIMIT 10
    `, [`%${searchTerm}%`, currentUserId, searchTerm]);
    
    res.json(result.rows);
    
  } catch (error) {
    console.error('Error searching users:', error);
    res.status(500).json({ error: 'Failed to search users' });
  }
});

// Get user by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const pool = await getPool();
    const query = 'SELECT * FROM "user" WHERE id = $1';
    const result = await pool.query(query, [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ 
        error: 'User not found',
        userId: id 
      });
    }
    
    const user = result.rows[0];
    
    // Load privacy settings
    let privacySettings = null;
    try {
      const privacyResult = await pool.query(
        'SELECT * FROM privacy_settings WHERE user_id = $1',
        [id]
      );
      
      if (privacyResult.rows.length > 0) {
        const row = privacyResult.rows[0];
        privacySettings = {
          publicProfile: row.public_profile,
          showLocation: row.show_location,
          allowMessages: row.allow_messages,
          allowComments: row.allow_comments,
        };
      }
    } catch (error) {
      console.warn('⚠️ Failed to load privacy settings for user:', id, error);
    }
    
    // Convert PostgreSQL user to expected format
    const userData = {
      id: user.id,
      email: user.email,
      username: user.username,
      firstName: user.first_name,
      lastName: user.last_name,
      displayName: user.display_name,
      avatarUrl: user.avatar_url,
      bio: user.bio,
      location: user.location,
      phoneNumber: user.phone_number,
      emailVerified: user.email_verified,
      isActive: user.is_active,
      isVerified: user.is_verified,
      role: user.role,
      status: user.status,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      lastLoginAt: user.created_at,
      preferences: {
        notificationsEnabled: true,
        showEmail: false,
        ...(privacySettings && {
          publicProfile: privacySettings.publicProfile,
          showLocation: privacySettings.showLocation,
          allowMessages: privacySettings.allowMessages,
          allowComments: privacySettings.allowComments,
        }),
        ...(user.preferences || {})
      },
      stats: user.stats || {},
    };
    
    res.status(200).json(userData);
    
  } catch (error) {
    console.error('❌ Error fetching user:', error);
    res.status(500).json({ 
      error: 'Failed to fetch user',
      details: error.message 
    });
  }
});

// Get notification settings
router.get('/settings/notifications', async (req, res) => {
  try {
    const { userId } = req.query;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const pool = await getPool();
    const result = await pool.query(
      'SELECT * FROM notifications_settings WHERE user_id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      const defaultSettings = {
        userId,
        globalNotifications: true,
        newMessages: true,
        tradeRequests: true,
        commentsPush: true,
        reactionsPush: false,
        mentionsPush: true,
        newPinsFromTraders: true,
      };
      return res.status(200).json(defaultSettings);
    }

    const row = result.rows[0];
    const settings = {
      userId: row.user_id,
      globalNotifications: row.global_notifications,
      newMessages: row.new_messages,
      tradeRequests: row.trade_requests,
      commentsPush: row.comments_push,
      reactionsPush: row.reactions_push,
      mentionsPush: row.mentions_push,
      newPinsFromTraders: row.new_pins_from_traders,
    };

    res.status(200).json(settings);
  } catch (error) {
    console.error('❌ Error getting notification settings:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update notification settings
router.post('/settings/notifications', async (req, res) => {
  try {
    const { userId, ...settings } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const pool = await getPool();
    const query = `
      INSERT INTO notifications_settings (
        user_id, global_notifications, new_messages, trade_requests,
        comments_push, reactions_push, mentions_push, new_pins_from_traders
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (user_id) DO UPDATE SET
        global_notifications = EXCLUDED.global_notifications,
        new_messages = EXCLUDED.new_messages,
        trade_requests = EXCLUDED.trade_requests,
        comments_push = EXCLUDED.comments_push,
        reactions_push = EXCLUDED.reactions_push,
        mentions_push = EXCLUDED.mentions_push,
        new_pins_from_traders = EXCLUDED.new_pins_from_traders,
        updated_at = CURRENT_TIMESTAMP
      RETURNING *;
    `;

    const values = [
      userId,
      settings.globalNotifications,
      settings.newMessages,
      settings.tradeRequests,
      settings.commentsPush,
      settings.reactionsPush,
      settings.mentionsPush,
      settings.newPinsFromTraders,
    ];

    const result = await pool.query(query, values);
    const row = result.rows[0];

    const savedSettings = {
      userId: row.user_id,
      globalNotifications: row.global_notifications,
      newMessages: row.new_messages,
      tradeRequests: row.trade_requests,
      commentsPush: row.comments_push,
      reactionsPush: row.reactions_push,
      mentionsPush: row.mentions_push,
      newPinsFromTraders: row.new_pins_from_traders,
    };

    res.status(200).json(savedSettings);
  } catch (error) {
    console.error('❌ Error saving notification settings:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get privacy settings
router.get('/settings/privacy', async (req, res) => {
  try {
    const { userId } = req.query;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const pool = await getPool();
    const result = await pool.query(
      'SELECT * FROM privacy_settings WHERE user_id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      const defaultSettings = {
        userId,
        publicProfile: true,
        showLocation: true,
        allowMessages: true,
        allowComments: true,
      };
      return res.status(200).json(defaultSettings);
    }

    const row = result.rows[0];
    const settings = {
      userId: row.user_id,
      publicProfile: row.public_profile,
      showLocation: row.show_location,
      allowMessages: row.allow_messages,
      allowComments: row.allow_comments,
    };

    res.status(200).json(settings);
  } catch (error) {
    console.error('❌ Error getting privacy settings:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update privacy settings
router.post('/settings/privacy', async (req, res) => {
  try {
    const { userId, ...settings } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const pool = await getPool();
    const query = `
      INSERT INTO privacy_settings (
        user_id, public_profile, show_location, allow_messages, allow_comments
      ) VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (user_id) DO UPDATE SET
        public_profile = EXCLUDED.public_profile,
        show_location = EXCLUDED.show_location,
        allow_messages = EXCLUDED.allow_messages,
        allow_comments = EXCLUDED.allow_comments,
        updated_at = CURRENT_TIMESTAMP
      RETURNING *;
    `;

    const values = [
      userId,
      settings.publicProfile,
      settings.showLocation,
      settings.allowMessages,
      settings.allowComments,
    ];

    const result = await pool.query(query, values);
    const row = result.rows[0];

    const savedSettings = {
      userId: row.user_id,
      publicProfile: row.public_profile,
      showLocation: row.show_location,
      allowMessages: row.allow_messages,
      allowComments: row.allow_comments,
    };

    res.status(200).json(savedSettings);
  } catch (error) {
    console.error('❌ Error saving privacy settings:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user by username
router.get('/by-username/:username', async (req, res) => {
  try {
    const { username } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        id, first_name, last_name, username, email, avatar_url, bio, 
        location, created_at, updated_at
      FROM "user"
      WHERE LOWER(username) = LOWER($1)
    `, [username]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json(result.rows[0]);
    
  } catch (error) {
    console.error('Error getting user by username:', error);
    res.status(500).json({ error: 'Failed to get user' });
  }
});

// Update user settings
router.put('/:userId/settings', async (req, res) => {
  try {
    const { userId } = req.params;
    const updates = req.body;
    
    const pool = await getPool();
    
    // Build dynamic update query
    const fields = Object.keys(updates);
    const values = Object.values(updates);
    const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');
    
    if (fields.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }
    
    await pool.query(`
      UPDATE "user" 
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [userId, ...values]);
    
    res.json({ success: true });
    
  } catch (error) {
    console.error('Error updating user settings:', error);
    res.status(500).json({ error: 'Failed to update user settings' });
  }
});

// Check username availability
router.get('/username/check', async (req, res) => {
  try {
    const { username, currentUserId } = req.query;
    
    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }
    
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT id FROM "user" 
      WHERE LOWER(username) = LOWER($1) AND id != $2
    `, [username, currentUserId || '']);
    
    res.json({
      available: result.rows.length === 0,
      username
    });
    
  } catch (error) {
    console.error('Error checking username:', error);
    res.status(500).json({ error: 'Failed to check username' });
  }
});

// Follow user
router.post('/:userId/follow', async (req, res) => {
  try {
    const { userId } = req.params;
    const { followerId } = req.body;
    
    if (!followerId) {
      return res.status(400).json({ error: 'Follower ID is required' });
    }
    
    if (userId === followerId) {
      return res.status(400).json({ error: 'Cannot follow yourself' });
    }
    
    const pool = await getPool();
    
    // Check if already following
    const existing = await pool.query(
      'SELECT id FROM user_follow WHERE follower_id = $1 AND following_id = $2',
      [followerId, userId]
    );
    
    if (existing.rows.length > 0) {
      return res.status(400).json({ error: 'Already following this user' });
    }
    
    // Create follow relationship
    await pool.query(
      'INSERT INTO user_follow (follower_id, following_id, created_at) VALUES ($1, $2, CURRENT_TIMESTAMP)',
      [followerId, userId]
    );
    
    res.json({ success: true, message: 'Successfully followed user' });
    
  } catch (error) {
    console.error('Error following user:', error);
    res.status(500).json({ error: 'Failed to follow user' });
  }
});

// Unfollow user
router.delete('/:userId/follow', async (req, res) => {
  try {
    const { userId } = req.params;
    const { followerId } = req.body;
    
    if (!followerId) {
      return res.status(400).json({ error: 'Follower ID is required' });
    }
    
    const pool = await getPool();
    
    const result = await pool.query(
      'DELETE FROM user_follow WHERE follower_id = $1 AND following_id = $2',
      [followerId, userId]
    );
    
    if (result.rowCount === 0) {
      return res.status(404).json({ error: 'Follow relationship not found' });
    }
    
    res.json({ success: true, message: 'Successfully unfollowed user' });
    
  } catch (error) {
    console.error('Error unfollowing user:', error);
    res.status(500).json({ error: 'Failed to unfollow user' });
  }
});

// Get follow statistics for a user
router.get('/:userId/follow-stats', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const pool = await getPool();
    
    // Get followers count
    const followersResult = await pool.query(
      'SELECT COUNT(*) as count FROM user_follow WHERE followed_id = $1',
      [userId]
    );
    
    // Get following count
    const followingResult = await pool.query(
      'SELECT COUNT(*) as count FROM user_follow WHERE follower_id = $1',
      [userId]
    );
    
    res.json({
      userId,
      followersCount: parseInt(followersResult.rows[0].count),
      followingCount: parseInt(followingResult.rows[0].count),
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error getting follow stats:', error);
    res.status(500).json({ error: 'Failed to get follow stats' });
  }
});

// Get user's saved pins (endpoint esperado pelo frontend)
router.get('/:userId/saved-pins', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 20, offset = 0 } = req.query;
    
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        p.*,
        sp.created_at as saved_at,
        (SELECT COUNT(*) FROM pin_likes pl WHERE pl.pin_id = p.id) as likes_count,
        (SELECT COUNT(*) FROM pin_comments pc WHERE pc.pin_id = p.id) as comments_count
      FROM saved_pins sp
      JOIN pin p ON sp.pin_id = p.id
      WHERE sp.user_id = $1
      ORDER BY sp.created_at DESC
      LIMIT $2 OFFSET $3
    `, [userId, limit, offset]);
    
    // CORREÇÃO: Mapear dados para camelCase para compatibilidade com frontend
    const pins = result.rows.map(row => ({
      id: row.id,
      name: row.title || 'Untitled Pin',
      image: row.image_url || '',  // CORREÇÃO: image_url → image
      description: row.description || '',
      origin: row.origin,
      releaseYear: row.release_year,
      originalPrice: row.original_price,
      pinNumber: row.pin_number,
      tradable: row.tradable || false,
      category: 'other',
      rarity: 'common',
      condition: 'excellent',
      year: row.release_year,
      series: row.origin,
      tags: [],
      isForTrade: row.tradable || false,
      likes: parseInt(row.likes_count) || 0,
      comments: parseInt(row.comments_count) || 0,
      isLiked: false, // TODO: Implementar verificação de like do usuário
      isSaved: true, // Todos os pins desta lista estão salvos
      userId: row.user_id,
      savedAt: row.saved_at,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      owner: {
        id: row.user_id,
        name: 'User', // TODO: Buscar dados reais do usuário
        avatar: null
      }
    }));
    
    console.log(`✅ Found ${pins.length} saved pins for user ${userId}`);
    res.json(pins);
    
  } catch (error) {
    console.error('Error getting saved pins:', error);
    res.status(500).json({ error: 'Failed to get saved pins' });
  }
});

// Get user's liked pins
router.get('/:userId/liked-pins', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 30, offset = 0 } = req.query;
    
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        p.id,
        p.title,
        p.image_url,
        p.description,
        p.origin,
        p.release_year,
        p.original_price,
        p.pin_number,
        p.tradable,
        p.is_public,
        p.likes_count,
        p.views_count,
        p.trades_count,
        p.user_id,
        p.created_at,
        p.updated_at,
        pl.created_at as liked_at,
        COALESCE(comment_count.count, 0) as comments_count,
        u.first_name,
        u.last_name,
        u.username,
        u.avatar_url
      FROM pin_likes pl
      JOIN pin p ON pl.pin_id = p.id
      LEFT JOIN "user" u ON p.user_id = u.id
      LEFT JOIN (
        SELECT pin_id, COUNT(*) as count 
        FROM pin_comments 
        GROUP BY pin_id
      ) comment_count ON p.id = comment_count.pin_id
      WHERE pl.user_id = $1 AND p.is_public = true
      ORDER BY pl.created_at DESC
      LIMIT $2 OFFSET $3
    `, [userId, limit, offset]);
    
    const pins = result.rows.map(row => ({
      id: row.id,
      name: row.title || 'Untitled Pin',
      title: row.title || 'Untitled Pin',
      image: row.image_url || '',
      imageUrl: row.image_url || '',
      description: row.description || '',
      origin: row.origin,
      series: row.origin,
      releaseYear: row.release_year,
      year: row.release_year,
      originalPrice: row.original_price,
      pinNumber: row.pin_number,
      tradable: row.tradable || false,
      isPublic: row.is_public,
      likes: parseInt(row.likes_count) || 0,
      likesCount: parseInt(row.likes_count) || 0,
      viewsCount: parseInt(row.views_count) || 0,
      tradesCount: parseInt(row.trades_count) || 0,
      comments: parseInt(row.comments_count) || 0,
      commentsCount: parseInt(row.comments_count) || 0,
      isLiked: true, // User liked this pin
      isSaved: false, // TODO: Check if user saved this pin
      userId: row.user_id,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      likedAt: row.liked_at,
      owner: {
        id: row.user_id,
        name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'Unknown',
        username: row.username,
        avatar: row.avatar_url
      }
    }));
    
    console.log(`✅ Found ${pins.length} liked pins for user ${userId}`);
    res.json(pins);
    
  } catch (error) {
    console.error('Error getting user liked pins:', error);
    res.status(500).json({ error: 'Failed to get liked pins' });
  }
});

// Get user's following list
router.get('/:userId/following', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 30, offset = 0 } = req.query;
    
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        u.id,
        u.first_name,
        u.last_name,
        u.username,
        u.avatar_url,
        u.bio,
        uf.created_at as followed_at,
        (SELECT COUNT(*) FROM user_follow WHERE followed_id = u.id) as followers_count,
        (SELECT COUNT(*) FROM user_follow WHERE follower_id = u.id) as following_count
      FROM user_follow uf
      JOIN "user" u ON uf.followed_id = u.id
      WHERE uf.follower_id = $1
      ORDER BY uf.created_at DESC
      LIMIT $2 OFFSET $3
    `, [userId, limit, offset]);
    
    const following = result.rows.map(row => ({
      id: row.id,
      name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'Unknown',
      username: row.username,
      avatar: row.avatar_url,
      bio: row.bio,
      followedAt: row.followed_at,
      followersCount: parseInt(row.followers_count) || 0,
      followingCount: parseInt(row.following_count) || 0
    }));
    
    console.log(`✅ Found ${following.length} following for user ${userId}`);
    res.json(following);
    
  } catch (error) {
    console.error('Error getting user following:', error);
    res.status(500).json({ error: 'Failed to get following list' });
  }
});

// Get user's recent pins
router.get('/:userId/recent-pins', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 5 } = req.query;
    
    const pool = await getPool();
    
    const result = await pool.query(`
      SELECT 
        p.id,
        p.title,
        p.image_url,
        p.description,
        p.origin,
        p.release_year,
        p.original_price,
        p.pin_number,
        p.tradable,
        p.is_public,
        p.likes_count,
        p.views_count,
        p.trades_count,
        p.user_id,
        p.created_at,
        p.updated_at,
        COALESCE(comment_count.count, 0) as comments_count,
        u.first_name,
        u.last_name,
        u.username,
        u.avatar_url
      FROM pin p
      LEFT JOIN "user" u ON p.user_id = u.id
      LEFT JOIN (
        SELECT pin_id, COUNT(*) as count 
        FROM pin_comments 
        GROUP BY pin_id
      ) comment_count ON p.id = comment_count.pin_id
      WHERE p.user_id = $1 AND p.is_public = true
      ORDER BY p.created_at DESC
      LIMIT $2
    `, [userId, limit]);
    
    const pins = result.rows.map(row => ({
      id: row.id,
      name: row.title || 'Untitled Pin',
      title: row.title || 'Untitled Pin',
      image: row.image_url || '',
      imageUrl: row.image_url || '',
      description: row.description || '',
      origin: row.origin,
      series: row.origin,
      releaseYear: row.release_year,
      year: row.release_year,
      originalPrice: row.original_price,
      pinNumber: row.pin_number,
      tradable: row.tradable || false,
      isPublic: row.is_public,
      likes: parseInt(row.likes_count) || 0,
      likesCount: parseInt(row.likes_count) || 0,
      viewsCount: parseInt(row.views_count) || 0,
      tradesCount: parseInt(row.trades_count) || 0,
      comments: parseInt(row.comments_count) || 0,
      commentsCount: parseInt(row.comments_count) || 0,
      userId: row.user_id,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      owner: {
        id: row.user_id,
        name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'Unknown',
        username: row.username,
        avatar: row.avatar_url
      }
    }));
    
    console.log(`✅ Found ${pins.length} recent pins for user ${userId}`);
    res.json(pins);
    
  } catch (error) {
    console.error('Error getting user recent pins:', error);
    res.status(500).json({ error: 'Failed to get recent pins' });
  }
});

// Get suggested follows for user
router.get('/:userId/suggested-follows', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 5 } = req.query;
    
    const pool = await getPool();
    
    // Simple algorithm: suggest users with most followers that current user doesn't follow
    const result = await pool.query(`
      SELECT 
        u.id,
        u.first_name,
        u.last_name,
        u.username,
        u.avatar_url,
        u.bio,
        (SELECT COUNT(*) FROM user_follow WHERE followed_id = u.id) as followers_count,
        (SELECT COUNT(*) FROM pin WHERE user_id = u.id AND is_public = true) as pins_count
      FROM "user" u
      WHERE u.id != $1
      AND u.id NOT IN (
        SELECT followed_id FROM user_follow WHERE follower_id = $1
      )
      ORDER BY followers_count DESC, pins_count DESC
      LIMIT $2
    `, [userId, limit]);
    
    const suggestions = result.rows.map(row => ({
      id: row.id,
      name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'Unknown',
      username: row.username,
      avatar: row.avatar_url,
      bio: row.bio,
      followersCount: parseInt(row.followers_count) || 0,
      pinsCount: parseInt(row.pins_count) || 0,
      reason: 'Popular user'
    }));
    
    console.log(`✅ Found ${suggestions.length} suggested follows for user ${userId}`);
    res.json(suggestions);
    
  } catch (error) {
    console.error('Error getting suggested follows:', error);
    res.status(500).json({ error: 'Failed to get suggested follows' });
  }
});

export default router; 