/**
 * New Place Photos Service
 * Uses the official Google Places API (New) for photo handling
 * Reference: https://developers.google.com/maps/documentation/places/web-service/place-photos
 */

export interface NewPlacePhoto {
  name: string; // Resource name: places/PLACE_ID/photos/PHOTO_REFERENCE
  photoUri: string; // Direct URL to the photo
  widthPx: number;
  heightPx: number;
  authorAttributions: Array<{
    displayName: string;
    uri?: string;
    photoUri?: string;
  }>;
}

export interface PlaceDetailsWithPhotos {
  id: string;
  displayName?: { text: string };
  formattedAddress?: string;
  photos?: NewPlacePhoto[];
  rating?: number;
  userRatingCount?: number;
  websiteUri?: string;
  internationalPhoneNumber?: string;
  location?: { latitude: number; longitude: number };
}

export class NewPlacePhotosService {
  private static instance: NewPlacePhotosService;

  private constructor() {}

  public static getInstance(): NewPlacePhotosService {
    if (!NewPlacePhotosService.instance) {
      NewPlacePhotosService.instance = new NewPlacePhotosService();
    }
    return NewPlacePhotosService.instance;
  }

  /**
   * Get place details with photos using the new API
   */
  public async getPlaceDetailsWithPhotos(placeId: string): Promise<PlaceDetailsWithPhotos | null> {
    try {
      const fields = [
        'id',
        'displayName',
        'formattedAddress',
        'photos',
        'rating',
        'userRatingCount',
        'websiteUri',
        'internationalPhoneNumber',
        'location'
      ];

      const url = `https://places.googleapis.com/v1/places/${placeId}`;
      
      console.log('🌐 Fetching place details with NEW API:', placeId);
      
      const response = await fetch(url, {
        headers: {
          'X-Goog-FieldMask': fields.join(','),
          'X-Goog-Api-Key': (import.meta as any).env.VITE_GOOGLE_MAPS_API_KEY,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Place Details API Error:', {
          status: response.status,
          error: errorText
        });
        return null;
      }

      const data = await response.json();
      
      // Process photos to generate proper URLs
      const processedPhotos: NewPlacePhoto[] = [];
      
      if (data.photos && Array.isArray(data.photos)) {
        for (const photo of data.photos) {
          if (photo.name) {
            const photoUrl = await this.getPhotoUrl(photo.name, 800);
            if (photoUrl) {
              processedPhotos.push({
                name: photo.name,
                photoUri: photoUrl,
                widthPx: photo.widthPx || 800,
                heightPx: photo.heightPx || 600,
                authorAttributions: photo.authorAttributions || []
              });
            }
          }
        }
      }

      const result: PlaceDetailsWithPhotos = {
        id: data.id,
        displayName: data.displayName,
        formattedAddress: data.formattedAddress,
        photos: processedPhotos,
        rating: data.rating,
        userRatingCount: data.userRatingCount,
        websiteUri: data.websiteUri,
        internationalPhoneNumber: data.internationalPhoneNumber,
        location: data.location
      };

      console.log('✅ Place details fetched successfully:', {
        placeId,
        photosCount: processedPhotos.length
      });

      return result;
    } catch (error) {
      console.error('❌ Error fetching place details:', error);
      return null;
    }
  }

  /**
   * Get photo URL using the new Place Photos API
   */
  public async getPhotoUrl(photoName: string, maxWidthPx: number = 800): Promise<string | null> {
    try {
      // Use skipHttpRedirect=true to get the JSON response with photoUri
      const url = `https://places.googleapis.com/v1/${photoName}/media?maxWidthPx=${maxWidthPx}&skipHttpRedirect=true&key=${(import.meta as any).env.VITE_GOOGLE_MAPS_API_KEY}`;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        console.error('❌ Photo API Error:', {
          status: response.status,
          photoName
        });
        return null;
      }

      const data = await response.json();
      
      if (data.photoUri) {
        console.log('✅ Photo URL generated:', {
          photoName,
          photoUri: data.photoUri
        });
        return data.photoUri;
      }

      return null;
    } catch (error) {
      console.error('❌ Error getting photo URL:', error);
      return null;
    }
  }

  /**
   * Get multiple photo URLs efficiently
   */
  public async getMultiplePhotoUrls(photoNames: string[], maxWidthPx: number = 800): Promise<Record<string, string>> {
    const results: Record<string, string> = {};
    
    // Process photos in parallel for better performance
    const promises = photoNames.map(async (photoName) => {
      const url = await this.getPhotoUrl(photoName, maxWidthPx);
      if (url) {
        results[photoName] = url;
      }
    });

    await Promise.all(promises);
    
    console.log('✅ Multiple photo URLs generated:', {
      requested: photoNames.length,
      successful: Object.keys(results).length
    });

    return results;
  }
} 