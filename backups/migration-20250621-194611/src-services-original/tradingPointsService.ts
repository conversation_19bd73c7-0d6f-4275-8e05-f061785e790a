import { TradingPointsDataConnectService } from './dataconnect/tradingPointsDataConnectService';
// import { PostgreSQLUserService } from './postgresql/userService'; // Disabled for frontend compatibility

// Extended interface for admin operations
export interface AdminTradingPoint {
  id: string;
  name?: string | null;
  description: string;
  address?: string | null;
  latitude?: number | null;
  longitude?: number | null;
  category: string;
  photoUrl?: string | null;
  googlePhotoReference?: string | null;
  googlePlaceId?: string | null;
  boardLocation?: string | null;
  boardsCount: number;
  rating: number;
  status: 'active' | 'pending' | 'rejected' | 'inactive';
  ambassadorId?: string | null;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  moderatedBy?: string;
  moderatedAt?: string;
  rejectionReason?: string;
}

class TradingPointsService {
  private dataConnectService: TradingPointsDataConnectService;
  // private userService: PostgreSQLUserService; // Disabled for frontend compatibility

  constructor() {
    this.dataConnectService = new TradingPointsDataConnectService();
    // this.userService = new PostgreSQLUserService(); // Disabled for frontend compatibility
  }

  /**
   * Get all trading points - 100% PostgreSQL
   */
  async getAll(): Promise<AdminTradingPoint[]> {
    try {
      console.log('🔄 Getting trading points from PostgreSQL');
      const tradingPoints = await this.dataConnectService.getAll({});
      
      return tradingPoints.map(tp => ({
        id: tp.id,
        name: tp.name,
        description: tp.description,
        address: tp.address,
        latitude: tp.latitude,
        longitude: tp.longitude,
        category: tp.category,
        photoUrl: tp.photoUrl,
        googlePhotoReference: tp.googlePhotoReference,
        googlePlaceId: tp.googlePlaceId,
        boardLocation: tp.boardLocation,
        boardsCount: tp.boardsCount,
        rating: tp.rating,
        status: tp.status as AdminTradingPoint['status'],
        ambassadorId: tp.ambassadorId,
        createdAt: tp.createdAt,
        updatedAt: tp.updatedAt,
        createdBy: undefined,
        moderatedBy: undefined,
        moderatedAt: undefined,
        rejectionReason: undefined
      }));
    } catch (error) {
      console.error('❌ Error fetching trading points:', error);
      return [];
    }
  }

  /**
   * Get a single trading point by ID - 100% PostgreSQL
   */
  async getById(id: string): Promise<AdminTradingPoint | null> {
    try {
      console.log('🔄 Getting trading point by ID from PostgreSQL:', id);
      const tp = await this.dataConnectService.getById(id);
      
      if (!tp) return null;

      return {
        id: tp.id,
        name: tp.name,
        description: tp.description,
        address: tp.address,
        latitude: tp.latitude,
        longitude: tp.longitude,
        category: tp.category,
        photoUrl: tp.photoUrl,
        googlePhotoReference: tp.googlePhotoReference,
        googlePlaceId: tp.googlePlaceId,
        boardLocation: tp.boardLocation,
        boardsCount: tp.boardsCount,
        rating: tp.rating,
        status: tp.status as AdminTradingPoint['status'],
        ambassadorId: tp.ambassadorId,
        createdAt: tp.createdAt,
        updatedAt: tp.updatedAt,
        createdBy: undefined,
        moderatedBy: undefined,
        moderatedAt: undefined,
        rejectionReason: undefined
      };
    } catch (error) {
      console.error('❌ Error fetching trading point:', error);
      throw new Error('Failed to fetch trading point');
    }
  }
}

// Export singleton instance
export const tradingPointsService = new TradingPointsService();
