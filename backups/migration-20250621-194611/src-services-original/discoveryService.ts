import { Pin } from '@/types/pin';
import { useAuthStore } from '@/store/authStore';

export interface DiscoveryFeedItem {
  pin: Pin;
  trendingScore: number;
  category: 'trending' | 'new_releases' | 'popular' | 'rising_stars' | 'regional';
  metadata: {
    rankPosition: number;
    categoryRank: number;
    timeInTrending: number;
  };
}

export interface CategoryStats {
  name: string;
  count: number;
  growth: number; // Crescimento percentual
  isPopular: boolean;
}

export interface RisingCreator {
  id: string;
  name: string;
  username: string;
  avatar?: string;
  followerCount: number;
  growthRate: number; // Taxa de crescimento de seguidores
  recentPinsCount: number;
  engagementRate: number;
}

class DiscoveryService {
  private baseUrl = 'http://localhost:3001/api';

  /**
   * Busca pins trending com algoritmo aprimorado
   */
  async getTrendingPins(category?: string, limit: number = 30): Promise<DiscoveryFeedItem[]> {
    try {
      console.log('🔥 Fetching trending pins:', { category, limit });

      // Get current user for personalized like/save states
      const { user } = useAuthStore.getState();
      const userId = user?.id;

      // Construir query
      const params = new URLSearchParams({
        limit: limit.toString(),
        sortBy: 'trending'
      });
      
      if (category) {
        params.append('series', category);
      }
      
      if (userId) {
        params.append('userId', userId);
      }

      const response = await fetch(`${this.baseUrl}/pins?${params}`, {
        credentials: 'same-origin',
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch trending pins: ${response.status}`);
      }

      const rawPins: any[] = await response.json();
      console.log('📦 Raw trending pins received:', rawPins.length);
      
      // Normalizar pins para garantir compatibilidade
      const pins: Pin[] = rawPins.map((p) => ({
        id: p.id || p._id,
        name: p.name || p.title || 'Untitled Pin',
        image: p.image || p.imageUrl || '',
        description: p.description || '',
        rarity: (p.rarity ?? 'common') as Pin['rarity'],
        year: p.year || p.releaseYear,
        series: p.series || p.origin,
        likes: p.likes || p.likesCount || 0,
        comments: p.comments || p.commentsCount || 0,
        isLiked: Boolean(p.isLiked),
        isSaved: Boolean(p.isSaved),
        owner: p.owner || {
          id: p.userId || p.user?.id,
          name: p.user?.displayName || p.user?.username || 'Unknown',
          username: p.user?.username,
          avatar: p.user?.avatarUrl,
        },
        createdAt: p.createdAt ? new Date(p.createdAt) : undefined,
      }));
      
      console.log('✅ Pins normalized:', pins.length);
      if (pins.length > 0) {
        console.log('🔍 First normalized pin:', pins[0]);
      }

      // Aplicar algoritmo de trending aprimorado
      const trendingItems = pins.map((pin, index) => {
        const trendingScore = this.calculateAdvancedTrendingScore(pin);
        
        return {
          pin,
          trendingScore,
          category: 'trending' as const,
          metadata: {
            rankPosition: index + 1,
            categoryRank: index + 1,
            timeInTrending: this.calculateTimeInTrending(pin)
          }
        } as DiscoveryFeedItem;
      });

      // Reordenar por score aprimorado
      const sortedTrending = trendingItems
        .sort((a, b) => b.trendingScore - a.trendingScore)
        .map((item, index) => ({
          ...item,
          metadata: {
            ...item.metadata,
            rankPosition: index + 1
          }
        }));

      console.log('🏆 Trending pins processed:', sortedTrending.length);
      return sortedTrending;
    } catch (error) {
      console.error('❌ Error fetching trending pins:', error);
      throw new Error('Failed to fetch trending pins');
    }
  }

  /**
   * Busca pins mais recentes
   */
  async getNewReleases(limit: number = 20): Promise<DiscoveryFeedItem[]> {
    try {
      console.log('🆕 Fetching new releases:', { limit });

      // Get current user for personalized like/save states
      const { user } = useAuthStore.getState();
      const userId = user?.id;

      const params = new URLSearchParams({
        limit: limit.toString(),
        sortBy: 'newest'
      });
      
      if (userId) {
        params.append('userId', userId);
      }

      const response = await fetch(`${this.baseUrl}/pins?${params}`, {
        credentials: 'same-origin',
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch new releases: ${response.status}`);
      }

      const rawPins: any[] = await response.json();
      console.log('📦 Raw new releases received:', rawPins.length);
      
      // Normalizar pins
      const pins: Pin[] = rawPins.map((p) => ({
        id: p.id || p._id,
        name: p.name || p.title || 'Untitled Pin',
        image: p.image || p.imageUrl || '',
        description: p.description || '',
        rarity: (p.rarity ?? 'common') as Pin['rarity'],
        year: p.year || p.releaseYear,
        series: p.series || p.origin,
        likes: p.likes || p.likesCount || 0,
        comments: p.comments || p.commentsCount || 0,
        isLiked: Boolean(p.isLiked),
        isSaved: Boolean(p.isSaved),
        owner: p.owner || {
          id: p.userId || p.user?.id,
          name: p.user?.displayName || p.user?.username || 'Unknown',
          username: p.user?.username,
          avatar: p.user?.avatarUrl,
        },
        createdAt: p.createdAt ? new Date(p.createdAt) : undefined,
      }));
      
      console.log('✅ New releases normalized:', pins.length);

      const newReleases = pins.map((pin, index) => ({
        pin,
        trendingScore: this.calculateNewReleaseScore(pin),
        category: 'new_releases' as const,
        metadata: {
          rankPosition: index + 1,
          categoryRank: index + 1,
          timeInTrending: 0 // Novos lançamentos não têm tempo em trending
        }
      } as DiscoveryFeedItem));

      console.log('✅ New releases processed:', newReleases.length);
      return newReleases;
    } catch (error) {
      console.error('❌ Error fetching new releases:', error);
      throw new Error('Failed to fetch new releases');
    }
  }

  /**
   * Busca pins mais populares (baseado em likes + saves totais)
   */
  async getPopularPins(timeframe: 'day' | 'week' | 'month' = 'week', limit: number = 20): Promise<DiscoveryFeedItem[]> {
    try {
      console.log('⭐ Fetching popular pins:', { timeframe, limit });

      // Get current user for personalized like/save states
      const { user } = useAuthStore.getState();
      const userId = user?.id;

      // Calcular data de início baseada no timeframe
      const startDate = this.getStartDateForTimeframe(timeframe);
      
      const params = new URLSearchParams({
        limit: limit.toString(),
        sortBy: 'popular',
        startDate: startDate.toISOString()
      });
      
      if (userId) {
        params.append('userId', userId);
      }

      const response = await fetch(`${this.baseUrl}/pins?${params}`, {
        credentials: 'same-origin',
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch popular pins: ${response.status}`);
      }

      const rawPins: any[] = await response.json();
      console.log('📦 Raw popular pins received:', rawPins.length);
      
      // Normalizar pins
      const pins: Pin[] = rawPins.map((p) => ({
        id: p.id || p._id,
        name: p.name || p.title || 'Untitled Pin',
        image: p.image || p.imageUrl || '',
        description: p.description || '',
        rarity: (p.rarity ?? 'common') as Pin['rarity'],
        year: p.year || p.releaseYear,
        series: p.series || p.origin,
        likes: p.likes || p.likesCount || 0,
        comments: p.comments || p.commentsCount || 0,
        isLiked: Boolean(p.isLiked),
        isSaved: Boolean(p.isSaved),
        owner: p.owner || {
          id: p.userId || p.user?.id,
          name: p.user?.displayName || p.user?.username || 'Unknown',
          username: p.user?.username,
          avatar: p.user?.avatarUrl,
        },
        createdAt: p.createdAt ? new Date(p.createdAt) : undefined,
      }));
      
      console.log('✅ Popular pins normalized:', pins.length);

      const popularPins = pins.map((pin, index) => ({
        pin,
        trendingScore: this.calculatePopularityScore(pin),
        category: 'popular' as const,
        metadata: {
          rankPosition: index + 1,
          categoryRank: index + 1,
          timeInTrending: this.calculateTimeInTrending(pin)
        }
      } as DiscoveryFeedItem));

      console.log('✅ Popular pins processed:', popularPins.length);
      return popularPins;
    } catch (error) {
      console.error('❌ Error fetching popular pins:', error);
      throw new Error('Failed to fetch popular pins');
    }
  }

  /**
   * Busca pins de criadores emergentes (rising stars)
   */
  async getRisingStars(limit: number = 15): Promise<DiscoveryFeedItem[]> {
    try {
      console.log('🌟 Fetching rising stars:', { limit });

      // Buscar criadores emergentes primeiro
      const risingCreators = await this.getRisingCreators(10);
      
      if (risingCreators.length === 0) {
        console.log('No rising creators found, returning empty array');
        return [];
      }

      // Buscar pins dos criadores emergentes
      const risingStarPins: Pin[] = [];
      
      for (const creator of risingCreators) {
        try {
          const response = await fetch(`${this.baseUrl}/users/${creator.id}/recent-pins?limit=3`, {
            credentials: 'same-origin',
          });
          if (response.ok) {
            const pins = await response.json();
            risingStarPins.push(...pins);
          }
        } catch (error) {
          console.warn(`Could not fetch pins for rising creator ${creator.id}:`, error);
        }
      }

      console.log('📦 Rising star pins collected:', risingStarPins.length);

      const risingStarItems = risingStarPins
        .slice(0, limit)
        .map((pin, index) => ({
          pin,
          trendingScore: this.calculateRisingStarScore(pin, risingCreators),
          category: 'rising_stars' as const,
          metadata: {
            rankPosition: index + 1,
            categoryRank: index + 1,
            timeInTrending: 0 // Rising stars são novos por definição
          }
        } as DiscoveryFeedItem));

      console.log('✅ Rising star items processed:', risingStarItems.length);
      return risingStarItems;
    } catch (error) {
      console.error('❌ Error fetching rising stars:', error);
      throw new Error('Failed to fetch rising stars');
    }
  }

  /**
   * Busca tendências regionais (baseado em localização)
   */
  async getRegionalTrends(location?: string, limit: number = 20): Promise<DiscoveryFeedItem[]> {
    try {
      console.log('🌍 Fetching regional trends:', { location, limit });

      // Get current user for personalized like/save states
      const { user } = useAuthStore.getState();
      const userId = user?.id;

      // Por enquanto, usar trending global com filtro de localização
      // TODO: Implementar lógica de localização real quando disponível
      const params = new URLSearchParams({
        limit: limit.toString(),
        sortBy: 'trending'
      });

      if (location) {
        params.append('region', location);
      }
      
      if (userId) {
        params.append('userId', userId);
      }

      const response = await fetch(`${this.baseUrl}/pins?${params}`, {
        credentials: 'same-origin',
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch regional trends: ${response.status}`);
      }

      const pins: Pin[] = await response.json();
      console.log('📦 Regional trend pins received:', pins.length);

      const regionalTrends = pins.map((pin, index) => ({
        pin,
        trendingScore: this.calculateRegionalTrendScore(pin, location),
        category: 'regional' as const,
        metadata: {
          rankPosition: index + 1,
          categoryRank: index + 1,
          timeInTrending: this.calculateTimeInTrending(pin)
        }
      } as DiscoveryFeedItem));

      console.log('✅ Regional trends processed:', regionalTrends.length);
      return regionalTrends;
    } catch (error) {
      console.error('❌ Error fetching regional trends:', error);
      throw new Error('Failed to fetch regional trends');
    }
  }

  /**
   * Busca estatísticas de categorias para filtros
   */
  async getCategoryStats(): Promise<CategoryStats[]> {
    try {
      console.log('📊 Fetching category stats');

      const response = await fetch(`${this.baseUrl}/pins/categories/stats`, {
        credentials: 'same-origin',
      });
      if (!response.ok) {
        // Fallback: gerar estatísticas básicas
        return this.generateFallbackCategoryStats();
      }

      const stats = await response.json();
      console.log('✅ Category stats received:', stats.length);
      return stats;
    } catch (error) {
      console.warn('Could not fetch category stats, using fallback:', error);
      return this.generateFallbackCategoryStats();
    }
  }

  /**
   * Busca criadores emergentes
   */
  async getRisingCreators(limit: number = 10): Promise<RisingCreator[]> {
    try {
      console.log('🌟 Fetching rising creators:', { limit });

      const response = await fetch(`${this.baseUrl}/users/rising-creators?limit=${limit}`, {
        credentials: 'same-origin',
      });
      if (!response.ok) {
        // Fallback: retornar array vazio
        console.log('No rising creators endpoint, returning empty array');
        return [];
      }

      const creators = await response.json();
      console.log('✅ Rising creators received:', creators.length);
      return creators;
    } catch (error) {
      console.warn('Could not fetch rising creators:', error);
      return [];
    }
  }

  // ========== ALGORITMOS DE SCORING ==========

  /**
   * Algoritmo de trending aprimorado
   */
  private calculateAdvancedTrendingScore(pin: Pin): number {
    const baseScore = (pin.likes || 0) + (pin.comments || 0) * 3;
    const timeFactor = this.calculateTimeFactor(pin);
    const rarityBonus = this.getRarityBonus(pin.rarity);
    const engagementVelocity = this.calculateEngagementVelocity(pin);
    
    return (baseScore * timeFactor * rarityBonus * engagementVelocity);
  }

  /**
   * Score para novos lançamentos
   */
  private calculateNewReleaseScore(pin: Pin): number {
    const freshness = this.calculateFreshness(pin);
    const initialEngagement = (pin.likes || 0) + (pin.comments || 0);
    const rarityBonus = this.getRarityBonus(pin.rarity);
    
    return freshness * (1 + initialEngagement * 0.1) * rarityBonus;
  }

  /**
   * Score de popularidade
   */
  private calculatePopularityScore(pin: Pin): number {
    const likes = pin.likes || 0;
    const comments = pin.comments || 0;
    const saves = 0; // TODO: Implementar quando saves estiver disponível
    
    return likes + (comments * 2) + (saves * 1.5);
  }

  /**
   * Score para rising stars
   */
  private calculateRisingStarScore(pin: Pin, creators: RisingCreator[]): number {
    const creator = creators.find(c => c.id === pin.owner?.id);
    const creatorBonus = creator ? creator.growthRate * creator.engagementRate : 1;
    const baseScore = (pin.likes || 0) + (pin.comments || 0) * 2;
    
    return baseScore * creatorBonus;
  }

  /**
   * Score para tendências regionais
   */
  private calculateRegionalTrendScore(pin: Pin, location?: string): number {
    const baseScore = this.calculateAdvancedTrendingScore(pin);
    const locationRelevance = location ? 1.2 : 1.0; // Bonus para localização específica
    
    return baseScore * locationRelevance;
  }

  // ========== FUNÇÕES AUXILIARES ==========

  private calculateTimeFactor(pin: Pin): number {
    if (!pin.createdAt) return 0.5;
    
    const now = Date.now();
    const pinTime = new Date(pin.createdAt).getTime();
    const hoursOld = (now - pinTime) / (1000 * 60 * 60);
    
    // Fator de decaimento temporal aprimorado
    if (hoursOld <= 1) return 2.0;      // Muito recente
    if (hoursOld <= 6) return 1.5;      // Recente
    if (hoursOld <= 24) return 1.2;     // Último dia
    if (hoursOld <= 48) return 1.0;     // Últimas 48h
    if (hoursOld <= 168) return 0.7;    // Última semana
    
    return Math.max(0.1, (336 - hoursOld) / 336); // Decaimento gradual até 2 semanas
  }

  private calculateFreshness(pin: Pin): number {
    if (!pin.createdAt) return 0.5;
    
    const now = Date.now();
    const pinTime = new Date(pin.createdAt).getTime();
    const hoursOld = (now - pinTime) / (1000 * 60 * 60);
    
    // Score de frescor para novos lançamentos
    if (hoursOld <= 1) return 1.0;
    if (hoursOld <= 6) return 0.9;
    if (hoursOld <= 24) return 0.8;
    if (hoursOld <= 72) return 0.6;
    
    return Math.max(0.1, (168 - hoursOld) / 168); // Decaimento até 1 semana
  }

  private getRarityBonus(rarity?: string): number {
    switch (rarity) {
      case 'legendary': return 1.5;
      case 'epic': return 1.3;
      case 'rare': return 1.2;
      case 'uncommon': return 1.1;
      case 'common': return 1.0;
      default: return 1.0;
    }
  }

  private calculateEngagementVelocity(pin: Pin): number {
    // Velocidade de engajamento baseada na idade do pin
    if (!pin.createdAt) return 1.0;
    
    const hoursOld = (Date.now() - new Date(pin.createdAt).getTime()) / (1000 * 60 * 60);
    const totalEngagement = (pin.likes || 0) + (pin.comments || 0);
    
    if (hoursOld <= 0) return 1.0;
    
    const velocity = totalEngagement / hoursOld;
    
    // Normalizar velocidade (engagement per hour)
    if (velocity >= 10) return 1.5;  // Muito viral
    if (velocity >= 5) return 1.3;   // Viral
    if (velocity >= 2) return 1.2;   // Bom engajamento
    if (velocity >= 1) return 1.1;   // Engajamento normal
    
    return 1.0;
  }

  private calculateTimeInTrending(pin: Pin): number {
    // Simular tempo em trending baseado na idade e engajamento
    if (!pin.createdAt) return 0;
    
    const hoursOld = (Date.now() - new Date(pin.createdAt).getTime()) / (1000 * 60 * 60);
    const engagement = (pin.likes || 0) + (pin.comments || 0);
    
    // Pins com mais engajamento ficam mais tempo em trending
    if (engagement >= 50) return Math.min(hoursOld, 72); // Máximo 3 dias
    if (engagement >= 20) return Math.min(hoursOld, 48); // Máximo 2 dias
    if (engagement >= 10) return Math.min(hoursOld, 24); // Máximo 1 dia
    
    return Math.min(hoursOld, 12); // Máximo 12 horas
  }

  private getStartDateForTimeframe(timeframe: 'day' | 'week' | 'month'): Date {
    const now = new Date();
    
    switch (timeframe) {
      case 'day':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case 'month':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      default:
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }
  }

  private generateFallbackCategoryStats(): CategoryStats[] {
    return [
      { name: 'Disney', count: 150, growth: 15.2, isPopular: true },
      { name: 'Marvel', count: 120, growth: 22.1, isPopular: true },
      { name: 'Star Wars', count: 95, growth: 8.7, isPopular: true },
      { name: 'Pixar', count: 80, growth: 12.3, isPopular: false },
      { name: 'Nintendo', count: 75, growth: 18.9, isPopular: false },
      { name: 'Pokemon', count: 65, growth: 25.4, isPopular: false }
    ];
  }
}

export const discoveryService = new DiscoveryService(); 