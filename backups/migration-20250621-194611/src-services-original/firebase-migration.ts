/**
 * Firebase Migration Service
 * 
 * Serviço para migrar dados mock para Firebase real
 * e criar as coleções necessárias no Firestore
 */

import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  getDocs, 
  query, 
  where,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './firebase';
import { User } from '@/types/firebase-schema';
import { COLLECTIONS } from '@/types/firebase-schema';
import { prepareForFirestore } from '@/utils/firestore-utils';

export class FirebaseMigrationService {
  
  /**
   * Cria um usuário no Firebase Firestore
   */
  static async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    try {
      console.log('🔄 Creating user in Firestore:', userData.email);
      
      // Gerar ID único para o usuário
      const usersRef = collection(db, COLLECTIONS.USERS);
      const userDocRef = doc(usersRef);
      
      const now = Timestamp.now();
      const rawUser: User = {
        ...userData,
        id: userDocRef.id,
        createdAt: now,
        updatedAt: now,
        joinedAt: userData.joinedAt || now,
        lastLoginAt: now
      };
      
      // Preparar dados para Firestore (remove campos undefined)
      const { data: user, valid, errors } = prepareForFirestore(rawUser);

      if (!valid) {
        console.error('❌ Invalid user data:', errors);
        throw new Error(`Invalid user data: ${errors.join(', ')}`);
      }

      console.log('🔄 Creating user with cleaned data:', user);
      
      // Salvar no Firestore
      await setDoc(userDocRef, user);
      
      console.log('✅ User created successfully:', rawUser.id);
      return rawUser;
      
    } catch (error) {
      console.error('❌ Error creating user:', error);
      throw error;
    }
  }
  
  /**
   * Verifica se um usuário já existe no Firestore
   */
  static async userExists(email: string): Promise<boolean> {
    try {
      const usersRef = collection(db, COLLECTIONS.USERS);
      const q = query(usersRef, where('email', '==', email));
      const querySnapshot = await getDocs(q);
      
      return !querySnapshot.empty;
    } catch (error) {
      console.error('❌ Error checking if user exists:', error);
      return false;
    }
  }
  
  /**
   * Busca usuário por email
   */
  static async getUserByEmail(email: string): Promise<User | null> {
    try {
      const usersRef = collection(db, COLLECTIONS.USERS);
      const q = query(usersRef, where('email', '==', email));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }
      
      const userDoc = querySnapshot.docs[0];
      return { id: userDoc.id, ...userDoc.data() } as User;
      
    } catch (error) {
      console.error('❌ Error getting user by email:', error);
      return null;
    }
  }
  
  /**
   * Busca usuário por ID
   */
  static async getUserById(userId: string): Promise<User | null> {
    try {
      const userDocRef = doc(db, COLLECTIONS.USERS, userId);
      const userDoc = await getDoc(userDocRef);
      
      if (!userDoc.exists()) {
        return null;
      }
      
      return { id: userDoc.id, ...userDoc.data() } as User;
      
    } catch (error) {
      console.error('❌ Error getting user by ID:', error);
      return null;
    }
  }
  
  /**
   * Busca usuário por username
   */
  static async getUserByUsername(username: string): Promise<User | null> {
    try {
      const usersRef = collection(db, COLLECTIONS.USERS);
      const q = query(usersRef, where('username', '==', username.toLowerCase()));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }
      
      const userDoc = querySnapshot.docs[0];
      return { id: userDoc.id, ...userDoc.data() } as User;
      
    } catch (error) {
      console.error('❌ Error getting user by username:', error);
      return null;
    }
  }
  
  /**
   * Atualiza dados do usuário
   */
  static async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    try {
      console.log('🔄 Updating user in Firestore:', userId);
      
      const userDocRef = doc(db, COLLECTIONS.USERS, userId);
      const updatedData = {
        ...updates,
        updatedAt: Timestamp.now()
      };
      
      // Remove campos undefined
      Object.keys(updatedData).forEach(key => {
        if (updatedData[key as keyof typeof updatedData] === undefined) {
          delete updatedData[key as keyof typeof updatedData];
        }
      });
      
      await setDoc(userDocRef, updatedData, { merge: true });
      
      // Buscar dados atualizados
      const updatedUser = await this.getUserById(userId);
      if (!updatedUser) {
        throw new Error('Failed to retrieve updated user');
      }
      
      console.log('✅ User updated successfully');
      return updatedUser;
      
    } catch (error) {
      console.error('❌ Error updating user:', error);
      throw error;
    }
  }
  
  /**
   * Migra usuários de desenvolvimento para o Firebase
   */
  static async migrateDevelopmentUsers(): Promise<void> {
    try {
      console.log('🚀 Starting development users migration...');
      
      const devUsers = [
        {
          email: '<EMAIL>',
          firstName: 'Developer',
          lastName: 'User',
          username: 'dev-user',
          avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
          bio: 'Full-stack developer working on PinPal. Passionate about Disney pins and modern web technologies.',
          location: 'San Francisco, CA',
          phoneNumber: '+****************',
          emailVerified: true,
          isActive: true,
          isVerified: true,
          role: 'admin' as const,
          status: 'active' as const,
          preferences: {
            notificationsEnabled: true,
            publicProfile: true,
            showLocation: true,
            showEmail: false,
            allowMessages: true
          },
          stats: {
            pinsCount: 156,
            boardsCount: 12,
            followersCount: 89,
            followingCount: 67,
            checkInsCount: 234,
            tradesCompletedCount: 89,
            likesReceivedCount: 445
          },
          joinedAt: Timestamp.fromDate(new Date('2024-01-15'))
        },
        {
          email: '<EMAIL>',
          firstName: 'Sarah',
          lastName: 'Collector',
          username: 'sarahcollector',
          avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
          bio: 'Disney pin enthusiast since childhood. Love collecting rare and vintage pieces!',
          location: 'Orlando, FL',
          phoneNumber: '+****************',
          emailVerified: true,
          isActive: true,
          isVerified: false,
          role: 'user' as const,
          status: 'active' as const,
          preferences: {
            notificationsEnabled: true,
            publicProfile: true,
            showLocation: true,
            showEmail: false,
            allowMessages: true
          },
          stats: {
            pinsCount: 324,
            boardsCount: 18,
            followersCount: 156,
            followingCount: 89,
            checkInsCount: 89,
            tradesCompletedCount: 156,
            likesReceivedCount: 892
          },
          joinedAt: Timestamp.fromDate(new Date('2023-11-01'))
        },
        {
          email: '<EMAIL>',
          firstName: 'Mike',
          lastName: 'Trader',
          username: 'miketrader',
          avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
          bio: 'Active pin trader specializing in Marvel and Star Wars collections.',
          location: 'Los Angeles, CA',
          emailVerified: true,
          isActive: true,
          isVerified: false,
          role: 'user' as const,
          status: 'active' as const,
          preferences: {
            notificationsEnabled: false,
            publicProfile: true,
            showLocation: true,
            showEmail: false,
            allowMessages: true
          },
          stats: {
            pinsCount: 198,
            boardsCount: 8,
            followersCount: 267,
            followingCount: 145,
            checkInsCount: 145,
            tradesCompletedCount: 267,
            likesReceivedCount: 534
          },
          joinedAt: Timestamp.fromDate(new Date('2023-12-15'))
        }
      ];
      
      const batch = writeBatch(db);
      let createdCount = 0;
      
      for (const userData of devUsers) {
        // Verificar se usuário já existe
        const exists = await this.userExists(userData.email);
        
        if (!exists) {
          const usersRef = collection(db, COLLECTIONS.USERS);
          const userDocRef = doc(usersRef);
          
          const now = Timestamp.now();
          const user: User = {
            ...userData,
            id: userDocRef.id,
            createdAt: now,
            updatedAt: now,
            lastLoginAt: now
          };
          
          batch.set(userDocRef, user);
          createdCount++;
          
          console.log(`📝 Prepared user for creation: ${userData.email}`);
        } else {
          console.log(`⏭️  User already exists: ${userData.email}`);
        }
      }
      
      if (createdCount > 0) {
        await batch.commit();
        console.log(`✅ Migration completed! Created ${createdCount} users.`);
      } else {
        console.log('✅ Migration completed! No new users to create.');
      }
      
    } catch (error) {
      console.error('❌ Error during migration:', error);
      throw error;
    }
  }
  
  /**
   * Verifica se as coleções necessárias existem e têm dados
   */
  static async checkCollectionsStatus(): Promise<{
    users: { exists: boolean; count: number };
    pins: { exists: boolean; count: number };
    boards: { exists: boolean; count: number };
    tradingPoints: { exists: boolean; count: number };
  }> {
    try {
      console.log('🔍 Checking collections status...');
      
      const collections = [
        COLLECTIONS.USERS,
        COLLECTIONS.PINS,
        COLLECTIONS.BOARDS,
        COLLECTIONS.TRADING_POINTS
      ];
      
      const status: any = {};
      
      for (const collectionName of collections) {
        try {
          const collectionRef = collection(db, collectionName);
          const snapshot = await getDocs(collectionRef);
          
          status[collectionName] = {
            exists: true,
            count: snapshot.size
          };
          
          console.log(`📊 ${collectionName}: ${snapshot.size} documents`);
        } catch (error) {
          status[collectionName] = {
            exists: false,
            count: 0
          };
          console.log(`❌ ${collectionName}: Collection not accessible`);
        }
      }
      
      return status;
      
    } catch (error) {
      console.error('❌ Error checking collections:', error);
      throw error;
    }
  }
}

export default FirebaseMigrationService; 