import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject,
  uploadBytesResumable,
  UploadTaskSnapshot
} from 'firebase/storage';
import { storage } from './firebase';

export interface UploadProgress {
  bytesTransferred: number;
  totalBytes: number;
  progress: number;
}

/**
 * Estrutura de organização do Firebase Storage:
 * 
 * /avatars/{userId}/
 *   - avatar_{timestamp}.{ext}
 * 
 * /pins/{userId}/
 *   - pin_{timestamp}.{ext}
 *   - pin_{pinId}_{timestamp}.{ext} (para múltiplas imagens do mesmo pin)
 * 
 * /boards/{userId}/{boardId}/
 *   - cover_{timestamp}.{ext}
 *   - thumbnail_{timestamp}.{ext} (versão reduzida)
 * 
 * /trading-points/{tradingPointId}/
 *   - main_{timestamp}.{ext} (foto principal)
 *   - gallery_{index}_{timestamp}.{ext} (galeria de fotos)
 *   - google_cache_{photoReference}.{ext} (cache de fotos do Google Places)
 * 
 * /temp/{userId}/
 *   - temp_{timestamp}.{ext} (uploads temporários)
 */

class StorageService {
  /**
   * Upload a file to Firebase Storage
   */
  async uploadFile(
    file: File, 
    path: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    try {
      console.log('🔄 Starting upload:', { path, fileName: file.name, fileSize: file.size });
      
      const storageRef = ref(storage, path);
      
      if (onProgress) {
        // Use resumable upload for progress tracking
        const uploadTask = uploadBytesResumable(storageRef, file);
        
        return new Promise((resolve, reject) => {
          uploadTask.on(
            'state_changed',
            (snapshot: UploadTaskSnapshot) => {
              const progress = {
                bytesTransferred: snapshot.bytesTransferred,
                totalBytes: snapshot.totalBytes,
                progress: (snapshot.bytesTransferred / snapshot.totalBytes) * 100
              };
              console.log(`📊 Upload progress: ${progress.progress.toFixed(1)}%`);
              onProgress(progress);
            },
            (error) => {
              console.error('❌ Upload error:', error);
              console.error('Error code:', error.code);
              console.error('Error message:', error.message);
              
              // Provide more specific error messages
              let errorMessage = 'Failed to upload file';
              if (error.code === 'storage/unauthorized') {
                errorMessage = 'Upload failed: Permission denied. Please check Firebase Storage rules.';
              } else if (error.code === 'storage/canceled') {
                errorMessage = 'Upload was canceled';
              } else if (error.code === 'storage/unknown') {
                errorMessage = 'Upload failed: Unknown error occurred';
              } else if (error.code === 'storage/invalid-format') {
                errorMessage = 'Upload failed: Invalid file format';
              } else if (error.code === 'storage/object-not-found') {
                errorMessage = 'Upload failed: Storage location not found';
              }
              
              reject(new Error(errorMessage));
            },
            async () => {
              try {
                console.log('✅ Upload completed, getting download URL...');
                const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
                console.log('✅ Download URL obtained:', downloadURL);
                resolve(downloadURL);
              } catch (error: any) {
                console.error('❌ Error getting download URL:', error);
                reject(new Error('Upload completed but failed to get download URL'));
              }
            }
          );
        });
      } else {
        // Simple upload without progress
        console.log('📤 Uploading without progress tracking...');
        const snapshot = await uploadBytes(storageRef, file);
        console.log('✅ Upload completed, getting download URL...');
        const downloadURL = await getDownloadURL(snapshot.ref);
        console.log('✅ Download URL obtained:', downloadURL);
        return downloadURL;
      }
    } catch (error: any) {
      console.error('❌ Error uploading file:', error);
      console.error('Error details:', {
        code: error.code,
        message: error.message,
        stack: error.stack
      });
      
      // Provide more specific error messages
      let errorMessage = 'Failed to upload file';
      if (error.code === 'storage/unauthorized') {
        errorMessage = 'Upload failed: Permission denied. Please check Firebase Storage rules.';
      } else if (error.code === 'storage/quota-exceeded') {
        errorMessage = 'Upload failed: Storage quota exceeded';
      } else if (error.message) {
        errorMessage = `Upload failed: ${error.message}`;
      }
      
      throw new Error(errorMessage);
    }
  }

  /**
   * Upload a pin image
   */
  async uploadPinImage(
    file: File, 
    userId: string,
    pinId?: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    // Generate unique filename
    const timestamp = Date.now();
    const extension = file.name.split('.').pop() || 'jpg';
    const filename = pinId 
      ? `pin_${pinId}_${timestamp}.${extension}`
      : `pin_${timestamp}.${extension}`;
    const path = `pins/${userId}/${filename}`;
    
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Upload a pin image with background removal processing
   * This method handles both original and processed versions
   */
  async uploadPinImageWithBackgroundRemoval(
    originalFile: File,
    processedFile: File | null,
    userId: string,
    pinId?: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<{
    originalUrl: string;
    processedUrl?: string;
    finalUrl: string; // URL da versão escolhida pelo usuário
  }> {
    const timestamp = Date.now();
    const originalExtension = originalFile.name.split('.').pop() || 'jpg';
    
    // Definir nomes dos arquivos
    const baseFilename = pinId 
      ? `pin_${pinId}_${timestamp}`
      : `pin_${timestamp}`;
    
    const originalFilename = `${baseFilename}_original.${originalExtension}`;
    const processedFilename = `${baseFilename}_processed.png`; // Background removal sempre gera PNG
    
    const originalPath = `pins/${userId}/${originalFilename}`;
    const processedPath = `pins/${userId}/${processedFilename}`;

    try {
      // Upload da imagem original
      console.log('🔄 Uploading original image...');
      const originalUrl = await this.uploadFile(originalFile, originalPath, onProgress);
      
      let processedUrl: string | undefined;
      
      // Upload da imagem processada se disponível
      if (processedFile) {
        console.log('🔄 Uploading processed image...');
        processedUrl = await this.uploadFile(processedFile, processedPath);
      }

      // Por padrão, usar a versão processada se disponível, senão a original
      const finalUrl = processedUrl || originalUrl;

      console.log('✅ Pin images uploaded successfully:', {
        original: originalUrl,
        processed: processedUrl,
        final: finalUrl
      });

      return {
        originalUrl,
        processedUrl,
        finalUrl
      };
    } catch (error) {
      console.error('❌ Error uploading pin images:', error);
      throw error;
    }
  }

  /**
   * Upload a board cover image
   */
  async uploadBoardCover(
    file: File, 
    userId: string,
    boardId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    const timestamp = Date.now();
    const extension = file.name.split('.').pop() || 'jpg';
    const filename = `cover_${timestamp}.${extension}`;
    const path = `boards/${userId}/${boardId}/${filename}`;
    
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Upload a board thumbnail (smaller version)
   */
  async uploadBoardThumbnail(
    file: File, 
    userId: string,
    boardId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    const timestamp = Date.now();
    const extension = file.name.split('.').pop() || 'jpg';
    const filename = `thumbnail_${timestamp}.${extension}`;
    const path = `boards/${userId}/${boardId}/${filename}`;
    
    // Compress image more aggressively for thumbnails
    const compressedFile = await this.compressImage(file, 400, 0.7);
    return this.uploadFile(compressedFile, path, onProgress);
  }

  /**
   * Upload a user avatar
   */
  async uploadAvatar(
    file: File, 
    userId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    const timestamp = Date.now();
    const extension = file.name.split('.').pop() || 'jpg';
    const filename = `avatar_${timestamp}.${extension}`;
    const path = `avatars/${userId}/${filename}`;
    
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Upload a trading point main photo
   */
  async uploadTradingPointPhoto(
    file: File, 
    tradingPointId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    const timestamp = Date.now();
    const extension = file.name.split('.').pop() || 'jpg';
    const filename = `main_${timestamp}.${extension}`;
    const path = `trading-points/${tradingPointId}/${filename}`;
    
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Upload a trading point gallery photo
   */
  async uploadTradingPointGalleryPhoto(
    file: File, 
    tradingPointId: string,
    index: number,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    const timestamp = Date.now();
    const extension = file.name.split('.').pop() || 'jpg';
    const filename = `gallery_${index}_${timestamp}.${extension}`;
    const path = `trading-points/${tradingPointId}/${filename}`;
    
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Cache a Google Places photo locally
   * This helps reduce API calls and improves performance
   */
  async cacheGooglePlacesPhoto(
    photoBlob: Blob,
    tradingPointId: string,
    photoReference: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    const timestamp = Date.now();
    const filename = `google_cache_${photoReference.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.jpg`;
    const path = `trading-points/${tradingPointId}/${filename}`;
    
    // Convert blob to file
    const file = new File([photoBlob], filename, { type: 'image/jpeg' });
    
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Upload a temporary file (for processing)
   */
  async uploadTempFile(
    file: File, 
    userId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    const timestamp = Date.now();
    const extension = file.name.split('.').pop() || 'jpg';
    const filename = `temp_${timestamp}.${extension}`;
    const path = `temp/${userId}/${filename}`;
    
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Delete a file from Firebase Storage
   */
  async deleteFile(url: string): Promise<void> {
    try {
      const fileRef = ref(storage, url);
      await deleteObject(fileRef);
      console.log('✅ File deleted successfully:', url);
    } catch (error) {
      console.error('❌ Error deleting file:', error);
      throw new Error('Failed to delete file');
    }
  }

  /**
   * Delete multiple files from Firebase Storage
   */
  async deleteFiles(urls: string[]): Promise<void> {
    try {
      const deletePromises = urls.map(url => this.deleteFile(url));
      await Promise.all(deletePromises);
      console.log(`✅ Successfully deleted ${urls.length} files`);
    } catch (error) {
      console.error('❌ Error deleting files:', error);
      throw new Error('Failed to delete some files');
    }
  }

  /**
   * Delete all files in a folder (e.g., when deleting a board)
   */
  async deleteFolderContents(folderPath: string): Promise<void> {
    try {
      // Note: Firebase Storage doesn't have a direct way to list and delete folder contents
      // This would need to be implemented with Cloud Functions or by tracking file URLs in the database
      console.warn('⚠️ deleteFolderContents not implemented - use deleteFiles with tracked URLs');
    } catch (error) {
      console.error('❌ Error deleting folder contents:', error);
      throw new Error('Failed to delete folder contents');
    }
  }

  /**
   * Get file path from download URL
   */
  getPathFromUrl(url: string): string {
    try {
      const baseUrl = 'https://firebasestorage.googleapis.com/v0/b/';
      const urlParts = url.split(baseUrl)[1];
      const pathPart = urlParts.split('/o/')[1];
      const path = pathPart.split('?')[0];
      return decodeURIComponent(path);
    } catch (error) {
      console.error('Error parsing URL:', error);
      return '';
    }
  }

  /**
   * Validate image file (including HEIC support and minimum dimensions)
   */
  async validateImageFile(
    file: File, 
    maxSizeMB: number = 10, 
    minWidth: number = 300, 
    minHeight: number = 300
  ): Promise<string | null> {
    const { validateImageRequirements } = await import('@/utils/imageProcessor');
    
    const requirements = {
      minWidth,
      minHeight,
      maxSizeMB,
      allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/heic']
    };

    const validation = await validateImageRequirements(file, requirements);
    return validation.valid ? null : validation.error || 'Erro na validação da imagem';
  }

  /**
   * Compress image before upload (with HEIC support and advanced compression)
   */
  async compressImage(file: File, maxWidth: number = 1200, quality: number = 0.8): Promise<File> {
    try {
      // Import the image processor utility
      const { processImage } = await import('@/utils/imageProcessor');
      
      // Process the image (handles HEIC conversion and compression)
      const processed = await processImage(file);
      
      // Additional compression if needed
      if (processed.file.size > 1024 * 1024) { // If still larger than 1MB
        const { default: imageCompression } = await import('browser-image-compression');
        
        const options = {
          maxSizeMB: 1,
          maxWidthOrHeight: maxWidth,
          useWebWorker: true,
          fileType: processed.file.type === 'image/png' ? 'image/png' : 'image/jpeg',
          initialQuality: quality,
        };

        const compressedFile = await imageCompression(processed.file, options);
        return compressedFile;
      }
      
      return processed.file;
    } catch (error) {
      console.error('Error compressing image:', error);
      
      // Fallback to basic canvas compression for non-HEIC files
      if (!file.name.toLowerCase().endsWith('.heic') && !file.name.toLowerCase().endsWith('.heif')) {
        return this.basicCompressImage(file, maxWidth, quality);
      }
      
      throw new Error('Failed to process image. Please try a different format.');
    }
  }

  /**
   * Basic canvas-based image compression (fallback method)
   */
  private async basicCompressImage(file: File, maxWidth: number = 1200, quality: number = 0.8): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now()
              });
              resolve(compressedFile);
            } else {
              resolve(file);
            }
          },
          file.type,
          quality
        );
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Create multiple sizes of an image (thumbnail, medium, large)
   */
  async createImageVariants(file: File): Promise<{
    thumbnail: File;
    medium: File;
    large: File;
  }> {
    const [thumbnail, medium, large] = await Promise.all([
      this.compressImage(file, 200, 0.7),  // Thumbnail: 200px, 70% quality
      this.compressImage(file, 600, 0.8),  // Medium: 600px, 80% quality
      this.compressImage(file, 1200, 0.9)  // Large: 1200px, 90% quality
    ]);

    return { thumbnail, medium, large };
  }

  /**
   * Get storage usage statistics (requires additional setup)
   */
  async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    byCategory: Record<string, { files: number; size: number }>;
  }> {
    // This would require Cloud Functions to implement properly
    // For now, return placeholder data
    console.warn('⚠️ getStorageStats not implemented - requires Cloud Functions');
    return {
      totalFiles: 0,
      totalSize: 0,
      byCategory: {}
    };
  }

  /**
   * Cleanup old temporary files (should be called periodically)
   */
  async cleanupTempFiles(olderThanHours: number = 24): Promise<void> {
    // This would require Cloud Functions to implement properly
    console.warn('⚠️ cleanupTempFiles not implemented - requires Cloud Functions');
  }
}

// Export singleton instance
export const storageService = new StorageService();
export default storageService; 