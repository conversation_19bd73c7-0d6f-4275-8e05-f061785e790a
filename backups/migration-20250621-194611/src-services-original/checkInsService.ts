import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './firebase';
import { CheckIn, COLLECTIONS } from '@/services/database/schema';

class CheckInsService {
  private collection = collection(db, COLLECTIONS.CHECK_INS);

  /**
   * Create a new check-in
   */
  async createCheckIn(data: {
    userId: string;
    userName: string;
    userAvatarUrl?: string;
    tradingPointId: string;
    tradingPointName: string;
    message?: string;
    photos?: string[];
    rating?: number;
    latitude?: number;
    longitude?: number;
    accuracy?: number;
  }): Promise<CheckIn> {
    try {
      const now = Timestamp.now();
      
      const checkInData: Omit<CheckIn, 'id'> = {
        ...data,
        checkInAt: now,
        isVerified: this.verifyLocation(data.latitude, data.longitude, data.accuracy),
      };

      // Remove undefined fields before sending to Firestore
      const cleanedData = this.removeUndefinedFields(checkInData);
      const docRef = await addDoc(this.collection, cleanedData);
      
      return {
        id: docRef.id,
        ...checkInData
      };
    } catch (error) {
      console.error('Error creating check-in:', error);
      throw new Error('Failed to create check-in');
    }
  }

  /**
   * Get check-ins for a user
   */
  async getUserCheckIns(userId: string, limitCount: number = 50): Promise<CheckIn[]> {
    try {
      const q = query(
        this.collection,
        where('userId', '==', userId),
        orderBy('checkInAt', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CheckIn));
    } catch (error) {
      console.error('Error fetching user check-ins:', error);
      throw new Error('Failed to fetch user check-ins');
    }
  }

  /**
   * Get check-ins for a trading point
   */
  async getTradingPointCheckIns(tradingPointId: string, limitCount: number = 50): Promise<CheckIn[]> {
    try {
      const q = query(
        this.collection,
        where('tradingPointId', '==', tradingPointId),
        orderBy('checkInAt', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CheckIn));
    } catch (error) {
      console.error('Error fetching trading point check-ins:', error);
      throw new Error('Failed to fetch trading point check-ins');
    }
  }

  /**
   * Get recent check-ins (for activity feed)
   */
  async getRecentCheckIns(limitCount: number = 20): Promise<CheckIn[]> {
    try {
      const q = query(
        this.collection,
        where('isVerified', '==', true),
        orderBy('checkInAt', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CheckIn));
    } catch (error) {
      console.error('Error fetching recent check-ins:', error);
      throw new Error('Failed to fetch recent check-ins');
    }
  }

  /**
   * Get check-ins in date range (for analytics)
   */
  async getCheckInsInDateRange(startDate: Date, endDate: Date): Promise<CheckIn[]> {
    try {
      const q = query(
        this.collection,
        where('checkInAt', '>=', Timestamp.fromDate(startDate)),
        where('checkInAt', '<=', Timestamp.fromDate(endDate)),
        orderBy('checkInAt', 'desc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CheckIn));
    } catch (error) {
      console.error('Error fetching check-ins in date range:', error);
      throw new Error('Failed to fetch check-ins in date range');
    }
  }

  /**
   * Update check-in
   */
  async updateCheckIn(id: string, updates: Partial<CheckIn>): Promise<void> {
    try {
      const docRef = doc(this.collection, id);
      await updateDoc(docRef, updates);
    } catch (error) {
      console.error('Error updating check-in:', error);
      throw new Error('Failed to update check-in');
    }
  }

  /**
   * Delete check-in
   */
  async deleteCheckIn(id: string): Promise<void> {
    try {
      const docRef = doc(this.collection, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting check-in:', error);
      throw new Error('Failed to delete check-in');
    }
  }

  /**
   * Report check-in as inappropriate
   */
  async reportCheckIn(checkInId: string, reportedBy: string): Promise<void> {
    try {
      const docRef = doc(this.collection, checkInId);
      const checkInDoc = await getDoc(docRef);
      
      if (!checkInDoc.exists()) {
        throw new Error('Check-in not found');
      }

      const checkIn = checkInDoc.data() as CheckIn;
      const reportedByList = checkIn.reportedBy || [];
      
      if (!reportedByList.includes(reportedBy)) {
        reportedByList.push(reportedBy);
        await updateDoc(docRef, { reportedBy: reportedByList });
      }
    } catch (error) {
      console.error('Error reporting check-in:', error);
      throw new Error('Failed to report check-in');
    }
  }

  /**
   * Get check-in statistics for a user
   */
  async getUserCheckInStats(userId: string): Promise<{
    total: number;
    last30Days: number;
    last7Days: number;
    uniqueLocations: number;
    averageRating: number;
  }> {
    try {
      const userCheckIns = await this.getUserCheckIns(userId, 1000);
      
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const last30Days = userCheckIns.filter(checkIn => 
        checkIn.checkInAt.toDate() >= thirtyDaysAgo
      ).length;

      const last7Days = userCheckIns.filter(checkIn => 
        checkIn.checkInAt.toDate() >= sevenDaysAgo
      ).length;

      const uniqueLocations = new Set(userCheckIns.map(checkIn => checkIn.tradingPointId)).size;
      
      const ratingsSum = userCheckIns
        .filter(checkIn => checkIn.rating)
        .reduce((sum, checkIn) => sum + (checkIn.rating || 0), 0);
      
      const ratingsCount = userCheckIns.filter(checkIn => checkIn.rating).length;
      const averageRating = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;

      return {
        total: userCheckIns.length,
        last30Days,
        last7Days,
        uniqueLocations,
        averageRating
      };
    } catch (error) {
      console.error('Error getting user check-in stats:', error);
      throw new Error('Failed to get user check-in stats');
    }
  }

  /**
   * Get check-in statistics for a trading point
   */
  async getTradingPointCheckInStats(tradingPointId: string): Promise<{
    total: number;
    last30Days: number;
    last7Days: number;
    uniqueVisitors: number;
    uniqueVisitorsLast30Days: number;
    averageRating: number;
  }> {
    try {
      const checkIns = await this.getTradingPointCheckIns(tradingPointId, 1000);
      
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const last30DaysCheckIns = checkIns.filter(checkIn => 
        checkIn.checkInAt.toDate() >= thirtyDaysAgo
      );

      const last7Days = checkIns.filter(checkIn => 
        checkIn.checkInAt.toDate() >= sevenDaysAgo
      ).length;

      const uniqueVisitors = new Set(checkIns.map(checkIn => checkIn.userId)).size;
      const uniqueVisitorsLast30Days = new Set(last30DaysCheckIns.map(checkIn => checkIn.userId)).size;
      
      const ratingsSum = checkIns
        .filter(checkIn => checkIn.rating)
        .reduce((sum, checkIn) => sum + (checkIn.rating || 0), 0);
      
      const ratingsCount = checkIns.filter(checkIn => checkIn.rating).length;
      const averageRating = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;

      return {
        total: checkIns.length,
        last30Days: last30DaysCheckIns.length,
        last7Days,
        uniqueVisitors,
        uniqueVisitorsLast30Days,
        averageRating
      };
    } catch (error) {
      console.error('Error getting trading point check-in stats:', error);
      throw new Error('Failed to get trading point check-in stats');
    }
  }

  /**
   * Verify if check-in location is valid (basic implementation)
   */
  private verifyLocation(latitude?: number, longitude?: number, accuracy?: number): boolean {
    // Basic verification - in a real app, you'd check distance to trading point
    if (!latitude || !longitude) return false;
    if (accuracy && accuracy > 100) return false; // More than 100m accuracy is suspicious
    return true;
  }

  /**
   * Remove undefined fields from object (Firestore doesn't accept undefined values)
   */
  private removeUndefinedFields(obj: any): any {
    const cleaned: any = {};
    Object.keys(obj).forEach(key => {
      if (obj[key] !== undefined) {
        cleaned[key] = obj[key];
      }
    });
    return cleaned;
  }

  /**
   * Bulk create check-ins (for data population)
   */
  async bulkCreateCheckIns(checkIns: Omit<CheckIn, 'id'>[]): Promise<void> {
    try {
      const batch = writeBatch(db);
      
      checkIns.forEach(checkInData => {
        const docRef = doc(this.collection);
        // Remove undefined fields before sending to Firestore
        const cleanedData = this.removeUndefinedFields(checkInData);
        batch.set(docRef, cleanedData);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error bulk creating check-ins:', error);
      throw new Error('Failed to bulk create check-ins');
    }
  }
}

export const checkInsService = new CheckInsService(); 