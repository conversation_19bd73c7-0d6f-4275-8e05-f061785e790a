// Import classes for local use
import { BoardsDataConnectService } from './boardsDataConnectService';
import { PinsDataConnectService } from './pinsDataConnectService';
import { TradingPointsDataConnectService } from './tradingPointsDataConnectService';
import { MCPPostgreSQLService } from './mcpPostgreSQLService';
import { UsersDataConnectService } from './usersDataConnectService';

// Data Connect Services - PostgreSQL
export { BoardsDataConnectService } from './boardsDataConnectService';
export { PinsDataConnectService } from './pinsDataConnectService';
export { TradingPointsDataConnectService } from './tradingPointsDataConnectService';
export { MCPPostgreSQLService } from './mcpPostgreSQLService';
export { UsersDataConnectService } from './usersDataConnectService';
export { dataConnect } from './config';

// Instances - Singleton pattern para os serviços
export const boardsDataConnectService = new BoardsDataConnectService();
export const pinsDataConnectService = new PinsDataConnectService();
export const tradingPointsDataConnectService = new TradingPointsDataConnectService();
export const mcpPostgreSQLService = new MCPPostgreSQLService();
export const usersDataConnectService = new UsersDataConnectService(); 