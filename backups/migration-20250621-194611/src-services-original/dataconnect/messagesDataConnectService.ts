// Importações atualizadas para usar REST API
import { dataConnect } from './config';

// Base URL for backend API
const API_BASE_URL = 'http://localhost:3001';

// ===== INTERFACES =====

export interface Conversation {
  id: string;
  type: string;
  title?: string | null;
  lastMessageId?: string | null;
  lastMessageAt?: string | null;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
  participants?: ConversationParticipant[];
  lastMessage?: Message;
  unreadCount?: number;
}

export interface ConversationParticipant {
  user: {
    id: string;
    username: string;
    displayName: string;
    email?: string;
  };
  lastReadMessageId?: string | null;
  lastReadAt?: string | null;
  isArchived: boolean;
  isMuted: boolean;
  isBlocked: boolean;
  joinedAt: string;
  leftAt?: string | null;
}

export interface Message {
  id: string;
  content: string;
  contentType: string;
  attachmentUrl?: string | null;
  pinId?: string | null;
  tradeId?: string | null;
  status: string;
  isEdited: boolean;
  editedAt?: string | null;
  isDeleted: boolean;
  deletedAt?: string | null;
  createdAt: string;
  updatedAt: string;
  sender: {
    id: string;
    username: string;
    displayName: string;
  };
  conversation?: {
    id: string;
    type: string;
  };
  pin?: {
    id: string;
    title: string;
    imageUrl: string;
    user: {
      id: string;
      username: string;
      displayName: string;
    };
  };
  trade?: {
    id: string;
    status: string;
    requestedPin: {
      id: string;
      title: string;
      imageUrl: string;
    };
    offeredPin: {
      id: string;
      title: string;
      imageUrl: string;
    };
  };
  messageStatuses?: MessageStatus[];
}

export interface MessageStatus {
  user: {
    id: string;
    username: string;
  };
  status: string;
  timestamp: string;
}

export interface CreateConversationData {
  type?: string;
  title?: string;
  participants: string[];
}

export interface SendMessageData {
  conversationId: string;
  senderId: string;
  content: string;
  contentType?: string;
  attachmentUrl?: string;
  pinId?: string;
  tradeId?: string;
  fileName?: string;
  fileSize?: number;
  fileType?: string;
}

export interface UpdateParticipantData {
  lastReadMessageId?: string;
  isArchived?: boolean;
  isMuted?: boolean;
  isBlocked?: boolean;
}

export interface PaginationOptions {
  limit?: number;
  offset?: number;
}

export interface SearchOptions extends PaginationOptions {
  before?: string;
}

// ===== SERVICE CLASS =====

export class MessagesDataConnectService {
  
  // ===== CONVERSATION METHODS =====
  
  async createConversation(data: CreateConversationData): Promise<{ id: string }> {
    try {
      console.log('📝 Creating conversation with data:', data);
      
      const response = await fetch(`${API_BASE_URL}/api/messages/conversations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json();
        console.error('❌ API error creating conversation:', error);
        throw new Error(error.error || 'Failed to create conversation');
      }
      
      const result = await response.json();
      console.log('✅ Successfully created conversation:', result.id);
      return { id: result.id };
    } catch (error: any) {
      console.error('❌ Error creating conversation:', error);
      throw new Error(`Failed to create conversation: ${error.message}`);
    }
  }

  async findOrCreateConversation(data: CreateConversationData): Promise<{ id: string }> {
    try {
      console.log('🔍 Finding or creating conversation with participants:', data.participants);
      
      // First try to find existing conversation
      try {
        const existing = await this.findConversationByParticipants(data.participants);
        if (existing) {
          console.log('✅ Found existing conversation:', existing.id);
          return { id: existing.id };
        }
      } catch (findError: any) {
        // If error is 404 (not found), that's expected - we'll create a new one
        if (findError.message?.includes('No conversation found') || findError.message?.includes('Failed to find conversation')) {
          console.log('💡 No existing conversation found, creating new one...');
        } else {
          // If it's a different error, log it but continue to creation
          console.warn('⚠️ Error finding conversation, will create new one:', findError.message);
        }
      }
      
      // If not found or error occurred, create new one
      console.log('📝 Creating new conversation...');
      const newConversation = await this.createConversation(data);
      console.log('✅ Created new conversation:', newConversation.id);
      return newConversation;
      
    } catch (error: any) {
      console.error('❌ Error in findOrCreateConversation:', error);
      throw new Error(`Failed to find or create conversation: ${error.message}`);
    }
  }

  async getConversationById(id: string): Promise<Conversation | null> {
    try {
      console.log('Getting conversation by ID:', id);
      
      // For now, we'll implement this by getting user conversations and filtering
      // In a real implementation, we'd have a specific endpoint for this
      throw new Error('getConversationById not yet implemented via REST API');
    } catch (error) {
      console.error('Error getting conversation:', error);
      throw new Error('Failed to get conversation');
    }
  }

  async getConversationByParticipants(participants: string[]): Promise<Conversation | null> {
    try {
      console.log('Getting conversation by participants:', participants);
      
      // This would require a specific endpoint or client-side filtering
      throw new Error('getConversationByParticipants not yet implemented via REST API');
    } catch (error) {
      console.error('Error getting conversation by participants:', error);
      throw new Error('Failed to get conversation by participants');
    }
  }

  async getUserConversations(userId: string, options?: PaginationOptions): Promise<Conversation[]> {
    try {
      console.log('Getting user conversations:', userId, options);
      
      const params = new URLSearchParams();
      if (options?.limit) params.append('limit', options.limit.toString());
      if (options?.offset) params.append('offset', options.offset.toString());
      
      const response = await fetch(`${API_BASE_URL}/api/messages/conversations/user/${userId}?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get conversations');
      }
      
      const result = await response.json();
      return result.conversations.map((conv: any) => ({
        id: conv.id,
        type: conv.type,
        title: conv.title,
        lastMessageId: conv.lastMessage?.id,
        lastMessageAt: conv.lastMessageAt,
        isArchived: conv.isArchived,
        createdAt: conv.createdAt,
        updatedAt: conv.updatedAt,
        unreadCount: parseInt(conv.unreadCount) || 0,
        participants: conv.participants || [],
        lastMessage: conv.lastMessage ? {
          id: conv.lastMessage.id,
          content: conv.lastMessage.content,
          contentType: conv.lastMessage.contentType,
          attachmentUrl: null,
          pinId: null,
          tradeId: null,
          status: 'sent',
          isEdited: false,
          editedAt: null,
          isDeleted: false,
          deletedAt: null,
          createdAt: conv.lastMessage.timestamp,
          updatedAt: conv.lastMessage.timestamp,
          sender: conv.lastMessage.sender
        } : undefined,
      }));
    } catch (error) {
      console.error('Error getting user conversations:', error);
      throw new Error('Failed to get user conversations');
    }
  }

  async updateConversation(id: string, data: Partial<CreateConversationData>): Promise<void> {
    try {
      console.log('Updating conversation:', id, data);
      throw new Error('updateConversation not yet implemented via REST API');
    } catch (error) {
      console.error('Error updating conversation:', error);
      throw new Error('Failed to update conversation');
    }
  }

  async deleteConversation(conversationId: string, userId: string): Promise<void> {
    try {
      console.log('🗑️ Deleting conversation via REST API:', { conversationId, userId });
      
      const response = await fetch(`${API_BASE_URL}/api/messages/conversations/${conversationId}/user/${userId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete conversation');
      }
      
      const result = await response.json();
      console.log('✅ Conversation deleted successfully:', result);
    } catch (error) {
      console.error('❌ Error deleting conversation:', error);
      throw new Error('Failed to delete conversation');
    }
  }

  // ===== MESSAGE METHODS =====

  async uploadAttachment(file: File, userId: string): Promise<{
    attachmentUrl: string;
    fileName: string;
    originalName: string;
    size: number;
    mimeType: string;
  }> {
    try {
      console.log('Uploading attachment:', file.name, file.size);
      
      const formData = new FormData();
      formData.append('attachment', file);
      formData.append('userId', userId);
      
      const response = await fetch(`${API_BASE_URL}/api/messages/upload`, {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload attachment');
      }
      
      const result = await response.json();
      return {
        attachmentUrl: result.attachmentUrl,
        fileName: result.fileName,
        originalName: result.originalName,
        size: result.size,
        mimeType: result.mimeType
      };
    } catch (error) {
      console.error('Error uploading attachment:', error);
      throw new Error('Failed to upload attachment');
    }
  }

  async sendMessage(data: SendMessageData): Promise<Message> {
    try {
      console.log('Sending message:', data);
      
      const response = await fetch(`${API_BASE_URL}/api/messages/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to send message');
      }
      
      const result = await response.json();
      return {
        id: result.id,
        content: result.content,
        contentType: result.contentType,
        attachmentUrl: result.attachmentUrl,
        pinId: result.pinId,
        tradeId: result.tradeId,
        status: result.status,
        isEdited: false,
        editedAt: null,
        isDeleted: false,
        deletedAt: null,
        createdAt: result.createdAt,
        updatedAt: result.createdAt,
        sender: {
          id: result.senderId,
          username: '', // Would need to fetch user data
          displayName: '', // Would need to fetch user data
        },
      };
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Failed to send message');
    }
  }

  async getConversationMessages(conversationId: string, options?: SearchOptions): Promise<Message[]> {
    try {
      console.log('Getting conversation messages:', conversationId, options);
      
      const params = new URLSearchParams();
      if (options?.limit) params.append('limit', options.limit.toString());
      if (options?.offset) params.append('offset', options.offset.toString());
      if (options?.before) params.append('before', options.before);
      
      const response = await fetch(`${API_BASE_URL}/api/messages/conversations/${conversationId}/messages?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get messages');
      }
      
      const result = await response.json();
      return result.messages;
    } catch (error) {
      console.error('Error getting conversation messages:', error);
      throw new Error('Failed to get conversation messages');
    }
  }

  async updateMessage(id: string, content: string, senderId: string): Promise<void> {
    try {
      console.log('Updating message:', id, content, senderId);
      throw new Error('updateMessage not yet implemented via REST API');
    } catch (error) {
      console.error('Error updating message:', error);
      throw new Error('Failed to update message');
    }
  }

  async deleteMessage(id: string, senderId: string): Promise<{ deletedMessageId: string; conversationId: string; newLastMessage?: any }> {
    try {
      console.log('🗑️ Deleting message via REST API:', { id, senderId });
      
      const response = await fetch(`${API_BASE_URL}/api/messages/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: senderId }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete message');
      }
      
      const result = await response.json();
      console.log('✅ Message deleted successfully:', result);
      
      return {
        deletedMessageId: result.deletedMessageId,
        conversationId: result.conversationId,
        newLastMessage: result.newLastMessage
      };
    } catch (error) {
      console.error('❌ Error deleting message:', error);
      throw new Error('Failed to delete message');
    }
  }

  async markMessageAsRead(messageId: string, userId: string): Promise<void> {
    try {
      console.log('Marking message as read:', messageId, userId);
      
      const response = await fetch(`${API_BASE_URL}/api/messages/${messageId}/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to mark message as read');
      }
    } catch (error) {
      console.error('Error marking message as read:', error);
      throw new Error('Failed to mark message as read');
    }
  }

  async markConversationAsRead(conversationId: string, userId: string, lastMessageId?: string): Promise<void> {
    try {
      console.log('Marking conversation as read:', conversationId, userId, lastMessageId);
      
      // For now, we'll mark individual messages as read
      // In a real implementation, we'd have a specific endpoint for this
      if (lastMessageId) {
        await this.markMessageAsRead(lastMessageId, userId);
      }
    } catch (error) {
      console.error('Error marking conversation as read:', error);
      throw new Error('Failed to mark conversation as read');
    }
  }

  // ===== PARTICIPANT METHODS =====

  async addParticipant(conversationId: string, userId: string): Promise<void> {
    try {
      console.log('Adding participant:', conversationId, userId);
      throw new Error('addParticipant not yet implemented via REST API');
    } catch (error) {
      console.error('Error adding participant:', error);
      throw new Error('Failed to add participant');
    }
  }

  async removeParticipant(conversationId: string, userId: string): Promise<void> {
    try {
      console.log('Removing participant:', conversationId, userId);
      throw new Error('removeParticipant not yet implemented via REST API');
    } catch (error) {
      console.error('Error removing participant:', error);
      throw new Error('Failed to remove participant');
    }
  }

  async updateParticipantSettings(conversationId: string, userId: string, settings: UpdateParticipantData): Promise<void> {
    try {
      console.log('Updating participant settings:', conversationId, userId, settings);
      throw new Error('updateParticipantSettings not yet implemented via REST API');
    } catch (error) {
      console.error('Error updating participant settings:', error);
      throw new Error('Failed to update participant settings');
    }
  }

  // ===== SEARCH AND UTILITY METHODS =====

  async getUnreadCount(userId: string): Promise<number> {
    try {
      console.log('Getting unread count for user:', userId);
      
      const response = await fetch(`${API_BASE_URL}/api/messages/unread-count/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get unread count');
      }
      
      const result = await response.json();
      return result.unreadCount;
    } catch (error) {
      console.error('Error getting unread count:', error);
      throw new Error('Failed to get unread count');
    }
  }

  // ===== SEARCH METHODS =====

  async searchMessages(
    userId: string, 
    query: string, 
    options?: {
      limit?: number;
      offset?: number;
      conversationId?: string;
    }
  ): Promise<{
    messages: (Message & { 
      highlightedContent: string;
      conversation: { id: string; type: string; title?: string };
    })[];
    query: string;
    totalResults: number;
    hasMore: boolean;
  }> {
    try {
      console.log('Searching messages:', { userId, query, options });
      
      const params = new URLSearchParams({
        userId,
        query,
        limit: (options?.limit || 20).toString(),
        offset: (options?.offset || 0).toString(),
      });
      
      if (options?.conversationId) {
        params.append('conversationId', options.conversationId);
      }
      
      const response = await fetch(`${API_BASE_URL}/api/messages/search?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to search messages');
      }
      
      const result = await response.json();
      return {
        messages: result.messages.map((msg: any) => ({
          id: msg.id,
          content: msg.content,
          highlightedContent: msg.highlightedContent,
          contentType: msg.contentType,
          attachmentUrl: msg.attachmentUrl,
          pinId: msg.pinId,
          tradeId: msg.tradeId,
          status: msg.status,
          isEdited: msg.isEdited,
          editedAt: msg.editedAt,
          isDeleted: false,
          deletedAt: null,
          createdAt: msg.createdAt,
          updatedAt: msg.updatedAt,
          sender: msg.sender,
          conversation: msg.conversation
        })),
        query: result.query,
        totalResults: result.totalResults,
        hasMore: result.hasMore
      };
    } catch (error) {
      console.error('Error searching messages:', error);
      throw new Error('Failed to search messages');
    }
  }

  async getArchivedConversations(userId: string): Promise<Conversation[]> {
    try {
      console.log('Getting archived conversations:', userId);
      throw new Error('getArchivedConversations not yet implemented via REST API');
    } catch (error) {
      console.error('Error getting archived conversations:', error);
      throw new Error('Failed to get archived conversations');
    }
  }

  // ===== CONVERSATION MANAGEMENT =====

  async archiveConversation(conversationId: string, userId: string): Promise<void> {
    try {
      console.log('📁 Archiving conversation via REST API:', { conversationId, userId });
      
      const response = await fetch(`${API_BASE_URL}/api/messages/conversations/${conversationId}/archive/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to archive conversation');
      }
      
      const result = await response.json();
      console.log('✅ Conversation archived successfully:', result);
    } catch (error) {
      console.error('❌ Error archiving conversation:', error);
      throw new Error('Failed to archive conversation');
    }
  }

  async unarchiveConversation(conversationId: string, userId: string): Promise<void> {
    try {
      console.log('📂 Unarchiving conversation via REST API:', { conversationId, userId });
      
      const response = await fetch(`${API_BASE_URL}/api/messages/conversations/${conversationId}/unarchive/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to unarchive conversation');
      }
      
      const result = await response.json();
      console.log('✅ Conversation unarchived successfully:', result);
    } catch (error) {
      console.error('❌ Error unarchiving conversation:', error);
      throw new Error('Failed to unarchive conversation');
    }
  }

  async blockUser(conversationId: string, userId: string): Promise<void> {
    try {
      console.log('Blocking user:', conversationId, userId);
      throw new Error('blockUser not yet implemented via REST API');
    } catch (error) {
      console.error('Error blocking user:', error);
      throw new Error('Failed to block user');
    }
  }

  async unblockUser(conversationId: string, userId: string): Promise<void> {
    try {
      console.log('Unblocking user:', conversationId, userId);
      throw new Error('unblockUser not yet implemented via REST API');
    } catch (error) {
      console.error('Error unblocking user:', error);
      throw new Error('Failed to unblock user');
    }
  }

  // ===== BULK OPERATIONS =====

  async markMultipleMessagesAsRead(messageIds: string[], userId: string): Promise<void> {
    try {
      console.log('Marking multiple messages as read:', messageIds, userId);
      
      // Mark each message individually for now
      for (const messageId of messageIds) {
        await this.markMessageAsRead(messageId, userId);
      }
    } catch (error) {
      console.error('Error marking multiple messages as read:', error);
      throw new Error('Failed to mark multiple messages as read');
    }
  }

  async deleteMultipleMessages(messageIds: string[], senderId: string): Promise<void> {
    try {
      console.log('Deleting multiple messages:', messageIds, senderId);
      throw new Error('deleteMultipleMessages not yet implemented via REST API');
    } catch (error) {
      console.error('Error deleting multiple messages:', error);
      throw new Error('Failed to delete multiple messages');
    }
  }

  // ===== PERMISSION CHECKS =====

  async canAccessConversation(conversationId: string, userId: string): Promise<boolean> {
    try {
      console.log('Checking conversation access:', conversationId, userId);
      
      // For now, we'll assume access is allowed
      // In a real implementation, we'd check participant status
      return true;
    } catch (error) {
      console.error('Error checking conversation access:', error);
      return false;
    }
  }

  async getConversationParticipants(conversationId: string): Promise<ConversationParticipant[]> {
    try {
      console.log('Getting conversation participants:', conversationId);
      throw new Error('getConversationParticipants not yet implemented via REST API');
    } catch (error) {
      console.error('Error getting conversation participants:', error);
      throw new Error('Failed to get conversation participants');
    }
  }

  // ===== CONVERSATION UTILITY METHODS =====

  async findConversationByParticipants(participants: string[]): Promise<Conversation | null> {
    const maxRetries = 3;
    const retryDelay = 1000; // 1 second
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 Finding conversation by participants (attempt ${attempt}/${maxRetries}):`, participants);
        
        const params = new URLSearchParams({
          participants: participants.join(',')
        });
        
        const response = await fetch(`${API_BASE_URL}/api/messages/conversations/find?${params}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (response.status === 404) {
          console.log('📭 No conversation found between participants');
          return null; // No conversation found
        }
        
        if (!response.ok) {
          const error = await response.json();
          console.error('❌ API error finding conversation:', error);
          
          // If it's a server error and we have retries left, try again
          if (response.status >= 500 && attempt < maxRetries) {
            console.log(`🔄 Server error, retrying in ${retryDelay}ms...`);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            continue;
          }
          
          throw new Error(error.error || 'Failed to find conversation');
        }
        
        const result = await response.json();
        console.log('✅ Found conversation:', result.conversation.id);
        return {
          id: result.conversation.id,
          type: result.conversation.type,
          title: result.conversation.title,
          lastMessageId: null,
          lastMessageAt: result.conversation.lastMessageAt,
          isArchived: result.conversation.isArchived,
          createdAt: result.conversation.createdAt,
          updatedAt: result.conversation.updatedAt
        };
      } catch (error: any) {
        console.error(`❌ Error finding conversation (attempt ${attempt}/${maxRetries}):`, error);
        
        // If it's a network error and we have retries left, try again
        if (error instanceof TypeError && error.message.includes('fetch') && attempt < maxRetries) {
          console.log(`🌐 Network error, retrying in ${retryDelay}ms...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }
        
        // If it's the last attempt or not a retryable error, handle accordingly
        if (attempt === maxRetries) {
          // If it's a fetch error (network), don't throw, just return null
          if (error instanceof TypeError && error.message.includes('fetch')) {
            console.log('🌐 Network error after all retries, treating as conversation not found');
            return null;
          }
          throw new Error(`Failed to find conversation: ${error.message}`);
        }
      }
    }
    
    // This should never be reached, but just in case
    return null;
  }
}

// Export singleton instance
export const messagesService = new MessagesDataConnectService(); 