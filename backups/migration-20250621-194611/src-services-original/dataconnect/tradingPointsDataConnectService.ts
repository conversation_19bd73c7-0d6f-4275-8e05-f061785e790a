import { 
  createTradingPoint, 
  updateTradingPoint, 
  deleteTradingPoint, 
  getTradingPoint, 
  getAllTradingPoints,
  getActiveTradingPoints,
  getTradingPointsByCategory,
  getTradingPointsByStatus,
  createCheckIn,
  updateCheckIn,
  deleteCheckIn,
  getCheckIn,
  getUserCheckIns,
  getTradingPointCheckIns,
  getActiveCheckIns,
  CreateTradingPointVariables,
  UpdateTradingPointVariables,
  GetTradingPointVariables,
  GetAllTradingPointsVariables,
  GetActiveTradingPointsVariables,
  GetTradingPointsByCategoryVariables,
  GetTradingPointsByStatusVariables,
  CreateCheckInVariables,
  UpdateCheckInVariables,
  GetCheckInVariables,
  GetUserCheckInsVariables,
  GetTradingPointCheckInsVariables,
  GetActiveCheckInsVariables
} from '../../../dataconnect-generated/js/default-connector';
import { dataConnect } from './config';

export interface TradingPoint {
  id: string;
  name?: string | null;
  description: string;
  address?: string | null;
  latitude?: number | null;
  longitude?: number | null;
  category: string;
  photoUrl?: string | null;
  googlePhotoReference?: string | null;
  googlePlaceId?: string | null;
  boardLocation?: string | null;
  boardsCount: number;
  rating: number;
  status: string;
  ambassadorId?: string | null;
  
  // ===== NOVOS CAMPOS GOOGLE PLACES =====
  displayName?: string | null;
  formattedAddress?: string | null;
  businessStatus?: string | null;
  googleRating?: number | null;
  priceLevel?: number | null;
  phoneNumber?: string | null;
  website?: string | null;
  googleTypes?: string | null;
  openingHours?: string | null;
  googlePhotos?: string | null;
  userRatingsTotal?: number | null;
  lastGoogleSync?: string | null;
  
  createdAt: string;
  updatedAt: string;
}

export interface CheckIn {
  id: string;
  userId: string;
  tradingPointId: string;
  message?: string;
  checkInAt: string;
  checkOutAt?: string;
  user?: {
    id: string;
    username: string;
    displayName: string;
  };
  tradingPoint?: {
    id: string;
    name?: string;
    category: string;
    address?: string;
  };
}

export interface CreateTradingPointData {
  name?: string;
  description: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  category: string;
  photoUrl?: string;
  googlePhotoReference?: string;
  googlePlaceId?: string;
  boardLocation?: string | string[];
  boardsCount?: number;
  status?: string;
  ambassadorId?: string;
  // Novos campos Google Places
  displayName?: string;
  formattedAddress?: string;
  businessStatus?: string;
  googleRating?: number;
  priceLevel?: number;
  phoneNumber?: string;
  website?: string;
  googleTypes?: string;
  openingHours?: string;
  googlePhotos?: string;
  userRatingsTotal?: number;
  lastGoogleSync?: string;
}

export interface CreateCheckInData {
  userId: string;
  tradingPointId: string;
  message?: string;
}

export class TradingPointsDataConnectService {
  // Trading Points Methods
  async create(data: CreateTradingPointData): Promise<{ id: string }> {
    try {
      const variables: CreateTradingPointVariables = {
        name: data.name,
        description: data.description,
        address: data.address,
        latitude: data.latitude,
        longitude: data.longitude,
        category: data.category,
        photoUrl: data.photoUrl,
        googlePhotoReference: data.googlePhotoReference,
        googlePlaceId: data.googlePlaceId,
        boardLocation: Array.isArray(data.boardLocation) 
          ? data.boardLocation.join(', ') 
          : data.boardLocation,
        boardsCount: data.boardsCount,
        status: data.status,
        ambassadorId: data.ambassadorId,
        // Novos campos Google Places
        displayName: data.displayName,
        formattedAddress: data.formattedAddress,
        businessStatus: data.businessStatus,
        googleRating: data.googleRating,
        priceLevel: data.priceLevel,
        phoneNumber: data.phoneNumber,
        website: data.website,
        googleTypes: data.googleTypes,
        openingHours: data.openingHours,
        googlePhotos: data.googlePhotos,
        userRatingsTotal: data.userRatingsTotal,
        lastGoogleSync: data.lastGoogleSync
      };

      const result = await createTradingPoint(dataConnect, variables);
      return { id: result.data.tradingPoint_insert.id };
    } catch (error) {
      console.error('Error creating trading point:', error);
      throw new Error('Failed to create trading point');
    }
  }

  async getById(id: string): Promise<TradingPoint | null> {
    try {
      const variables: GetTradingPointVariables = { id };
      const result = await getTradingPoint(dataConnect, variables);
      
      if (!result.data.tradingPoint) {
        return null;
      }

      const tp = result.data.tradingPoint;
      return {
        id: tp.id,
        name: tp.name,
        description: tp.description,
        address: tp.address,
        latitude: tp.latitude,
        longitude: tp.longitude,
        category: tp.category,
        photoUrl: tp.photoUrl,
        googlePhotoReference: tp.googlePhotoReference,
        googlePlaceId: tp.googlePlaceId,
        boardLocation: tp.boardLocation,
        boardsCount: tp.boardsCount,
        rating: tp.rating,
        status: tp.status,
        ambassadorId: tp.ambassadorId,
        // Novos campos Google Places
        displayName: tp.displayName,
        formattedAddress: tp.formattedAddress,
        businessStatus: tp.businessStatus,
        googleRating: tp.googleRating,
        priceLevel: tp.priceLevel,
        phoneNumber: tp.phoneNumber,
        website: tp.website,
        googleTypes: tp.googleTypes,
        openingHours: tp.openingHours,
        googlePhotos: tp.googlePhotos,
        userRatingsTotal: tp.userRatingsTotal,
        lastGoogleSync: tp.lastGoogleSync,
        createdAt: tp.createdAt,
        updatedAt: tp.updatedAt
      };
    } catch (error) {
      console.error('Error getting trading point:', error);
      throw new Error('Failed to get trading point');
    }
  }

  async getAll(options?: { limit?: number; status?: string; category?: string }): Promise<TradingPoint[]> {
    try {
      if (options?.status) {
        return this.getTradingPointsByStatus(options.status, options.limit);
      }
      
      if (options?.category) {
        return this.getTradingPointsByCategory(options.category, options.limit);
      }

      const variables: GetAllTradingPointsVariables = { limit: options?.limit || 50 };
      const result = await getAllTradingPoints(dataConnect, variables);
      
      return result.data.tradingPoints.map(tp => ({
        id: tp.id,
        name: tp.name,
        description: tp.description,
        address: tp.address,
        latitude: tp.latitude,
        longitude: tp.longitude,
        category: tp.category,
        photoUrl: tp.photoUrl,
        googlePhotoReference: tp.googlePhotoReference,
        googlePlaceId: tp.googlePlaceId,
        boardLocation: tp.boardLocation,
        boardsCount: tp.boardsCount,
        rating: tp.rating,
        status: tp.status,
        ambassadorId: tp.ambassadorId,
        // Novos campos Google Places
        displayName: tp.displayName,
        formattedAddress: tp.formattedAddress,
        businessStatus: tp.businessStatus,
        googleRating: tp.googleRating,
        priceLevel: tp.priceLevel,
        phoneNumber: tp.phoneNumber,
        website: tp.website,
        googleTypes: tp.googleTypes,
        openingHours: tp.openingHours,
        googlePhotos: tp.googlePhotos,
        userRatingsTotal: tp.userRatingsTotal,
        lastGoogleSync: tp.lastGoogleSync,
        createdAt: tp.createdAt,
        updatedAt: tp.updatedAt
      }));
    } catch (error) {
      console.error('Error getting all trading points:', error);
      throw new Error('Failed to get all trading points');
    }
  }

  async getAllTradingPoints(limit = 50): Promise<TradingPoint[]> {
    return this.getAll({ limit });
  }

  async getActiveTradingPoints(limit = 50): Promise<TradingPoint[]> {
    try {
      const variables: GetActiveTradingPointsVariables = { limit };
      const result = await getActiveTradingPoints(dataConnect, variables);
      
      return result.data.tradingPoints.map(tp => ({
        id: tp.id,
        name: tp.name,
        description: tp.description,
        address: tp.address,
        latitude: tp.latitude,
        longitude: tp.longitude,
        category: tp.category,
        photoUrl: tp.photoUrl,
        googlePhotoReference: tp.googlePhotoReference,
        googlePlaceId: tp.googlePlaceId,
        boardLocation: tp.boardLocation,
        boardsCount: tp.boardsCount,
        rating: tp.rating,
        status: tp.status,
        ambassadorId: tp.ambassadorId,
        createdAt: tp.createdAt,
        updatedAt: tp.updatedAt
      }));
    } catch (error) {
      console.error('Error getting active trading points:', error);
      throw new Error('Failed to get active trading points');
    }
  }

  async getTradingPointsByCategory(category: string, limit = 50): Promise<TradingPoint[]> {
    try {
      const variables: GetTradingPointsByCategoryVariables = { category, limit };
      const result = await getTradingPointsByCategory(dataConnect, variables);
      
      return result.data.tradingPoints.map(tp => ({
        id: tp.id,
        name: tp.name,
        description: tp.description,
        address: tp.address,
        latitude: tp.latitude,
        longitude: tp.longitude,
        category: tp.category,
        photoUrl: tp.photoUrl,
        googlePhotoReference: tp.googlePhotoReference,
        googlePlaceId: tp.googlePlaceId,
        boardLocation: tp.boardLocation,
        boardsCount: tp.boardsCount,
        rating: tp.rating,
        status: tp.status,
        ambassadorId: tp.ambassadorId,
        createdAt: tp.createdAt,
        updatedAt: tp.updatedAt
      }));
    } catch (error) {
      console.error('Error getting trading points by category:', error);
      throw new Error('Failed to get trading points by category');
    }
  }

  async getTradingPointsByStatus(status: string, limit = 50): Promise<TradingPoint[]> {
    try {
      const variables: GetTradingPointsByStatusVariables = { status, limit };
      const result = await getTradingPointsByStatus(dataConnect, variables);
      
      return result.data.tradingPoints.map(tp => ({
        id: tp.id,
        name: tp.name,
        description: tp.description,
        address: tp.address,
        latitude: tp.latitude,
        longitude: tp.longitude,
        category: tp.category,
        photoUrl: tp.photoUrl,
        googlePhotoReference: tp.googlePhotoReference,
        googlePlaceId: tp.googlePlaceId,
        boardLocation: tp.boardLocation,
        boardsCount: tp.boardsCount,
        rating: tp.rating,
        status: tp.status,
        ambassadorId: tp.ambassadorId,
        createdAt: tp.createdAt,
        updatedAt: tp.updatedAt
      }));
    } catch (error) {
      console.error('Error getting trading points by status:', error);
      throw new Error('Failed to get trading points by status');
    }
  }

  async update(id: string, data: Partial<CreateTradingPointData>): Promise<void> {
    try {
      const variables: UpdateTradingPointVariables = {
        id,
        name: data.name,
        description: data.description,
        address: data.address,
        latitude: data.latitude,
        longitude: data.longitude,
        category: data.category,
        photoUrl: data.photoUrl,
        googlePhotoReference: data.googlePhotoReference,
        googlePlaceId: data.googlePlaceId,
        boardLocation: Array.isArray(data.boardLocation) 
          ? data.boardLocation.join(', ') 
          : data.boardLocation,
        boardsCount: data.boardsCount,
        status: data.status,
        ambassadorId: data.ambassadorId,
        // Novos campos Google Places
        displayName: data.displayName,
        formattedAddress: data.formattedAddress,
        businessStatus: data.businessStatus,
        googleRating: data.googleRating,
        priceLevel: data.priceLevel,
        phoneNumber: data.phoneNumber,
        website: data.website,
        googleTypes: data.googleTypes,
        openingHours: data.openingHours,
        googlePhotos: data.googlePhotos,
        userRatingsTotal: data.userRatingsTotal,
        lastGoogleSync: data.lastGoogleSync
      };

      await updateTradingPoint(dataConnect, variables);
    } catch (error) {
      console.error('Error updating trading point:', error);
      throw new Error('Failed to update trading point');
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await deleteTradingPoint(dataConnect, { id });
    } catch (error) {
      console.error('Error deleting trading point:', error);
      throw new Error('Failed to delete trading point');
    }
  }

  // Helper methods for compatibility with old service
  async search(query: string, options?: { limit?: number }): Promise<TradingPoint[]> {
    const allTradingPoints = await this.getAll({ limit: options?.limit || 100 });
    return allTradingPoints.filter(tp => 
      (tp.name && tp.name.toLowerCase().includes(query.toLowerCase())) ||
      tp.description.toLowerCase().includes(query.toLowerCase()) ||
      (tp.address && tp.address.toLowerCase().includes(query.toLowerCase()))
    );
  }

  async getNearby(lat: number, lng: number, radius = 5000): Promise<TradingPoint[]> {
    // TODO: Implementar busca por localização com cálculo de distância
    const allTradingPoints = await this.getAll({ limit: 100 });
    return allTradingPoints.filter(tp => tp.latitude && tp.longitude);
  }

  async getStatistics(): Promise<any> {
    try {
      const allPoints = await this.getAll({ limit: 1000 });
      const activePoints = allPoints.filter(tp => tp.status === 'active');
      
      return {
        total: allPoints.length,
        active: activePoints.length,
        inactive: allPoints.length - activePoints.length,
        categories: this.groupByCategory(allPoints),
        avgRating: this.calculateAverageRating(allPoints)
      };
    } catch (error) {
      console.error('Error getting statistics:', error);
      throw new Error('Failed to get statistics');
    }
  }

  private groupByCategory(points: TradingPoint[]): Record<string, number> {
    return points.reduce((acc, point) => {
      acc[point.category] = (acc[point.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private calculateAverageRating(points: TradingPoint[]): number {
    if (points.length === 0) return 0;
    const total = points.reduce((sum, point) => sum + point.rating, 0);
    return Math.round((total / points.length) * 10) / 10;
  }

  // Check-in Methods
  async createCheckIn(data: CreateCheckInData): Promise<{ id: string }> {
    try {
      const variables: CreateCheckInVariables = {
        userId: data.userId,
        tradingPointId: data.tradingPointId,
        message: data.message
      };

      const result = await createCheckIn(dataConnect, variables);
      return { id: result.data.checkIn_insert.id };
    } catch (error) {
      console.error('Error creating check-in:', error);
      throw new Error('Failed to create check-in');
    }
  }

  async getCheckIn(id: string): Promise<CheckIn | null> {
    try {
      const variables: GetCheckInVariables = { id };
      const result = await getCheckIn(dataConnect, variables);
      
      if (!result.data.checkIn) {
        return null;
      }

      const checkIn = result.data.checkIn;
      return {
        id: checkIn.id,
        userId: checkIn.user.id,
        tradingPointId: checkIn.tradingPoint.id,
        message: checkIn.message,
        checkInAt: checkIn.checkInAt,
        checkOutAt: checkIn.checkOutAt,
        user: {
          id: checkIn.user.id,
          username: checkIn.user.username,
          displayName: checkIn.user.displayName
        },
        tradingPoint: {
          id: checkIn.tradingPoint.id,
          name: checkIn.tradingPoint.name,
          category: checkIn.tradingPoint.category
        }
      };
    } catch (error) {
      console.error('Error getting check-in:', error);
      throw new Error('Failed to get check-in');
    }
  }

  async getUserCheckIns(userId: string, limit = 20): Promise<CheckIn[]> {
    try {
      const variables: GetUserCheckInsVariables = { userId, limit };
      const result = await getUserCheckIns(dataConnect, variables);
      
      return result.data.checkIns.map(checkIn => ({
        id: checkIn.id,
        userId,
        tradingPointId: checkIn.tradingPoint.id,
        message: checkIn.message,
        checkInAt: checkIn.checkInAt,
        checkOutAt: checkIn.checkOutAt,
        tradingPoint: {
          id: checkIn.tradingPoint.id,
          name: checkIn.tradingPoint.name,
          category: checkIn.tradingPoint.category,
          address: checkIn.tradingPoint.address
        }
      }));
    } catch (error) {
      console.error('Error getting user check-ins:', error);
      throw new Error('Failed to get user check-ins');
    }
  }

  async getTradingPointCheckIns(tradingPointId: string, limit = 20): Promise<CheckIn[]> {
    try {
      const variables: GetTradingPointCheckInsVariables = { tradingPointId, limit };
      const result = await getTradingPointCheckIns(dataConnect, variables);
      
      return result.data.checkIns.map(checkIn => ({
        id: checkIn.id,
        userId: checkIn.user.id,
        tradingPointId,
        message: checkIn.message,
        checkInAt: checkIn.checkInAt,
        checkOutAt: checkIn.checkOutAt,
        user: {
          id: checkIn.user.id,
          username: checkIn.user.username,
          displayName: checkIn.user.displayName
        }
      }));
    } catch (error) {
      console.error('Error getting trading point check-ins:', error);
      throw new Error('Failed to get trading point check-ins');
    }
  }

  async getActiveCheckIns(tradingPointId: string): Promise<CheckIn[]> {
    try {
      const variables: GetActiveCheckInsVariables = { tradingPointId };
      const result = await getActiveCheckIns(dataConnect, variables);
      
      return result.data.checkIns.map(checkIn => ({
        id: checkIn.id,
        userId: checkIn.user.id,
        tradingPointId,
        message: checkIn.message,
        checkInAt: checkIn.checkInAt,
        checkOutAt: undefined,
        user: {
          id: checkIn.user.id,
          username: checkIn.user.username,
          displayName: checkIn.user.displayName
        }
      }));
    } catch (error) {
      console.error('Error getting active check-ins:', error);
      throw new Error('Failed to get active check-ins');
    }
  }

  async updateCheckIn(id: string, data: { message?: string; checkOutAt?: string }): Promise<void> {
    try {
      const variables: UpdateCheckInVariables = {
        id,
        message: data.message,
        checkOutAt: data.checkOutAt
      };

      await updateCheckIn(dataConnect, variables);
    } catch (error) {
      console.error('Error updating check-in:', error);
      throw new Error('Failed to update check-in');
    }
  }

  async deleteCheckIn(id: string): Promise<void> {
    try {
      await deleteCheckIn(dataConnect, { id });
    } catch (error) {
      console.error('Error deleting check-in:', error);
      throw new Error('Failed to delete check-in');
    }
  }

  // Additional compatibility methods
  async bulkUpdate(ids: string[], data: Partial<CreateTradingPointData>, userId?: string): Promise<void> {
    try {
      await Promise.all(ids.map(id => this.update(id, data)));
    } catch (error) {
      console.error('Error bulk updating trading points:', error);
      throw new Error('Failed to bulk update trading points');
    }
  }

  async bulkDelete(ids: string[]): Promise<void> {
    try {
      await Promise.all(ids.map(id => this.delete(id)));
    } catch (error) {
      console.error('Error bulk deleting trading points:', error);
      throw new Error('Failed to bulk delete trading points');
    }
  }
} 