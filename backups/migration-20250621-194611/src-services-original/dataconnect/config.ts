import { getApp } from 'firebase/app';
import { getDataConnect, connectDataConnectEmulator } from 'firebase/data-connect';
import { connectorConfig } from '../../../dataconnect-generated/js/default-connector';

// Import Firebase configuration to ensure it's initialized first
import '../firebaseConfig';

// Use the existing Firebase app instance instead of creating a new one
const app = getApp();

export const dataConnect = getDataConnect(app, connectorConfig);

// Para desenvolvimento, conecte ao emulador se necessário
// if (process.env.NODE_ENV === 'development') {
//   connectDataConnectEmulator(dataConnect, 'localhost', 9399);
// } 