import { storage } from './firebase';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { v4 as uuidv4 } from 'uuid';

export interface UploadedFile {
  id: string;
  url: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadedAt: string;
}

export interface UploadProgress {
  fileId: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
}

class FileUploadService {
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/heic'];
  private readonly ALLOWED_DOCUMENT_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];

  /**
   * Validate file before upload
   */
  validateFile(file: File): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File size must be less than ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`
      };
    }

    // Check file type - including HEIC support
    const isHeic = file.name.toLowerCase().endsWith('.heic') || file.name.toLowerCase().endsWith('.heif');
    const isImage = this.ALLOWED_IMAGE_TYPES.includes(file.type) || isHeic;
    const isDocument = this.ALLOWED_DOCUMENT_TYPES.includes(file.type);

    if (!isImage && !isDocument) {
      return {
        valid: false,
        error: 'File type not supported. Please upload images (JPEG, PNG, GIF, WebP, HEIC) or documents (PDF, DOC, DOCX, TXT, XLS, XLSX)'
      };
    }

    return { valid: true };
  }

  /**
   * Upload file to Firebase Storage
   */
  async uploadFile(
    file: File, 
    userId: string, 
    conversationId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadedFile> {
    // Validate file
    const validation = this.validateFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    const fileId = uuidv4();
    const fileExtension = file.name.split('.').pop() || '';
    const fileName = `${fileId}.${fileExtension}`;
    
    // Determine folder based on file type
    const isImage = this.ALLOWED_IMAGE_TYPES.includes(file.type);
    const folder = isImage ? 'message-images' : 'message-documents';
    
    // Create storage reference
    const storageRef = ref(storage, `messages/${folder}/${userId}/${conversationId}/${fileName}`);

    try {
      // Notify upload start
      onProgress?.({
        fileId,
        progress: 0,
        status: 'uploading'
      });

      // Upload file
      const snapshot = await uploadBytes(storageRef, file);
      
      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);

      // Notify upload completion
      onProgress?.({
        fileId,
        progress: 100,
        status: 'completed'
      });

      return {
        id: fileId,
        url: downloadURL,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        uploadedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error('File upload error:', error);
      
      // Notify upload error
      onProgress?.({
        fileId,
        progress: 0,
        status: 'error',
        error: error instanceof Error ? error.message : 'Upload failed'
      });

      throw new Error('Failed to upload file. Please try again.');
    }
  }

  /**
   * Upload multiple files
   */
  async uploadMultipleFiles(
    files: File[],
    userId: string,
    conversationId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadedFile[]> {
    const uploadPromises = files.map(file => 
      this.uploadFile(file, userId, conversationId, onProgress)
    );

    try {
      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('Multiple file upload error:', error);
      throw error;
    }
  }

  /**
   * Delete file from storage
   */
  async deleteFile(fileUrl: string): Promise<void> {
    try {
      const fileRef = ref(storage, fileUrl);
      await deleteObject(fileRef);
    } catch (error) {
      console.error('File deletion error:', error);
      throw new Error('Failed to delete file');
    }
  }

  /**
   * Get file type category
   */
  getFileCategory(fileType: string): 'image' | 'document' {
    return this.ALLOWED_IMAGE_TYPES.includes(fileType) ? 'image' : 'document';
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get file icon based on type
   */
  getFileIcon(fileType: string): string {
    if (this.ALLOWED_IMAGE_TYPES.includes(fileType)) {
      return '🖼️';
    }

    switch (fileType) {
      case 'application/pdf':
        return '📄';
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return '📝';
      case 'application/vnd.ms-excel':
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return '📊';
      case 'text/plain':
        return '📃';
      default:
        return '📎';
    }
  }

  /**
   * Create preview URL for images
   */
  createPreviewUrl(file: File): string | null {
    if (this.ALLOWED_IMAGE_TYPES.includes(file.type)) {
      return URL.createObjectURL(file);
    }
    return null;
  }

  /**
   * Revoke preview URL to free memory
   */
  revokePreviewUrl(url: string): void {
    URL.revokeObjectURL(url);
  }
}

export const fileUploadService = new FileUploadService(); 