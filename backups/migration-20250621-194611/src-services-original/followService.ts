import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  writeBatch,
  increment,
  serverTimestamp
} from 'firebase/firestore';
import { db } from './firebase';
import { Follow } from '@/types/user';
import { COLLECTIONS } from './database/schema';

export interface FollowStats {
  followersCount: number;
  followingCount: number;
}

class FollowService {
  private followsCollection = collection(db, COLLECTIONS.FOLLOWS);
  private usersCollection = collection(db, 'users');

  /**
   * Follow a user
   */
  async followUser(data: {
    followerId: string;
    followerName: string;
    followerAvatarUrl?: string;
    followingId: string;
    followingName: string;
    followingAvatarUrl?: string;
  }): Promise<Follow> {
    try {
      // Check if already following
      const existingFollow = await this.getFollow(data.followerId, data.followingId);
      if (existingFollow) {
        throw new Error('Already following this user');
      }

      const now = Timestamp.now();
      const followData = {
        ...data,
        createdAt: now,
        isActive: true
      };

      const docRef = await addDoc(this.followsCollection, followData);
      
      return {
        id: docRef.id,
        ...followData
      };
    } catch (error) {
      console.error('Error following user:', error);
      throw new Error('Failed to follow user');
    }
  }

  /**
   * Unfollow a user
   */
  async unfollowUser(followerId: string, followingId: string): Promise<void> {
    try {
      const q = query(
        this.followsCollection,
        where('followerId', '==', followerId),
        where('followingId', '==', followingId),
        where('isActive', '==', true)
      );

      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        throw new Error('Follow relationship not found');
      }

      const batch = writeBatch(db);
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error unfollowing user:', error);
      throw new Error('Failed to unfollow user');
    }
  }

  /**
   * Get a specific follow relationship
   */
  async getFollow(followerId: string, followingId: string): Promise<Follow | null> {
    try {
      const q = query(
        this.followsCollection,
        where('followerId', '==', followerId),
        where('followingId', '==', followingId),
        where('isActive', '==', true)
      );

      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        return null;
      }

      const doc = snapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      } as Follow;
    } catch (error) {
      console.error('Error getting follow relationship:', error);
      throw new Error('Failed to get follow relationship');
    }
  }

  /**
   * Check if user A is following user B
   */
  async isFollowing(followerId: string, followingId: string): Promise<boolean> {
    try {
      const follow = await this.getFollow(followerId, followingId);
      return follow !== null;
    } catch (error) {
      console.error('Error checking follow status:', error);
      return false;
    }
  }

  /**
   * Get followers of a user
   */
  async getFollowers(userId: string, limitCount: number = 50): Promise<Follow[]> {
    try {
      const q = query(
        this.followsCollection,
        where('followingId', '==', userId),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      const realData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Follow));

      // Return real data or empty array if none found
      return realData;
    } catch (error) {
      console.error('Error fetching followers:', error);
      // Return empty array instead of mock data
      return [];
    }
  }

  /**
   * Get users that a user is following
   */
  async getFollowing(userId: string, limitCount: number = 50): Promise<Follow[]> {
    try {
      const q = query(
        this.followsCollection,
        where('followerId', '==', userId),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      const realData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Follow));

      // Return real data or empty array if none found
      return realData;
    } catch (error) {
      console.error('Error fetching following:', error);
      // Return empty array instead of mock data
      return [];
    }
  }

  /**
   * Get mock followers data for testing
   */
  private getMockFollowers(): Follow[] {
    const now = Timestamp.now();
    return [
      {
        id: 'mock-1',
        followerId: 'user-1',
        followerName: 'Sarah Collector',
        followerAvatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        followingId: 'liamveteran',
        followingName: 'Liam Veteran',
        followingAvatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        createdAt: now,
        isActive: true
      },
      {
        id: 'mock-2',
        followerId: 'user-2',
        followerName: 'Mike Trader',
        followerAvatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        followingId: 'liamveteran',
        followingName: 'Liam Veteran',
        followingAvatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        createdAt: now,
        isActive: true
      },
      {
        id: 'mock-3',
        followerId: 'user-3',
        followerName: 'Disney Fan',
        followerAvatarUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        followingId: 'liamveteran',
        followingName: 'Liam Veteran',
        followingAvatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        createdAt: now,
        isActive: true
      },
      {
        id: 'mock-4',
        followerId: 'user-4',
        followerName: 'Pin Master',
        followerAvatarUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        followingId: 'liamveteran',
        followingName: 'Liam Veteran',
        followingAvatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        createdAt: now,
        isActive: true
      },
      {
        id: 'mock-5',
        followerId: 'user-5',
        followerName: 'Emma Collector',
        followerAvatarUrl: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        followingId: 'liamveteran',
        followingName: 'Liam Veteran',
        followingAvatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        createdAt: now,
        isActive: true
      }
    ];
  }

  /**
   * Get mock following data for testing
   */
  private getMockFollowing(): Follow[] {
    const now = Timestamp.now();
    return [
      {
        id: 'mock-f-1',
        followerId: 'liamveteran',
        followerName: 'Liam Veteran',
        followerAvatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        followingId: 'user-1',
        followingName: 'Disney Princess',
        followingAvatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        createdAt: now,
        isActive: true
      },
      {
        id: 'mock-f-2',
        followerId: 'liamveteran',
        followerName: 'Liam Veteran',
        followerAvatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        followingId: 'user-2',
        followingName: 'Marvel Collector',
        followingAvatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        createdAt: now,
        isActive: true
      },
      {
        id: 'mock-f-3',
        followerId: 'liamveteran',
        followerName: 'Liam Veteran',
        followerAvatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        followingId: 'user-3',
        followingName: 'Vintage Hunter',
        followingAvatarUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        createdAt: now,
        isActive: true
      },
      {
        id: 'mock-f-4',
        followerId: 'liamveteran',
        followerName: 'Liam Veteran',
        followerAvatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        followingId: 'user-4',
        followingName: 'Pin Trader Pro',
        followingAvatarUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        createdAt: now,
        isActive: true
      }
    ];
  }

  /**
   * Get follower count for a user
   */
  async getFollowerCount(userId: string): Promise<number> {
    try {
      const q = query(
        this.followsCollection,
        where('followingId', '==', userId),
        where('isActive', '==', true)
      );

      const snapshot = await getDocs(q);
      return snapshot.size;
    } catch (error) {
      console.error('Error getting follower count:', error);
      return 0;
    }
  }

  /**
   * Get following count for a user
   */
  async getFollowingCount(userId: string): Promise<number> {
    try {
      const q = query(
        this.followsCollection,
        where('followerId', '==', userId),
        where('isActive', '==', true)
      );

      const snapshot = await getDocs(q);
      return snapshot.size;
    } catch (error) {
      console.error('Error getting following count:', error);
      return 0;
    }
  }

  /**
   * Get mutual followers between two users
   */
  async getMutualFollowers(userId1: string, userId2: string): Promise<Follow[]> {
    try {
      // Get followers of both users
      const [followers1, followers2] = await Promise.all([
        this.getFollowers(userId1),
        this.getFollowers(userId2)
      ]);

      // Find mutual followers
      const mutualFollowers: Follow[] = [];
      followers1.forEach(follower1 => {
        const mutual = followers2.find(follower2 => 
          follower1.followerId === follower2.followerId
        );
        if (mutual) {
          mutualFollowers.push(follower1);
        }
      });

      return mutualFollowers;
    } catch (error) {
      console.error('Error getting mutual followers:', error);
      throw new Error('Failed to get mutual followers');
    }
  }

  /**
   * Get suggested users to follow (users followed by people you follow)
   */
  async getSuggestedFollows(userId: string, limitCount: number = 10): Promise<Follow[]> {
    try {
      // Get users that the current user is following
      const following = await this.getFollowing(userId);
      
      if (following.length === 0) {
        return [];
      }

      // Get users followed by people the current user follows
      const suggestions: Follow[] = [];
      const seenUserIds = new Set([userId]); // Don't suggest the user themselves
      
      for (const follow of following.slice(0, 10)) { // Limit to avoid too many queries
        const theirFollowing = await this.getFollowing(follow.followingId, 20);
        
        theirFollowing.forEach(suggestion => {
          if (!seenUserIds.has(suggestion.followingId)) {
            seenUserIds.add(suggestion.followingId);
            suggestions.push(suggestion);
          }
        });
      }

      // Remove users already followed by current user
      const currentFollowingIds = new Set(following.map(f => f.followingId));
      const filteredSuggestions = suggestions.filter(
        suggestion => !currentFollowingIds.has(suggestion.followingId)
      );

      // Sort by most recent and limit
      return filteredSuggestions
        .sort((a, b) => b.createdAt.toMillis() - a.createdAt.toMillis())
        .slice(0, limitCount);
    } catch (error) {
      console.error('Error getting suggested follows:', error);
      throw new Error('Failed to get suggested follows');
    }
  }

  /**
   * Get recent followers (for notifications)
   */
  async getRecentFollowers(userId: string, limitCount: number = 10): Promise<Follow[]> {
    try {
      const q = query(
        this.followsCollection,
        where('followingId', '==', userId),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Follow));
    } catch (error) {
      console.error('Error fetching recent followers:', error);
      throw new Error('Failed to fetch recent followers');
    }
  }

  /**
   * Bulk follow multiple users
   */
  async bulkFollow(followerId: string, followerName: string, followerAvatarUrl: string | undefined, followingUsers: Array<{
    id: string;
    name: string;
    avatarUrl?: string;
  }>): Promise<void> {
    try {
      const batch = writeBatch(db);
      const now = Timestamp.now();

      followingUsers.forEach(user => {
        const followRef = doc(this.followsCollection);
        batch.set(followRef, {
          followerId,
          followerName,
          followerAvatarUrl,
          followingId: user.id,
          followingName: user.name,
          followingAvatarUrl: user.avatarUrl,
          createdAt: now,
          isActive: true
        });
      });

      await batch.commit();
    } catch (error) {
      console.error('Error bulk following users:', error);
      throw new Error('Failed to bulk follow users');
    }
  }

  /**
   * Remove all follows for a user (when deactivating account)
   */
  async removeAllFollowsForUser(userId: string): Promise<void> {
    try {
      const batch = writeBatch(db);

      // Remove where user is follower
      const followingQuery = query(
        this.followsCollection,
        where('followerId', '==', userId)
      );
      const followingSnapshot = await getDocs(followingQuery);
      followingSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      // Remove where user is being followed
      const followersQuery = query(
        this.followsCollection,
        where('followingId', '==', userId)
      );
      const followersSnapshot = await getDocs(followersQuery);
      followersSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error removing all follows for user:', error);
      throw new Error('Failed to remove all follows for user');
    }
  }

  // Get follow statistics for a user
  async getFollowStats(userId: string): Promise<FollowStats> {
    try {
      const userDoc = await getDoc(doc(this.usersCollection, userId));
      
      if (userDoc.exists()) {
        const userData = userDoc.data();
        return {
          followersCount: userData.statistics?.followersCount || 0,
          followingCount: userData.statistics?.followingCount || 0
        };
      }
      
      return { followersCount: 0, followingCount: 0 };
    } catch (error) {
      console.error('Error getting follow stats:', error);
      return { followersCount: 0, followingCount: 0 };
    }
  }
}

export const followService = new FollowService(); 