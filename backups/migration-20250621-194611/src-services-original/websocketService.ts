export interface WebSocketMessage {
  type: 'authenticate' | 'new_message' | 'message_read' | 'typing' | 'authenticated' | 'ping' | 'pong' | 'user_online' | 'user_offline';
  userId?: string;
  conversationId?: string;
  messageId?: string;
  message?: any;
  isTyping?: boolean;
  timestamp?: string;
}

export interface MessageNotification {
  type: 'new_message' | 'message_read' | 'typing';
  data: any;
}

export class WebSocketService {
  private ws: WebSocket | null = null;
  private userId: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private pingInterval: NodeJS.Timeout | null = null;
  private listeners: Map<string, ((data: any) => void)[]> = new Map();

  constructor() {
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // Setup listeners map
    this.listeners.set('new_message', []);
    this.listeners.set('message_read', []);
    this.listeners.set('typing', []);
    this.listeners.set('authenticated', []);
    this.listeners.set('pong', []);
    this.listeners.set('user_online', []);
    this.listeners.set('user_offline', []);
  }

  connect(userId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.userId = userId;
        const wsUrl = `ws://localhost:3001`;
        
        console.log('🔌 Connecting to WebSocket:', wsUrl);
        console.log(`🔄 Attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts + 1}`);
        
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('✅ WebSocket connected');
          this.reconnectAttempts = 0;
          
          // Authenticate user
          this.send({
            type: 'authenticate',
            userId: this.userId!
          });

          // Setup ping interval
          this.setupPingInterval();
          
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data: WebSocketMessage = JSON.parse(event.data);
            console.log('📨 WebSocket message received:', data);
            
            // Emit to listeners
            this.emit(data.type, data);
          } catch (error) {
            console.error('❌ Error parsing WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('🔌 WebSocket disconnected:', event.code, event.reason);
          this.cleanup();
          
          // Attempt to reconnect
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            
            setTimeout(() => {
              if (this.userId) {
                this.connect(this.userId);
              }
            }, this.reconnectDelay * this.reconnectAttempts);
          } else {
            console.error('❌ Max reconnection attempts reached');
            reject(new Error('Failed to connect to WebSocket'));
          }
        };

        this.ws.onerror = (error) => {
          console.error('❌ WebSocket error:', error);
          console.error('❌ Failed to connect WebSocket:', error);
          
          // Don't reject immediately, let the onclose handler handle reconnection
          if (this.reconnectAttempts === 0) {
            reject(error);
          }
        };

      } catch (error) {
        console.error('❌ Error creating WebSocket connection:', error);
        reject(error);
      }
    });
  }

  disconnect() {
    console.log('🔌 Disconnecting WebSocket');
    this.cleanup();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.userId = null;
  }

  private cleanup() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private setupPingInterval() {
    this.pingInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 'ping' });
      }
    }, 30000); // Ping every 30 seconds
  }

  send(message: WebSocketMessage) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('⚠️ WebSocket not connected, cannot send message:', message);
    }
  }

  // Event listener methods
  on(event: string, callback: (data: any) => void) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: (data: any) => void) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(callback);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ Error in WebSocket event listener for ${event}:`, error);
        }
      });
    }
  }

  // Convenience methods for common actions
  sendTypingStatus(conversationId: string, isTyping: boolean) {
    if (this.userId) {
      this.send({
        type: 'typing',
        userId: this.userId,
        conversationId,
        isTyping
      });
    }
  }

  // Check connection status
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  getConnectionState(): string {
    if (!this.ws) return 'DISCONNECTED';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING';
      case WebSocket.OPEN: return 'CONNECTED';
      case WebSocket.CLOSING: return 'CLOSING';
      case WebSocket.CLOSED: return 'DISCONNECTED';
      default: return 'UNKNOWN';
    }
  }
}

// Export singleton instance
export const websocketService = new WebSocketService(); 