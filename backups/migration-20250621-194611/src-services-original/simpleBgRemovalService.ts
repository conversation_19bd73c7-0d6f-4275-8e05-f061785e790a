/**
 * Simple Background Removal Service
 * 
 * Lightweight solution for removing backgrounds from pin images
 * specifically designed for Disney pins with well-defined edges.
 * 
 * This service uses canvas-based algorithms instead of heavy AI models,
 * making it much faster and more suitable for simple objects like pins.
 */

export interface SimpleBgRemovalConfig {
  /** Edge detection sensitivity (0-100) */
  edgeThreshold?: number;
  /** Color difference threshold (0-255) */
  colorThreshold?: number;
  /** Blur radius for edge smoothing */
  blurRadius?: number;
  /** Enable smart cropping to focus on the pin */
  enableSmartCrop?: boolean;
  /** Quality of the output (0.1-1.0) */
  quality?: number;
  /** Progress callback function */
  onProgress?: (progress: { step: string; percentage: number }) => void;
  /** Maximum dimension for processing (auto-resize if larger). Default: 2000px */
  maxProcessingDimension?: number;
  /** Disable auto-resize (process original size even if large) */
  disableAutoResize?: boolean;
}

export interface SimpleBgRemovalResult {
  originalBlob: Blob;
  processedBlob: Blob;
  originalUrl: string;
  processedUrl: string;
  processingTime: number;
}

class SimpleBgRemovalService {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor() {
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d')!;
  }

  /**
   * Remove background from a pin image using simple edge detection
   */
  async removeBackground(
    file: File,
    config: SimpleBgRemovalConfig = {}
  ): Promise<SimpleBgRemovalResult> {
    const startTime = Date.now();
    const TIMEOUT_MS = 30000; // 30 seconds timeout
    
    const {
      edgeThreshold = 50,
      colorThreshold = 40,
      blurRadius = 2,
      enableSmartCrop = true,
      quality = 0.95
    } = config;

    console.log('🎯 Starting simple background removal for pin...');
    
    // Create timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error('Background removal timed out after 30 seconds'));
      }, TIMEOUT_MS);
    });
    
    try {
      // Wrap processing in timeout
      const result = await Promise.race([
        this.processImageInternal(file, config, startTime),
        timeoutPromise
      ]);
      
      return result;
      
    } catch (error) {
      console.error('❌ Simple background removal failed:', error);
      throw new Error(`Background removal failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Internal processing method
   */
  private async processImageInternal(
    file: File,
    config: SimpleBgRemovalConfig,
    startTime: number
  ): Promise<SimpleBgRemovalResult> {
    const {
      edgeThreshold = 50,
      colorThreshold = 40,
      blurRadius = 2,
      enableSmartCrop = true,
      quality = 0.95,
      onProgress,
      maxProcessingDimension = 2000,
      disableAutoResize = false
    } = config;

    // Progress tracking
    const reportProgress = (step: string, percentage: number) => {
      if (onProgress) {
        onProgress({ step, percentage });
      }
    };

    reportProgress('Loading image...', 10);

    // Load image
    let processedFile = file;
    const img = await this.loadImage(file);
    
    // Auto-resize large images for better performance (unless disabled)
    const ABSOLUTE_MAX_DIMENSION = 4000; // Hard limit
    
    if (!disableAutoResize && (img.width > maxProcessingDimension || img.height > maxProcessingDimension)) {
      reportProgress('Optimizing image size...', 15);
      console.log(`📏 Large image detected (${img.width}x${img.height}), resizing to ${maxProcessingDimension}px for optimal processing...`);
      
      try {
        processedFile = await this.resizeImageForProcessing(file, maxProcessingDimension);
        console.log(`✅ Image resized for processing (performance optimization)`);
      } catch (resizeError) {
        console.warn('⚠️ Failed to resize image, proceeding with original:', resizeError);
        
        // If resize fails and image is too large, throw error
        if (img.width * img.height > ABSOLUTE_MAX_DIMENSION * ABSOLUTE_MAX_DIMENSION) {
          throw new Error(`Image too large for processing (${img.width}x${img.height} pixels). Maximum: ${ABSOLUTE_MAX_DIMENSION}x${ABSOLUTE_MAX_DIMENSION} pixels`);
        }
      }
    } else if (disableAutoResize && img.width * img.height > ABSOLUTE_MAX_DIMENSION * ABSOLUTE_MAX_DIMENSION) {
      // Even with auto-resize disabled, we have an absolute hard limit
      throw new Error(`Image too large for processing (${img.width}x${img.height} pixels). Maximum: ${ABSOLUTE_MAX_DIMENSION}x${ABSOLUTE_MAX_DIMENSION} pixels`);
    } else if (!disableAutoResize) {
      console.log(`📏 Image size OK (${img.width}x${img.height}), processing at original resolution`);
    } else {
      console.log(`📏 Auto-resize disabled, processing at original resolution (${img.width}x${img.height})`);
    }
    
    reportProgress('Preparing canvas...', 20);

    // Load the processed image (might be resized)
    const processedImg = processedFile !== file ? await this.loadImage(processedFile) : img;

    // Set canvas size
    this.canvas.width = processedImg.width;
    this.canvas.height = processedImg.height;
    
    // Draw processed image
    this.ctx.drawImage(processedImg, 0, 0);
    
    // Get image data
    const imageData = this.ctx.getImageData(0, 0, processedImg.width, processedImg.height);
    const data = imageData.data;
    
    console.log(`📐 Processing image: ${processedImg.width}x${processedImg.height} pixels`);
    
    reportProgress('Analyzing image...', 30);

          // Create mask using edge detection and color analysis
      const mask = this.createPinMask(
        data, 
        processedImg.width, 
        processedImg.height, 
        edgeThreshold, 
        colorThreshold
      );
    
    reportProgress('Smoothing edges...', 70);

    // Apply blur to smooth edges
    if (blurRadius > 0) {
      this.blurMask(mask, processedImg.width, processedImg.height, blurRadius);
    }
    
    reportProgress('Removing background...', 80);

    // Apply mask to remove background
    this.applyMask(data, mask);
    
    // Smart crop to focus on the pin
    let finalImageData = imageData;
    if (enableSmartCrop) {
      reportProgress('Smart cropping...', 85);
      finalImageData = this.smartCrop(imageData, mask, processedImg.width, processedImg.height);
    }
    
    reportProgress('Finalizing image...', 90);

    // Put processed image back to canvas
    this.canvas.width = finalImageData.width;
    this.canvas.height = finalImageData.height;
    this.ctx.putImageData(finalImageData, 0, 0);
    
    // Convert to blob
    const processedBlob = await new Promise<Blob>((resolve, reject) => {
      try {
        this.canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create blob from canvas'));
          }
        }, 'image/png', quality);
      } catch (error) {
        reject(error);
      }
    });
    
    reportProgress('Creating preview...', 95);

    // Create URLs
    const originalUrl = URL.createObjectURL(file);
    const processedUrl = URL.createObjectURL(processedBlob);
    
    const processingTime = Date.now() - startTime;
    
    reportProgress('Complete!', 100);

    console.log(`✅ Simple background removal completed in ${processingTime}ms`);
    console.log(`📊 File size: ${(file.size / 1024).toFixed(1)}KB → ${(processedBlob.size / 1024).toFixed(1)}KB`);
    
    return {
      originalBlob: file,
      processedBlob,
      originalUrl,
      processedUrl,
      processingTime
    };
  }

  /**
   * Load image from file
   */
  private loadImage(file: File): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Resize image for optimal processing performance
   */
  private async resizeImageForProcessing(file: File, maxDimension: number): Promise<File> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        try {
          // Calculate new dimensions maintaining aspect ratio
          let { width, height } = img;
          const aspectRatio = width / height;
          
          if (width > height) {
            if (width > maxDimension) {
              width = maxDimension;
              height = width / aspectRatio;
            }
          } else {
            if (height > maxDimension) {
              height = maxDimension;
              width = height * aspectRatio;
            }
          }
          
          // Create canvas for resizing
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            reject(new Error('Failed to get canvas context'));
            return;
          }
          
          canvas.width = Math.round(width);
          canvas.height = Math.round(height);
          
          // Use high-quality scaling
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
          
          // Draw resized image
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
          
          // Convert to blob
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const resizedFile = new File([blob], file.name, {
                  type: file.type,
                  lastModified: Date.now()
                });
                console.log(`📏 Resized from ${img.width}x${img.height} to ${canvas.width}x${canvas.height}`);
                resolve(resizedFile);
              } else {
                reject(new Error('Failed to create resized image blob'));
              }
            },
            file.type,
            0.9 // High quality for processing
          );
        } catch (error) {
          reject(error);
        }
      };
      
      img.onerror = () => reject(new Error('Failed to load image for resizing'));
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Create a mask for the pin using edge detection and color analysis
   */
  private createPinMask(
    data: Uint8ClampedArray,
    width: number,
    height: number,
    edgeThreshold: number,
    colorThreshold: number
  ): Uint8ClampedArray {
    console.log('🔍 Creating pin mask...');
    const mask = new Uint8ClampedArray(width * height);
    
    // Detect the most common background color (usually corners)
    const bgColor = this.detectBackgroundColor(data, width, height);
    console.log(`📊 Background color detected: RGB(${bgColor.r}, ${bgColor.g}, ${bgColor.b})`);
    
    // Create initial mask based on color difference
    console.log('🎨 Creating initial color-based mask...');
    for (let i = 0; i < mask.length; i++) {
      const pixelIndex = i * 4;
      const r = data[pixelIndex];
      const g = data[pixelIndex + 1];
      const b = data[pixelIndex + 2];
      
      // Use Manhattan distance for faster calculation
      const colorDiff = Math.abs(r - bgColor.r) + Math.abs(g - bgColor.g) + Math.abs(b - bgColor.b);
      
      // If color is significantly different from background, it's likely the pin
      mask[i] = colorDiff > colorThreshold * 3 ? 255 : 0; // Adjust threshold for Manhattan distance
    }
    
    // Refine mask using edge detection (skip for very large images to prevent timeout)
    if (width * height < 2000 * 2000) {
      console.log('🔍 Refining with edge detection...');
      this.refineWithEdgeDetection(data, mask, width, height, edgeThreshold);
    } else {
      console.log('⏭️ Skipping edge detection for large image');
    }
    
    // Fill holes inside the pin
    console.log('🕳️ Filling holes...');
    this.fillHoles(mask, width, height);
    
    console.log('✅ Pin mask created successfully');
    return mask;
  }

  /**
   * Detect the most common background color (usually uniform)
   */
  private detectBackgroundColor(data: Uint8ClampedArray, width: number, height: number) {
    const samples: { r: number; g: number; b: number }[] = [];
    
    // Sample corners and edges (likely background)
    const samplePoints = [
      [0, 0], [width-1, 0], [0, height-1], [width-1, height-1], // corners
      [Math.floor(width/2), 0], [Math.floor(width/2), height-1], // top/bottom center
      [0, Math.floor(height/2)], [width-1, Math.floor(height/2)] // left/right center
    ];
    
    samplePoints.forEach(([x, y]) => {
      const index = (y * width + x) * 4;
      samples.push({
        r: data[index],
        g: data[index + 1],
        b: data[index + 2]
      });
    });
    
    // Find average of samples (simple approach)
    const avg = samples.reduce(
      (acc, color) => ({
        r: acc.r + color.r,
        g: acc.g + color.g,
        b: acc.b + color.b
      }),
      { r: 0, g: 0, b: 0 }
    );
    
    return {
      r: Math.round(avg.r / samples.length),
      g: Math.round(avg.g / samples.length),
      b: Math.round(avg.b / samples.length)
    };
  }

  /**
   * Refine mask using edge detection
   */
  private refineWithEdgeDetection(
    data: Uint8ClampedArray,
    mask: Uint8ClampedArray,
    width: number,
    height: number,
    threshold: number
  ) {
    // Simple Sobel edge detection
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x;
        
        // Calculate gradient
        const gx = this.getGradientX(data, x, y, width);
        const gy = this.getGradientY(data, x, y, width);
        const magnitude = Math.sqrt(gx * gx + gy * gy);
        
        // Strong edges are likely pin boundaries
        if (magnitude > threshold) {
          mask[idx] = 255;
        }
      }
    }
  }

  /**
   * Calculate X gradient (Sobel)
   */
  private getGradientX(data: Uint8ClampedArray, x: number, y: number, width: number): number {
    const getGray = (px: number, py: number) => {
      const idx = (py * width + px) * 4;
      return (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
    };
    
    return (
      -1 * getGray(x - 1, y - 1) + 1 * getGray(x + 1, y - 1) +
      -2 * getGray(x - 1, y) + 2 * getGray(x + 1, y) +
      -1 * getGray(x - 1, y + 1) + 1 * getGray(x + 1, y + 1)
    );
  }

  /**
   * Calculate Y gradient (Sobel)
   */
  private getGradientY(data: Uint8ClampedArray, x: number, y: number, width: number): number {
    const getGray = (px: number, py: number) => {
      const idx = (py * width + px) * 4;
      return (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
    };
    
    return (
      -1 * getGray(x - 1, y - 1) + -2 * getGray(x, y - 1) + -1 * getGray(x + 1, y - 1) +
      1 * getGray(x - 1, y + 1) + 2 * getGray(x, y + 1) + 1 * getGray(x + 1, y + 1)
    );
  }

  /**
   * Fill holes inside the detected pin
   */
  private fillHoles(mask: Uint8ClampedArray, width: number, height: number) {
    // Simple flood fill from edges to mark background
    const visited = new Array(mask.length).fill(false);
    const queue: [number, number][] = [];
    
    // Start from all edge pixels
    for (let x = 0; x < width; x++) {
      queue.push([x, 0]); // top edge
      queue.push([x, height - 1]); // bottom edge
    }
    for (let y = 0; y < height; y++) {
      queue.push([0, y]); // left edge
      queue.push([width - 1, y]); // right edge
    }
    
    // Flood fill background with safety limit to prevent infinite loops
    let iterations = 0;
    const maxIterations = width * height * 2; // Safety limit
    
    while (queue.length > 0 && iterations < maxIterations) {
      iterations++;
      const [x, y] = queue.shift()!;
      const idx = y * width + x;
      
      // Check bounds and if already processed
      if (x < 0 || x >= width || y < 0 || y >= height || visited[idx] || mask[idx] === 255) {
        continue;
      }
      
      visited[idx] = true;
      mask[idx] = 0; // Mark as background
      
      // Add neighbors only if they haven't been visited
      const neighbors = [
        [x + 1, y], [x - 1, y], [x, y + 1], [x, y - 1]
      ];
      
      for (const [nx, ny] of neighbors) {
        const nIdx = ny * width + nx;
        if (nx >= 0 && nx < width && ny >= 0 && ny < height && 
            !visited[nIdx] && mask[nIdx] !== 255) {
          queue.push([nx, ny]);
        }
      }
    }
    
    // Log warning if we hit the safety limit
    if (iterations >= maxIterations) {
      console.warn('⚠️ Flood fill hit iteration limit, processing may be incomplete');
    }
    
    // Everything not marked as background is foreground
    for (let i = 0; i < mask.length; i++) {
      if (!visited[i]) {
        mask[i] = 255;
      }
    }
  }

  /**
   * Apply blur to mask for smoother edges (optimized)
   */
  private blurMask(mask: Uint8ClampedArray, width: number, height: number, radius: number) {
    // Limit radius to prevent excessive processing
    const limitedRadius = Math.min(radius, 5);
    const temp = new Uint8ClampedArray(mask);
    
    // Use separable blur for better performance (horizontal then vertical)
    // Horizontal pass
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        let sum = 0;
        let count = 0;
        
        for (let dx = -limitedRadius; dx <= limitedRadius; dx++) {
          const nx = x + dx;
          if (nx >= 0 && nx < width) {
            sum += temp[y * width + nx];
            count++;
          }
        }
        
        mask[y * width + x] = Math.round(sum / count);
      }
    }
    
    // Vertical pass
    temp.set(mask);
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        let sum = 0;
        let count = 0;
        
        for (let dy = -limitedRadius; dy <= limitedRadius; dy++) {
          const ny = y + dy;
          if (ny >= 0 && ny < height) {
            sum += temp[ny * width + x];
            count++;
          }
        }
        
        mask[y * width + x] = Math.round(sum / count);
      }
    }
  }

  /**
   * Apply mask to remove background
   */
  private applyMask(data: Uint8ClampedArray, mask: Uint8ClampedArray) {
    for (let i = 0; i < mask.length; i++) {
      const alpha = mask[i] / 255;
      data[i * 4 + 3] = Math.round(alpha * 255); // Set alpha channel
    }
  }

  /**
   * Smart crop to focus on the pin
   */
  private smartCrop(
    imageData: ImageData,
    mask: Uint8ClampedArray,
    width: number,
    height: number
  ): ImageData {
    // Find bounding box of the pin
    let minX = width, minY = height, maxX = 0, maxY = 0;
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        if (mask[y * width + x] > 128) {
          minX = Math.min(minX, x);
          minY = Math.min(minY, y);
          maxX = Math.max(maxX, x);
          maxY = Math.max(maxY, y);
        }
      }
    }
    
    // Add some padding
    const padding = 20;
    minX = Math.max(0, minX - padding);
    minY = Math.max(0, minY - padding);
    maxX = Math.min(width - 1, maxX + padding);
    maxY = Math.min(height - 1, maxY + padding);
    
    const cropWidth = maxX - minX + 1;
    const cropHeight = maxY - minY + 1;
    
    // Create cropped image data
    const croppedData = new Uint8ClampedArray(cropWidth * cropHeight * 4);
    
    for (let y = 0; y < cropHeight; y++) {
      for (let x = 0; x < cropWidth; x++) {
        const srcX = minX + x;
        const srcY = minY + y;
        const srcIdx = (srcY * width + srcX) * 4;
        const dstIdx = (y * cropWidth + x) * 4;
        
        croppedData[dstIdx] = imageData.data[srcIdx];
        croppedData[dstIdx + 1] = imageData.data[srcIdx + 1];
        croppedData[dstIdx + 2] = imageData.data[srcIdx + 2];
        croppedData[dstIdx + 3] = imageData.data[srcIdx + 3];
      }
    }
    
    return new ImageData(croppedData, cropWidth, cropHeight);
  }

  /**
   * Clean up resources
   */
  cleanup() {
    // Canvas cleanup is automatic in browser
  }
}

// Create singleton instance
const simpleBgRemovalService = new SimpleBgRemovalService();

export default simpleBgRemovalService; 