import { Timestamp } from 'firebase/firestore';
import { TradingPointCategory } from '@/modules/trading-points/types';

// Collection names constant
export const COLLECTIONS = {
  USERS: 'users',
  TRADING_POINTS: 'tradingPoints',
  CHECK_INS: 'checkIns',
  USER_FAVORITES: 'userFavorites',
  PINS: 'pins',
  BOARDS: 'boards',
  MESSAGES: 'messages',
  NOTIFICATIONS: 'notifications',
  FOLLOWS: 'follows',
} as const;

// Type definitions for database entities

export interface CheckIn {
  id: string;
  userId: string;
  userName: string;
  userAvatarUrl?: string;
  tradingPointId: string;
  tradingPointName: string;
  message?: string;
  photos?: string[];
  rating?: number;
  latitude?: number;
  longitude?: number;
  accuracy?: number;
  checkInAt: Timestamp;
  isVerified: boolean;
  reportedBy?: string[];
}

export interface UserFavorite {
  id: string;
  userId: string;
  tradingPointId: string;
  tradingPointName: string;
  tradingPointCategory: TradingPointCategory;
  notes?: string;
  addedAt: Timestamp;
  visitCount: number;
  lastVisited?: Timestamp;
}

export interface User {
  id: string;
  email: string;
  name: string;
  username: string;
  avatarUrl?: string;
  bio?: string;
  location?: string;
  joinedAt: Timestamp;
  isActive: boolean;
  preferences?: {
    notifications: boolean;
    publicProfile: boolean;
    showLocation: boolean;
  };
  stats?: {
    checkInsCount: number;
    favoritesCount: number;
    pinsCount: number;
  };
}

export interface TradingPoint {
  id: string;
  name: string;
  description?: string;
  category: TradingPointCategory;
  address: string;
  latitude: number;
  longitude: number;
  photos?: string[];
  website?: string;
  phone?: string;
  hours?: {
    [key: string]: string; // day of week -> hours string
  };
  amenities?: string[];
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string;
  stats?: {
    checkInsCount: number;
    favoritesCount: number;
    averageRating: number;
  };
}

export interface Pin {
  id: string;
  name: string;
  description?: string;
  imageUrl?: string;
  category: string;
  rarity?: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  year?: number;
  series?: string;
  manufacturer?: string;
  condition?: 'mint' | 'excellent' | 'good' | 'fair' | 'poor';
  estimatedValue?: number;
  tags?: string[];
  ownerId: string;
  isForTrade: boolean;
  isPublic: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Board {
  id: string;
  name: string;
  description?: string;
  coverImageUrl?: string;
  ownerId: string;
  pinIds: string[];
  isPublic: boolean;
  tags?: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  type: 'text' | 'trade_offer' | 'system';
  isRead: boolean;
  sentAt: Timestamp;
  metadata?: {
    tradeOfferId?: string;
    pinIds?: string[];
  };
}

export interface Notification {
  id: string;
  userId: string;
  type: 'check_in' | 'trade_offer' | 'message' | 'system';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: Timestamp;
  metadata?: {
    relatedId?: string;
    actionUrl?: string;
  };
}

// Type for collection names
export type CollectionName = typeof COLLECTIONS[keyof typeof COLLECTIONS]; 