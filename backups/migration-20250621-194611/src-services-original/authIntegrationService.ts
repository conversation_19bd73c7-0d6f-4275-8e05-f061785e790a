import { User as FirebaseUser } from 'firebase/auth';
import { usersService, AdminUser, UserInput } from './usersService';
import { auth } from './firebase';
import { onAuthStateChanged } from 'firebase/auth';

/**
 * Service to integrate Firebase Auth with our admin user management system
 */
class AuthIntegrationService {
  private static instance: AuthIntegrationService;

  public static getInstance(): AuthIntegrationService {
    if (!AuthIntegrationService.instance) {
      AuthIntegrationService.instance = new AuthIntegrationService();
    }
    return AuthIntegrationService.instance;
  }

  /**
   * Sync Firebase Auth user with our admin user collection
   */
  async syncUserWithFirestore(firebaseUser: FirebaseUser): Promise<AdminUser> {
    try {
      // Check if user already exists in our collection
      let adminUser = await usersService.getByEmail(firebaseUser.email!);

      if (!adminUser) {
        // Create new admin user from Firebase Auth data
        const displayName = firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User';
        const nameParts = displayName.split(' ');
        const userData: UserInput = {
          firstName: nameParts[0] || 'User',
          lastName: nameParts.slice(1).join(' ') || '',
          email: firebaseUser.email!,
          username: displayName.toLowerCase().replace(/\s+/g, '-'),
          avatarUrl: firebaseUser.photoURL || '',
          status: 'active',
          role: 'user', // Default role for new users
          emailVerified: firebaseUser.emailVerified,
          preferences: {
            notificationsEnabled: true,
            publicProfile: true,
            showLocation: false,
            showEmail: false,
            allowMessages: true
          }
        };

        const result = await usersService.createOrUpdate(userData);
        adminUser = result.user;
        console.log('✅ User synced to admin collection:', adminUser, result.created ? '(created)' : '(updated)');
      } else {
        // Update existing user with latest Firebase Auth data
        const displayName = firebaseUser.displayName;
        const nameParts = displayName ? displayName.split(' ') : [];
        const updateData: Partial<UserInput> = {
          firstName: nameParts[0] || adminUser.firstName,
          lastName: nameParts.slice(1).join(' ') || adminUser.lastName,
          avatarUrl: firebaseUser.photoURL || adminUser.avatarUrl,
          emailVerified: firebaseUser.emailVerified
        };

        // Only update if there are actual changes
        const hasChanges = Object.entries(updateData).some(([key, value]) => {
          return adminUser![key as keyof AdminUser] !== value;
        });

        if (hasChanges) {
          adminUser = await usersService.update(adminUser.id, updateData);
          console.log('✅ User data updated from Firebase Auth:', adminUser);
        }

        // Update last login
        await usersService.updateLastLogin(adminUser.id);
      }

      return adminUser;
    } catch (error) {
      console.error('❌ Error syncing user with Firestore:', error);
      throw error;
    }
  }

  /**
   * Get admin user data for a Firebase Auth user
   */
  async getAdminUserForFirebaseUser(firebaseUser: FirebaseUser): Promise<AdminUser | null> {
    if (!firebaseUser.email) return null;
    
    try {
      return await usersService.getByEmail(firebaseUser.email);
    } catch (error) {
      console.error('❌ Error getting admin user:', error);
      return null;
    }
  }

  /**
   * Check if a Firebase Auth user has admin privileges
   */
  async isUserAdmin(firebaseUser: FirebaseUser): Promise<boolean> {
    const adminUser = await this.getAdminUserForFirebaseUser(firebaseUser);
    return adminUser?.role === 'admin' || adminUser?.role === 'moderator';
  }

  /**
   * Check if a Firebase Auth user has specific role
   */
  async hasRole(firebaseUser: FirebaseUser, role: AdminUser['role']): Promise<boolean> {
    const adminUser = await this.getAdminUserForFirebaseUser(firebaseUser);
    return adminUser?.role === role;
  }

  /**
   * Get user permissions based on role
   */
  async getUserPermissions(firebaseUser: FirebaseUser): Promise<{
    canAccessAdmin: boolean;
    canManageUsers: boolean;
    canManageTradingPoints: boolean;
    canModerate: boolean;
    isAmbassador: boolean;
  }> {
    const adminUser = await this.getAdminUserForFirebaseUser(firebaseUser);
    
    if (!adminUser || adminUser.status !== 'active') {
      return {
        canAccessAdmin: false,
        canManageUsers: false,
        canManageTradingPoints: false,
        canModerate: false,
        isAmbassador: false
      };
    }

    const role = adminUser.role;
    
    return {
      canAccessAdmin: ['admin', 'moderator'].includes(role),
      canManageUsers: role === 'admin',
      canManageTradingPoints: ['admin', 'moderator'].includes(role),
      canModerate: ['admin', 'moderator'].includes(role),
      isAmbassador: role === 'ambassador'
    };
  }

  /**
   * Setup automatic sync when user signs in/out
   */
  setupAutoSync(): () => void {
    return onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          await this.syncUserWithFirestore(firebaseUser);
        } catch (error) {
          console.error('❌ Auto-sync failed:', error);
        }
      }
    });
  }

  /**
   * Bulk import existing Firebase Auth users to admin collection
   * Note: This would require Firebase Admin SDK in a server environment
   */
  async bulkImportFirebaseUsers(): Promise<void> {
    console.warn('⚠️ Bulk import requires Firebase Admin SDK and should be run server-side');
    // This would be implemented server-side with Firebase Admin SDK
    // For now, users will be synced as they log in
  }

  /**
   * Get all users from both Firebase Auth and our admin collection
   * Returns merged data with admin info when available
   */
  async getAllUsersWithAuthData(): Promise<AdminUser[]> {
    // For now, return only users from our admin collection
    // In a full implementation, this would merge Firebase Auth users
    return await usersService.getAll();
  }

  /**
   * Delete user from both Firebase Auth and admin collection
   * Note: Deleting from Firebase Auth requires Admin SDK
   */
  async deleteUserCompletely(userId: string): Promise<void> {
    try {
      // Delete from our admin collection
      await usersService.delete(userId);
      
      // Note: Deleting from Firebase Auth requires Admin SDK server-side
      console.warn('⚠️ User deleted from admin collection. Firebase Auth deletion requires server-side implementation.');
    } catch (error) {
      console.error('❌ Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Update user role and sync with any external systems
   */
  async updateUserRole(userId: string, newRole: AdminUser['role'], moderatedBy: string): Promise<AdminUser> {
    try {
      const updatedUser = await usersService.update(userId, { role: newRole }, moderatedBy);
      
      // Here you could add additional logic like:
      // - Sending notification emails
      // - Updating external systems
      // - Logging role changes
      
      console.log(`✅ User role updated: ${updatedUser.email} -> ${newRole}`);
      return updatedUser;
    } catch (error) {
      console.error('❌ Error updating user role:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const authIntegrationService = AuthIntegrationService.getInstance();
export default authIntegrationService; 