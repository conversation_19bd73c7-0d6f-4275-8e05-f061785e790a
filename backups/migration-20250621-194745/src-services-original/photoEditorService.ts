import { UIEvent, PhotoEditorSDKUI } from 'photoeditorsdk';

export interface PhotoEditorConfig {
  container: string | HTMLDivElement;
  image: string;
  license?: string;
  assets?: {
    baseUrl: string;
  };
}

export interface PhotoEditorResult {
  imageSrc: string;
  blob: Blob;
}

export class PhotoEditorService {
  private static instance: PhotoEditorService;
  private editor: any = null;

  private constructor() {}

  public static getInstance(): PhotoEditorService {
    if (!PhotoEditorService.instance) {
      PhotoEditorService.instance = new PhotoEditorService();
    }
    return PhotoEditorService.instance;
  }

  /**
   * Inicializa o PhotoEditor SDK
   */
  public async initEditor(config: PhotoEditorConfig): Promise<any> {
    try {
      console.log('🎨 Initializing PhotoEditor SDK...');
      
      const editorConfig = {
        container: config.container,
        image: config.image,
        license: config.license || '', // Usar licença vazia para trial
        assets: {
          baseUrl: config.assets?.baseUrl || '/assets',
        },
        // Configurações de UI
        ui: {
          elements: {
            panels: {
              settings: true
            },
            navigation: {
              action: {
                export: {
                  show: true,
                  format: ['image/jpeg', 'image/png']
                }
              }
            }
          }
        }
      };

      this.editor = await PhotoEditorSDKUI.init(editorConfig);
      console.log('✅ PhotoEditor SDK initialized successfully');
      
      return this.editor;
    } catch (error) {
      console.error('❌ Failed to initialize PhotoEditor SDK:', error);
      throw error;
    }
  }

  /**
   * Abre o editor com uma imagem
   */
  public async openEditor(
    imageUrl: string, 
    containerId: string = '#photo-editor'
  ): Promise<PhotoEditorResult> {
    return new Promise(async (resolve, reject) => {
      try {
        const editor = await this.initEditor({
          container: containerId,
          image: imageUrl,
          assets: {
            baseUrl: '/assets'
          }
        });

        // Listener para quando o usuário exporta a imagem
        editor.on(UIEvent.EXPORT, async (imageSrc: string) => {
          try {
            console.log('📸 Image exported:', imageSrc);
            
            // Converter para blob
            const response = await fetch(imageSrc);
            const blob = await response.blob();
            
            resolve({
              imageSrc,
              blob
            });
          } catch (error) {
            console.error('❌ Error processing exported image:', error);
            reject(error);
          }
        });

        // Listener para quando o usuário cancela
        editor.on(UIEvent.CLOSE, () => {
          console.log('🚪 Editor closed');
          reject(new Error('Editor closed by user'));
        });

      } catch (error) {
        console.error('❌ Error opening PhotoEditor:', error);
        reject(error);
      }
    });
  }

  /**
   * Fecha o editor atual
   */
  public closeEditor(): void {
    if (this.editor) {
      try {
        this.editor.dispose();
        this.editor = null;
        console.log('🗑️ PhotoEditor disposed');
      } catch (error) {
        console.error('❌ Error disposing PhotoEditor:', error);
      }
    }
  }

  /**
   * Verifica se o PhotoEditor SDK está disponível
   */
  public static isAvailable(): boolean {
    try {
      return typeof PhotoEditorSDKUI !== 'undefined';
    } catch {
      return false;
    }
  }
}

export const photoEditorService = PhotoEditorService.getInstance(); 