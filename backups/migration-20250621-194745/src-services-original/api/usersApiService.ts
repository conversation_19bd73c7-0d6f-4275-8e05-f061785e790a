import { User } from '@/config/firebase';

interface UserCreateData {
  id: string;
  email: string;
  username: string;
  displayName: string;
  firstName?: string;
  lastName?: string;
  bio?: string;
  location?: string;
  avatarUrl?: string;
}

interface UserSettings {
  notificationsEnabled?: boolean;
  publicProfile?: boolean;
  showLocation?: boolean;
  showEmail?: boolean;
  allowMessages?: boolean;
}

class UsersApiService {
  private baseUrl = 'http://localhost:3001/api/users';

  async getCurrentUser(uid: string): Promise<User | null> {
    const response = await fetch(`${this.baseUrl}/${uid}`);
    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch user: ${response.statusText}`);
    }
    return response.json();
  }

  async upsertUser(userData: UserCreateData): Promise<User> {
    const response = await fetch(`${this.baseUrl}/upsert`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to upsert user: ${response.statusText}`);
    }
    return response.json();
  }

  async isUsernameAvailable(username: string, excludeUserId?: string): Promise<boolean> {
    const params = new URLSearchParams({ username });
    if (excludeUserId) {
      params.append('excludeUserId', excludeUserId);
    }
    
    const response = await fetch(`${this.baseUrl}/username-available?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to check username availability: ${response.statusText}`);
    }
    
    const result = await response.json();
    return result.available;
  }

  async updateUserSettings(userId: string, settings: UserSettings): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${userId}/settings`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(settings),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to update user settings: ${response.statusText}`);
    }
  }
}

export const usersApiService = new UsersApiService();
export type { UserCreateData, UserSettings }; 