import { Board, BoardInput } from '../boardsService';

class BoardsApiService {
  private baseUrl = 'http://localhost:3001/api/boards';

  async getUserBoards(userId: string, includePrivate: boolean = false): Promise<Board[]> {
    const response = await fetch(`${this.baseUrl}/user/${userId}?includePrivate=${includePrivate}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch user boards: ${response.statusText}`);
    }
    return response.json();
  }

  async getById(boardId: string): Promise<Board | null> {
    const response = await fetch(`${this.baseUrl}/${boardId}`);
    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch board: ${response.statusText}`);
    }
    return response.json();
  }

  async create(data: BoardInput): Promise<Board> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to create board: ${error}`);
    }
    
    return response.json();
  }

  async update(boardId: string, data: Partial<BoardInput>): Promise<Board> {
    const response = await fetch(`${this.baseUrl}/${boardId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to update board: ${error}`);
    }
    
    return response.json();
  }

  async delete(boardId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${boardId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to delete board: ${error}`);
    }
  }

  async updatePinCount(boardId: string, pinCount: number): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${boardId}/pin-count`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ pinCount }),
    });
    
    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to update pin count: ${error}`);
    }
  }
}

export const boardsApiService = new BoardsApiService(); 