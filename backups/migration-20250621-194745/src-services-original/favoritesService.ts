import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './firebase';
import { UserFavorite, COLLECTIONS } from '@/services/database/schema';
import { TradingPointCategory } from '@/modules/trading-points/types';

class FavoritesService {
  private collection = collection(db, COLLECTIONS.USER_FAVORITES);

  /**
   * Add a trading point to user's favorites
   */
  async addFavorite(data: {
    userId: string;
    tradingPointId: string;
    tradingPointName: string;
    tradingPointCategory: TradingPointCategory;
    notes?: string;
  }): Promise<UserFavorite> {
    try {
      // Check if already favorited
      const existing = await this.getFavorite(data.userId, data.tradingPointId);
      if (existing) {
        throw new Error('Trading point is already in favorites');
      }

      const now = Timestamp.now();
      
      const favoriteData: Omit<UserFavorite, 'id'> = {
        ...data,
        addedAt: now,
        visitCount: 0,
      };

      // Remove undefined fields before sending to Firestore
      const cleanedData = this.removeUndefinedFields(favoriteData);
      const docRef = await addDoc(this.collection, cleanedData);
      
      return {
        id: docRef.id,
        ...favoriteData
      };
    } catch (error) {
      console.error('Error adding favorite:', error);
      throw new Error('Failed to add favorite');
    }
  }

  /**
   * Remove a trading point from user's favorites
   */
  async removeFavorite(userId: string, tradingPointId: string): Promise<void> {
    try {
      const q = query(
        this.collection,
        where('userId', '==', userId),
        where('tradingPointId', '==', tradingPointId)
      );

      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        throw new Error('Favorite not found');
      }

      const batch = writeBatch(db);
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error removing favorite:', error);
      throw new Error('Failed to remove favorite');
    }
  }

  /**
   * Get a specific favorite
   */
  async getFavorite(userId: string, tradingPointId: string): Promise<UserFavorite | null> {
    try {
      const q = query(
        this.collection,
        where('userId', '==', userId),
        where('tradingPointId', '==', tradingPointId)
      );

      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        return null;
      }

      const doc = snapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      } as UserFavorite;
    } catch (error) {
      console.error('Error getting favorite:', error);
      throw new Error('Failed to get favorite');
    }
  }

  /**
   * Get all favorites for a user
   */
  async getUserFavorites(userId: string): Promise<UserFavorite[]> {
    try {
      const q = query(
        this.collection,
        where('userId', '==', userId),
        orderBy('addedAt', 'desc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as UserFavorite));
    } catch (error) {
      console.error('Error fetching user favorites:', error);
      throw new Error('Failed to fetch user favorites');
    }
  }

  /**
   * Get favorites for a trading point (who favorited it)
   */
  async getTradingPointFavorites(tradingPointId: string): Promise<UserFavorite[]> {
    try {
      const q = query(
        this.collection,
        where('tradingPointId', '==', tradingPointId),
        orderBy('addedAt', 'desc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as UserFavorite));
    } catch (error) {
      console.error('Error fetching trading point favorites:', error);
      throw new Error('Failed to fetch trading point favorites');
    }
  }

  /**
   * Update favorite (notes, visit count, etc.)
   */
  async updateFavorite(id: string, updates: Partial<UserFavorite>): Promise<void> {
    try {
      const docRef = doc(this.collection, id);
      await updateDoc(docRef, updates);
    } catch (error) {
      console.error('Error updating favorite:', error);
      throw new Error('Failed to update favorite');
    }
  }

  /**
   * Increment visit count for a favorite
   */
  async incrementVisitCount(userId: string, tradingPointId: string): Promise<void> {
    try {
      const favorite = await this.getFavorite(userId, tradingPointId);
      
      if (favorite) {
        await this.updateFavorite(favorite.id, {
          visitCount: favorite.visitCount + 1,
          lastVisited: Timestamp.now()
        });
      }
    } catch (error) {
      console.error('Error incrementing visit count:', error);
      throw new Error('Failed to increment visit count');
    }
  }

  /**
   * Check if a trading point is favorited by user
   */
  async isFavorited(userId: string, tradingPointId: string): Promise<boolean> {
    try {
      const favorite = await this.getFavorite(userId, tradingPointId);
      return favorite !== null;
    } catch (error) {
      console.error('Error checking if favorited:', error);
      return false;
    }
  }

  /**
   * Get user's favorite statistics
   */
  async getUserFavoriteStats(userId: string): Promise<{
    totalFavorites: number;
    favoritesByCategory: Record<TradingPointCategory, number>;
    mostVisited: UserFavorite | null;
    recentlyAdded: UserFavorite[];
  }> {
    try {
      const favorites = await this.getUserFavorites(userId);
      
      const favoritesByCategory = favorites.reduce((acc, favorite) => {
        acc[favorite.tradingPointCategory] = (acc[favorite.tradingPointCategory] || 0) + 1;
        return acc;
      }, {} as Record<TradingPointCategory, number>);

      const mostVisited = favorites.reduce((max, favorite) => 
        favorite.visitCount > (max?.visitCount || 0) ? favorite : max, 
        null as UserFavorite | null
      );

      const recentlyAdded = favorites.slice(0, 5); // Already sorted by addedAt desc

      return {
        totalFavorites: favorites.length,
        favoritesByCategory,
        mostVisited,
        recentlyAdded
      };
    } catch (error) {
      console.error('Error getting user favorite stats:', error);
      throw new Error('Failed to get user favorite stats');
    }
  }

  /**
   * Get popular trading points (most favorited)
   */
  async getPopularTradingPoints(limitCount: number = 10): Promise<Array<{
    tradingPointId: string;
    tradingPointName: string;
    category: TradingPointCategory;
    favoriteCount: number;
  }>> {
    try {
      const snapshot = await getDocs(this.collection);
      const favorites = snapshot.docs.map(doc => doc.data() as UserFavorite);
      
      // Group by trading point and count
      const tradingPointCounts = favorites.reduce((acc, favorite) => {
        const key = favorite.tradingPointId;
        if (!acc[key]) {
          acc[key] = {
            tradingPointId: favorite.tradingPointId,
            tradingPointName: favorite.tradingPointName,
            category: favorite.tradingPointCategory,
            favoriteCount: 0
          };
        }
        acc[key].favoriteCount++;
        return acc;
      }, {} as Record<string, {
        tradingPointId: string;
        tradingPointName: string;
        category: TradingPointCategory;
        favoriteCount: number;
      }>);

      // Sort by count and return top results
      return Object.values(tradingPointCounts)
        .sort((a, b) => b.favoriteCount - a.favoriteCount)
        .slice(0, limitCount);
    } catch (error) {
      console.error('Error getting popular trading points:', error);
      throw new Error('Failed to get popular trading points');
    }
  }

  /**
   * Remove undefined fields from object (Firestore doesn't accept undefined values)
   */
  private removeUndefinedFields(obj: any): any {
    const cleaned: any = {};
    Object.keys(obj).forEach(key => {
      if (obj[key] !== undefined) {
        cleaned[key] = obj[key];
      }
    });
    return cleaned;
  }

  /**
   * Bulk create favorites (for data population)
   */
  async bulkCreateFavorites(favorites: Omit<UserFavorite, 'id'>[]): Promise<void> {
    try {
      const batch = writeBatch(db);
      
      favorites.forEach(favoriteData => {
        const docRef = doc(this.collection);
        // Remove undefined fields before sending to Firestore
        const cleanedData = this.removeUndefinedFields(favoriteData);
        batch.set(docRef, cleanedData);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error bulk creating favorites:', error);
      throw new Error('Failed to bulk create favorites');
    }
  }

  /**
   * Clear all favorites for a user
   */
  async clearUserFavorites(userId: string): Promise<void> {
    try {
      const q = query(this.collection, where('userId', '==', userId));
      const snapshot = await getDocs(q);
      
      const batch = writeBatch(db);
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error clearing user favorites:', error);
      throw new Error('Failed to clear user favorites');
    }
  }
}

export const favoritesService = new FavoritesService(); 