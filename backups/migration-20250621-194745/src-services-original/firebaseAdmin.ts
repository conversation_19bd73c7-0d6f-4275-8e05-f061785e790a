import { initializeApp, cert, getApps, App } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore } from 'firebase-admin/firestore';

// Configuração do Firebase Admin
const firebaseAdminConfig = {
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || 'iconpal-cf925',
  // Para produção, use variáveis de ambiente ou arquivo de credenciais
  // credential: cert(require('./path/to/serviceAccountKey.json'))
};

let adminApp: App;
let adminAuth: Auth;
let adminFirestore: Firestore;

// Inicializar Firebase Admin (apenas uma vez)
function initializeFirebaseAdmin() {
  if (getApps().length === 0) {
    try {
      // Em desenvolvimento, pode usar credenciais padrão
      // Em produção, configure as credenciais adequadamente
      adminApp = initializeApp(firebaseAdminConfig);
      adminAuth = getAuth(adminApp);
      adminFirestore = getFirestore(adminApp);
      
      console.log('✅ Firebase Admin SDK initialized');
    } catch (error) {
      console.error('❌ Error initializing Firebase Admin SDK:', error);
      throw error;
    }
  } else {
    adminApp = getApps()[0];
    adminAuth = getAuth(adminApp);
    adminFirestore = getFirestore(adminApp);
  }
}

// Serviços administrativos avançados
export class FirebaseAdminService {
  constructor() {
    initializeFirebaseAdmin();
  }

  // Deletar usuário do Firebase Auth (com privilégios admin)
  async deleteUserFromAuth(uid: string): Promise<{ success: boolean; message: string }> {
    try {
      await adminAuth.deleteUser(uid);
      console.log('✅ User deleted from Firebase Auth via Admin SDK:', uid);
      return {
        success: true,
        message: 'User deleted from Firebase Auth successfully'
      };
    } catch (error: any) {
      console.error('❌ Error deleting user from Firebase Auth:', error);
      return {
        success: false,
        message: `Failed to delete from Firebase Auth: ${error.message}`
      };
    }
  }

  // Obter usuário do Firebase Auth
  async getUserFromAuth(uid: string) {
    try {
      const userRecord = await adminAuth.getUser(uid);
      return userRecord;
    } catch (error: any) {
      console.error('❌ Error getting user from Firebase Auth:', error);
      throw error;
    }
  }

  // Listar todos os usuários do Firebase Auth
  async listAllUsers(maxResults: number = 1000) {
    try {
      const listUsersResult = await adminAuth.listUsers(maxResults);
      return listUsersResult.users;
    } catch (error: any) {
      console.error('❌ Error listing users from Firebase Auth:', error);
      throw error;
    }
  }

  // Atualizar usuário no Firebase Auth
  async updateUserInAuth(uid: string, properties: any) {
    try {
      const userRecord = await adminAuth.updateUser(uid, properties);
      console.log('✅ User updated in Firebase Auth:', uid);
      return userRecord;
    } catch (error: any) {
      console.error('❌ Error updating user in Firebase Auth:', error);
      throw error;
    }
  }

  // Criar usuário no Firebase Auth (com privilégios admin)
  async createUserInAuth(userData: {
    email: string;
    password?: string;
    displayName?: string;
    emailVerified?: boolean;
    disabled?: boolean;
  }) {
    try {
      const userRecord = await adminAuth.createUser(userData);
      console.log('✅ User created in Firebase Auth via Admin SDK:', userRecord.uid);
      return userRecord;
    } catch (error: any) {
      console.error('❌ Error creating user in Firebase Auth:', error);
      throw error;
    }
  }

  // Verificar se email existe no Firebase Auth
  async checkEmailExists(email: string): Promise<boolean> {
    try {
      await adminAuth.getUserByEmail(email);
      return true;
    } catch (error: any) {
      if (error.code === 'auth/user-not-found') {
        return false;
      }
      throw error;
    }
  }

  // Sincronizar usuários entre Auth e Firestore
  async syncAuthWithFirestore() {
    try {
      const authUsers = await this.listAllUsers();
      const firestoreUsers = await adminFirestore.collection('users').get();
      
      const authUIDs = new Set(authUsers.map(user => user.uid));
      const firestoreUIDs = new Set(firestoreUsers.docs.map(doc => doc.id));
      
      // Usuários órfãos no Auth (não estão no Firestore)
      const orphanedInAuth = authUsers.filter(user => !firestoreUIDs.has(user.uid));
      
      // Usuários órfãos no Firestore (não estão no Auth)
      const orphanedInFirestore = firestoreUsers.docs.filter(doc => !authUIDs.has(doc.id));
      
      return {
        authUsers: authUsers.length,
        firestoreUsers: firestoreUsers.size,
        orphanedInAuth: orphanedInAuth.length,
        orphanedInFirestore: orphanedInFirestore.length,
        orphanedAuthUsers: orphanedInAuth,
        orphanedFirestoreUsers: orphanedInFirestore
      };
    } catch (error: any) {
      console.error('❌ Error syncing Auth with Firestore:', error);
      throw error;
    }
  }

  // Limpar usuários órfãos
  async cleanupOrphanedUsers(deleteFromAuth: boolean = false, deleteFromFirestore: boolean = false) {
    try {
      const syncResult = await this.syncAuthWithFirestore();
      const results = {
        deletedFromAuth: 0,
        deletedFromFirestore: 0,
        errors: [] as string[]
      };

      // Deletar órfãos do Auth
      if (deleteFromAuth && syncResult.orphanedAuthUsers) {
        for (const user of syncResult.orphanedAuthUsers) {
          try {
            await this.deleteUserFromAuth(user.uid);
            results.deletedFromAuth++;
          } catch (error: any) {
            results.errors.push(`Failed to delete ${user.uid} from Auth: ${error.message}`);
          }
        }
      }

      // Deletar órfãos do Firestore
      if (deleteFromFirestore && syncResult.orphanedFirestoreUsers) {
        for (const doc of syncResult.orphanedFirestoreUsers) {
          try {
            await doc.ref.delete();
            results.deletedFromFirestore++;
          } catch (error: any) {
            results.errors.push(`Failed to delete ${doc.id} from Firestore: ${error.message}`);
          }
        }
      }

      return results;
    } catch (error: any) {
      console.error('❌ Error cleaning up orphaned users:', error);
      throw error;
    }
  }
}

// Instância singleton
export const firebaseAdminService = new FirebaseAdminService(); 