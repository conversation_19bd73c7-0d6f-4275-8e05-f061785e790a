import { Pin } from '@/types/pin';
import { useAuthStore } from '@/store/authStore';

interface GetExplorePinsParams {
  page?: number;
  limit?: number;
  sortBy?: 'trending' | 'newest';
}

/**
 * Basic Explore service – fetches public pins from the REST API and applies a simple in-memory
 * scoring algorithm when `sortBy === "trending"`.
 *
 * NOTE: This is intentionally lightweight so we can iterate quickly. Once the backend exposes a
 * dedicated /api/explore endpoint we can swap the implementation here without touching the
 * UI layer.
 */
export const exploreService = {
  async getPins({ page = 0, limit = 30, sortBy = 'trending' }: GetExplorePinsParams = {}): Promise<Pin[]> {
    const offset = page * limit;

    console.log('🔄 exploreService.getPins called with:', { page, limit, sortBy, offset });

    // Get current user for personalized like/save states
    const { user } = useAuthStore.getState();
    const userId = user?.id;

    // Fetch raw pins from the existing pins REST endpoint (public only)
    const rsp = await fetch(
      `http://localhost:3001/api/pins?limit=${limit}&offset=${offset}${userId ? `&userId=${userId}` : ''}`,
      {
        // Credentials: same-origin in case the API is proxied through Vite dev-server
        credentials: 'same-origin',
      }
    );

    console.log('📡 API response status:', rsp.status);

    if (!rsp.ok) {
      console.error('❌ API request failed:', rsp.status, rsp.statusText);
      throw new Error(`Failed to load pins, status ${rsp.status}`);
    }

    const rawPins: any[] = await rsp.json();
    console.log('📦 Raw pins received:', rawPins.length, 'pins');
    console.log('🔍 First pin sample:', rawPins[0]);

    // Normalise a bit so the UI has what it needs
    const normalised: Pin[] = rawPins.map((p) => ({
      id: p.id || p._id,
      name: p.name || p.title || 'Untitled Pin',
      image: p.image || p.imageUrl || '',
      description: p.description || '',
      rarity: (p.rarity ?? 'common') as Pin['rarity'],
      year: p.year || p.releaseYear,
      series: p.series || p.origin,
      likes: p.likes || p.likesCount || 0,
      comments: p.comments || p.commentsCount || 0,
      isLiked: Boolean(p.isLiked),
      isSaved: Boolean(p.isSaved),
      owner: p.owner || {
        id: p.userId || p.user?.id,
        name: p.user?.displayName || p.user?.username || 'Unknown',
        username: p.user?.username,
        avatar: p.user?.avatarUrl,
      },
      createdAt: p.createdAt ? new Date(p.createdAt) : undefined,
    }));

    console.log('✅ Normalised pins:', normalised.length, 'pins');
    console.log('🔍 First normalised pin:', normalised[0]);
    console.log('❤️ Like/Save states loaded for user:', userId);

    if (sortBy === 'newest') {
      return normalised.sort((a, b) => (b.createdAt?.getTime() || 0) - (a.createdAt?.getTime() || 0));
    }

    // Trending score: popularity adjusted by time decay (last 48h has big weight)
    const now = Date.now();
    const decayMs = 1000 * 60 * 60 * 48; // 48h
    const scored = normalised.map((pin) => {
      const interactions = (pin.likes || 0) + (pin.comments || 0) * 3;
      const ageMs = pin.createdAt ? now - pin.createdAt.getTime() : decayMs;
      const freshnessFactor = Math.exp(-ageMs / decayMs); // 1 -> 0
      const score = interactions * 0.7 + interactions * freshnessFactor * 0.3;
      return { pin, score };
    });

    const result = scored
      .sort((a, b) => b.score - a.score)
      .map((s) => s.pin);

    console.log('🏆 Final trending result:', result.length, 'pins');
    
    return result;
  },
}; 