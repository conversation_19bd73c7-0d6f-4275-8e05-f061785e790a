export interface PollingMessage {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  timestamp: string;
  type: 'text' | 'image' | 'file';
  attachmentUrl?: string;
  isRead: boolean;
}

export class MessagingPollingService {
  private pollingInterval: number | null = null;
  private isPolling: boolean = false;
  private lastMessageTimestamp: string | null = null;
  private listeners: Map<string, ((data: any) => void)[]> = new Map();
  private userId: string | null = null;

  constructor() {
    this.setupEventListeners();
  }

  private setupEventListeners() {
    this.listeners.set('new_message', []);
    this.listeners.set('message_read', []);
    this.listeners.set('typing', []);
  }

  async startPolling(userId: string): Promise<void> {
    if (!userId || userId === 'test-user') {
      console.warn('⚠️ Invalid user ID for polling:', userId);
      return;
    }

    if (this.isPolling && this.userId === userId) {
      console.log('🔄 Polling already active for user:', userId);
      return;
    }

    this.userId = userId;
    this.isPolling = true;
    this.lastMessageTimestamp = new Date().toISOString();

    console.log('🔄 Starting message polling for user:', userId);

    // Poll every 3 seconds
    this.pollingInterval = setInterval(async () => {
      await this.pollForNewMessages();
    }, 3000);

    // Initial poll - with slight delay to let the page load
    setTimeout(async () => {
      await this.pollForNewMessages();
    }, 1000);
  }

  stopPolling() {
    console.log('🔄 Stopping message polling');
    
    this.isPolling = false;
    this.userId = null;
    
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
  }

  private async pollForNewMessages() {
    if (!this.userId || !this.isPolling) return;

    try {
      // Get conversations for the user
      const response = await fetch(`http://localhost:3001/api/messages/conversations/user/${this.userId}`);
      
      if (!response.ok) {
        console.error('❌ Failed to poll conversations:', response.statusText, response.status);
        return;
      }

      const data = await response.json();
      console.log('🔄 Polling response:', data);
      
      // Handle both direct array and wrapped object responses
      const conversations = Array.isArray(data) ? data : (Array.isArray(data.conversations) ? data.conversations : []);
      console.log('🔄 Conversations found:', conversations.length);
      
      // Check each conversation for new messages
      for (const conversation of conversations) {
        if (conversation.lastMessage && 
            conversation.lastMessage.senderId !== this.userId &&
            conversation.lastMessage.timestamp > (this.lastMessageTimestamp || '')) {
          
          console.log('📨 New message detected:', conversation.lastMessage);
          
          // New message found
          this.emit('new_message', {
            message: conversation.lastMessage,
            conversationId: conversation.id
          });
          
          // Update timestamp
          this.lastMessageTimestamp = conversation.lastMessage.timestamp;
        }
      }
    } catch (error) {
      console.error('❌ Error polling for messages:', error);
      console.error('❌ Polling URL:', `http://localhost:3001/api/messages/conversations/user/${this.userId}`);
    }
  }

  // Event listener methods
  on(event: string, callback: (data: any) => void) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: (data: any) => void) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(callback);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ Error in polling event listener for ${event}:`, error);
        }
      });
    }
  }

  isActive(): boolean {
    return this.isPolling;
  }

  // Simulate typing status (not real-time, but provides feedback)
  sendTypingStatus(conversationId: string, isTyping: boolean) {
    // In polling mode, we can't send real-time typing status
    // This is a limitation of the fallback approach
    console.log('⚠️ Typing status not supported in polling mode');
  }
}

// Export singleton instance
export const messagingPollingService = new MessagingPollingService(); 