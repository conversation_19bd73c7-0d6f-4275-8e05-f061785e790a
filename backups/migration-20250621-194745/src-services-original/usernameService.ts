import { collection, query, where, getDocs, doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/services/firebase';
import { UsernameAvailability } from '@/types/user';

// Usernames reservados que não podem ser usados
const RESERVED_USERNAMES = [
  'admin', 'administrator', 'root', 'api', 'www', 'mail', 'ftp',
  'support', 'help', 'info', 'contact', 'about', 'terms', 'privacy',
  'settings', 'profile', 'user', 'users', 'account', 'accounts',
  'login', 'signup', 'register', 'auth', 'authentication',
  'explore', 'discover', 'trending', 'popular', 'search',
  'home', 'feed', 'notifications', 'messages', 'chat',
  'trade', 'trading', 'marketplace', 'shop', 'store',
  'pins', 'pin', 'boards', 'board', 'collections', 'collection',
  'pinpal', 'disney', 'marvel', 'pixar', 'starwars'
];

export class UsernameService {
  /**
   * Valida se um username é válido
   */
  static validateUsername(username: string): { valid: boolean; error?: string } {
    // Remove espaços e converte para lowercase
    const cleanUsername = username.trim().toLowerCase();
    
    // Verifica comprimento
    if (cleanUsername.length < 3) {
      return { valid: false, error: 'Username must be at least 3 characters long' };
    }
    
    if (cleanUsername.length > 30) {
      return { valid: false, error: 'Username must be less than 30 characters' };
    }
    
    // Verifica caracteres válidos (apenas letras minúsculas, números e underscore)
    const validPattern = /^[a-z0-9_]+$/;
    if (!validPattern.test(cleanUsername)) {
      return { valid: false, error: 'Username can only contain lowercase letters, numbers, and underscores' };
    }
    
    // Não pode terminar com underscore
    if (cleanUsername.endsWith('_')) {
      return { valid: false, error: 'Username cannot end with underscore' };
    }
    
    // Verifica se não é um username reservado
    if (RESERVED_USERNAMES.includes(cleanUsername)) {
      return { valid: false, error: 'This username is reserved and cannot be used' };
    }
    
    return { valid: true };
  }

  /**
   * Verifica se um username está disponível
   */
  static async checkAvailability(username: string): Promise<UsernameAvailability> {
    const validation = this.validateUsername(username);
    
    if (!validation.valid) {
      return {
        available: false,
        reason: 'invalid',
        suggestions: this.generateSuggestions(username)
      };
    }
    
    const cleanUsername = username.trim().toLowerCase();
    
    try {
      console.log('🔍 [1/2] Checking username availability in Firestore:', cleanUsername);
      
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('username', '==', cleanUsername));
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        console.log('❌ Username already taken in Firestore:', cleanUsername);
        return {
          available: false,
          reason: 'taken',
          suggestions: this.generateSuggestions(username)
        };
      }
      
      console.log('✅ Username available in Firestore, checking PostgreSQL...', cleanUsername);
      
      // DOUBLE CHECK: Verificação adicional no PostgreSQL via API
      try {
        console.log('🔍 [2/2] Double-checking username availability in PostgreSQL:', cleanUsername);
        
        const response = await fetch('http://localhost:3001/api/admin/users/check-username', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username: cleanUsername }),
        });
        
        if (response.ok) {
          const result = await response.json();
          console.log('📊 PostgreSQL availability result:', result);
          
          if (!result.available) {
            console.log('❌ Username already taken in PostgreSQL (double check):', cleanUsername);
            return {
              available: false,
              reason: 'taken',
              suggestions: this.generateSuggestions(username)
            };
          }
        } else {
          console.log('⚠️ PostgreSQL double-check failed, using Firestore result only');
        }
      } catch (apiError) {
        console.log('⚠️ PostgreSQL API check failed:', apiError, '- using Firestore result only');
      }
      
      console.log('✅ Username available in both Firestore and PostgreSQL:', cleanUsername);
      return { available: true };
    } catch (error) {
      console.error('❌ Error checking username availability:', error);
      
      // Return unavailable by default when there's an error
      return {
        available: false,
        reason: 'taken',
        suggestions: this.generateSuggestions(username)
      };
    }
  }

  /**
   * Busca usuário por username
   */
  static async getUserByUsername(username: string) {
    const cleanUsername = username.trim().toLowerCase();
    
    try {
      console.log('🔍 Searching user by username in PostgreSQL:', cleanUsername);
      
      // Buscar via API do PostgreSQL (proxy do Vite redireciona para porta 3001)
      const response = await fetch(`http://localhost:3001/api/users/by-username/${encodeURIComponent(cleanUsername)}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          console.log('❌ User not found by username in PostgreSQL:', cleanUsername);
          return null;
        } else {
          throw new Error(`API error: ${response.status}`);
        }
      }
      
      const userData = await response.json();
      console.log('✅ User found by username in PostgreSQL:', userData);
      return userData;
    } catch (error) {
      console.error('❌ Error getting user by username from PostgreSQL:', error);
      return null;
    }
  }

  /**
   * Busca todos os usernames que começam com um prefixo específico
   */
  static async findUsernamesWithPrefix(prefix: string): Promise<string[]> {
    const cleanPrefix = prefix.trim().toLowerCase();
    
    try {
      console.log('🔍 Searching usernames with prefix in Firestore:', cleanPrefix);
      
      const usersRef = collection(db, 'users');
      // Busca usernames que começam com o prefixo
      const q = query(
        usersRef, 
        where('username', '>=', cleanPrefix),
        where('username', '<', cleanPrefix + '\uf8ff')
      );
      const querySnapshot = await getDocs(q);
      
      const usernames: string[] = [];
      querySnapshot.forEach((doc) => {
        const userData = doc.data();
        if (userData.username && userData.username.startsWith(cleanPrefix)) {
          usernames.push(userData.username);
        }
      });
      
      console.log('✅ Found usernames with prefix:', usernames);
      return usernames;
    } catch (error) {
      console.error('❌ Error finding usernames with prefix:', error);
      return [];
    }
  }

  /**
   * Gera sugestões de username baseadas no nome fornecido
   */
  static generateSuggestions(baseUsername: string): string[] {
    const cleanBase = baseUsername.trim().toLowerCase().replace(/[^a-z0-9]/g, '');
    const suggestions: string[] = [];
    
    // Adiciona números
    for (let i = 1; i <= 99; i++) {
      suggestions.push(`${cleanBase}${i}`);
      if (suggestions.length >= 5) break;
    }
    
    // Adiciona variações com underscore
    suggestions.push(`${cleanBase}_official`);
    suggestions.push(`${cleanBase}_collector`);
    suggestions.push(`${cleanBase}_pins`);
    
    // Adiciona ano atual
    const currentYear = new Date().getFullYear();
    suggestions.push(`${cleanBase}${currentYear}`);
    
    return suggestions.slice(0, 10); // Retorna no máximo 10 sugestões
  }

  /**
   * Gera um username baseado no nome do usuário
   */
  static async generateUsernameFromName(name: string): Promise<string> {
    // Remove caracteres especiais e espaços, converte para lowercase
    let baseUsername = name
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '')
      .substring(0, 20); // Limita a 20 caracteres
    
    // Se ficou muito curto, adiciona sufixo
    if (baseUsername.length < 3) {
      baseUsername = baseUsername + 'user';
    }
    
    // Verifica se está disponível
    const availability = await this.checkAvailability(baseUsername);
    
    if (availability.available) {
      return baseUsername;
    }
    
    // Se não está disponível, tenta as sugestões
    if (availability.suggestions) {
      for (const suggestion of availability.suggestions) {
        const suggestionAvailability = await this.checkAvailability(suggestion);
        if (suggestionAvailability.available) {
          return suggestion;
        }
      }
    }
    
    // Se nenhuma sugestão funcionou, gera um com timestamp
    const timestamp = Date.now().toString().slice(-4);
    return `${baseUsername}${timestamp}`;
  }

  /**
   * Atualiza o username de um usuário
   */
  static async updateUsername(userId: string, newUsername: string): Promise<void> {
    const validation = this.validateUsername(newUsername);
    
    if (!validation.valid) {
      throw new Error(validation.error || 'Invalid username');
    }
    
    const cleanUsername = newUsername.trim().toLowerCase();
    
    // Verifica se está disponível
    const availability = await this.checkAvailability(cleanUsername);
    if (!availability.available) {
      throw new Error('Username is not available');
    }
    
    try {
      console.log('🔄 Updating username in Firestore:', { userId, newUsername: cleanUsername });
      
      const userDocRef = doc(db, 'users', userId);
      await updateDoc(userDocRef, {
        username: cleanUsername,
        updatedAt: new Date()
      });
      
      console.log('✅ Username updated successfully');
    } catch (error) {
      console.error('❌ Error updating username:', error);
      throw new Error('Failed to update username');
    }
  }
}

export default UsernameService; 