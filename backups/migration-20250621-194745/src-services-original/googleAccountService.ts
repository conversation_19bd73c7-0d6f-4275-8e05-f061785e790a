import { signInWithPopup, linkWithPopup, unlink, User as FirebaseUser } from 'firebase/auth';
import { auth, googleProvider } from './firebase';
import { usersService } from './usersService';
import { authIntegrationService } from './authIntegrationService';

interface GoogleAccountInfo {
  isConnected: boolean;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  providerId: string | null;
}

class GoogleAccountService {
  private static instance: GoogleAccountService;

  public static getInstance(): GoogleAccountService {
    if (!GoogleAccountService.instance) {
      GoogleAccountService.instance = new GoogleAccountService();
    }
    return GoogleAccountService.instance;
  }

  /**
   * Verifica se o usuário atual tem uma conta Google conectada
   */
  getGoogleAccountInfo(user: FirebaseUser | null): GoogleAccountInfo {
    if (!user) {
      return {
        isConnected: false,
        email: null,
        displayName: null,
        photoURL: null,
        providerId: null
      };
    }

    // Verifica se há um provedor Google entre os provedores do usuário
    const googleProvider = user.providerData.find(provider => provider.providerId === 'google.com');
    
    return {
      isConnected: !!googleProvider,
      email: googleProvider?.email || null,
      displayName: googleProvider?.displayName || null,
      photoURL: googleProvider?.photoURL || null,
      providerId: googleProvider?.providerId || null
    };
  }

  /**
   * Conecta uma conta Google ao usuário atual
   */
  async connectGoogleAccount(): Promise<{ success: boolean; message: string; user?: FirebaseUser }> {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        return { success: false, message: 'No user is currently logged in' };
      }

      // Verifica se já tem uma conta Google conectada
      const googleInfo = this.getGoogleAccountInfo(currentUser);
      if (googleInfo.isConnected) {
        return { success: false, message: 'Google account is already connected' };
      }

      // Conecta a conta Google
      const result = await linkWithPopup(currentUser, googleProvider);
      
      // Sincroniza os dados atualizados com o Firestore
      const adminUser = await authIntegrationService.syncUserWithFirestore(result.user);
      
      // Atualiza os dados do usuário no Firestore com informações do Google
      if (result.user.displayName || result.user.photoURL) {
        const updateData: any = {};
        
        if (result.user.displayName && !adminUser.firstName) {
          const nameParts = result.user.displayName.split(' ');
          updateData.firstName = nameParts[0];
          updateData.lastName = nameParts.slice(1).join(' ');
        }
        
        if (result.user.photoURL) {
          updateData.avatarUrl = result.user.photoURL;
        }

        if (Object.keys(updateData).length > 0) {
          await usersService.update(adminUser.id, updateData);
        }
      }

      return {
        success: true,
        message: 'Google account connected successfully',
        user: result.user
      };
    } catch (error: any) {
      console.error('Error connecting Google account:', error);
      
      // Tratamento de erros específicos
      if (error.code === 'auth/account-exists-with-different-credential') {
        return {
          success: false,
          message: 'This Google account is already associated with another user'
        };
      } else if (error.code === 'auth/provider-already-linked') {
        return {
          success: false,
          message: 'Google account is already connected'
        };
      } else if (error.code === 'auth/popup-blocked') {
        return {
          success: false,
          message: 'Popup was blocked by browser. Please allow popups and try again'
        };
      } else if (error.code === 'auth/popup-closed-by-user') {
        return {
          success: false,
          message: 'Google sign-in was cancelled'
        };
      }

      return {
        success: false,
        message: error.message || 'Failed to connect Google account'
      };
    }
  }

  /**
   * Desconecta a conta Google do usuário atual
   */
  async disconnectGoogleAccount(): Promise<{ success: boolean; message: string }> {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        return { success: false, message: 'No user is currently logged in' };
      }

      // Verifica se tem uma conta Google conectada
      const googleInfo = this.getGoogleAccountInfo(currentUser);
      if (!googleInfo.isConnected) {
        return { success: false, message: 'No Google account is connected' };
      }

      // Verifica se o usuário tem outros métodos de login além do Google
      const hasOtherProviders = currentUser.providerData.some(
        provider => provider.providerId !== 'google.com'
      );
      
      // Se o usuário só tem o Google como provedor, não pode desconectar
      if (!hasOtherProviders && currentUser.providerData.length === 1) {
        return {
          success: false,
          message: 'Cannot disconnect Google account as it is your only sign-in method. Please add an email/password first.'
        };
      }

      // Desconecta a conta Google
      await unlink(currentUser, 'google.com');

      // Sincroniza as mudanças com o Firestore
      await authIntegrationService.syncUserWithFirestore(currentUser);

      return {
        success: true,
        message: 'Google account disconnected successfully'
      };
    } catch (error: any) {
      console.error('Error disconnecting Google account:', error);

      if (error.code === 'auth/no-such-provider') {
        return {
          success: false,
          message: 'No Google account is connected'
        };
      }

      return {
        success: false,
        message: error.message || 'Failed to disconnect Google account'
      };
    }
  }

  /**
   * Login direto com Google (não vinculação)
   */
  async signInWithGoogle(): Promise<{ success: boolean; message: string; user?: FirebaseUser }> {
    try {
      const result = await signInWithPopup(auth, googleProvider);
      
      // Sincroniza com o Firestore
      await authIntegrationService.syncUserWithFirestore(result.user);

      return {
        success: true,
        message: 'Signed in with Google successfully',
        user: result.user
      };
    } catch (error: any) {
      console.error('Error signing in with Google:', error);

      if (error.code === 'auth/popup-blocked') {
        return {
          success: false,
          message: 'Popup was blocked by browser. Please allow popups and try again'
        };
      } else if (error.code === 'auth/popup-closed-by-user') {
        return {
          success: false,
          message: 'Google sign-in was cancelled'
        };
      }

      return {
        success: false,
        message: error.message || 'Failed to sign in with Google'
      };
    }
  }
}

export const googleAccountService = GoogleAccountService.getInstance(); 