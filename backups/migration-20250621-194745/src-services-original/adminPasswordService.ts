// Serviço para gerenciar senhas de usuários na área admin
import { createUserWithEmailAndPassword, updatePassword, signInWithEmailAndPassword, deleteUser, User as FirebaseUser } from 'firebase/auth';
import { auth, getCurrentEnvironment } from './firebase';
import { usersService } from './usersService';

export interface CreateUserWithPasswordData {
  email: string;
  password: string;
  name: string;
  role?: 'user' | 'admin' | 'moderator' | 'ambassador';
  status?: 'active' | 'inactive' | 'banned' | 'pending';
  phoneNumber?: string;
  bio?: string;
  location?: string;
  emailVerified?: boolean;
  preferences?: {
    notifications: boolean;
    publicProfile: boolean;
    showLocation: boolean;
    showEmail: boolean;
    allowMessages: boolean;
  };
}

export interface UpdateUserPasswordData {
  userId: string;
  newPassword: string;
  currentPassword?: string; // Para validação se necessário
}

class AdminPasswordService {
  /**
   * Cria um usuário com email e senha no Firebase Auth e Firestore
   */
  async createUserWithPassword(data: CreateUserWithPasswordData) {
    console.log('👤 Creating user with password:', data.email);
    
    try {
      // 1. Criar usuário no Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, data.email, data.password);
      const firebaseUser = userCredential.user;
      
      console.log('✅ Firebase Auth user created:', firebaseUser.uid);
      
      // Verificar ambiente
      const environment = import.meta.env.VITE_FIREBASE_PROJECT_ID === 'iconpal-cf925' ? 'production' : 'development';
      console.log('📍 Auth environment:', environment === 'development' ? 'Development project' : 'Production project');
      
      // 2. Criar usuário no Firestore usando o UID do Firebase Auth
      const nameParts = data.name.split(' ');
      const firestoreUserData = {
        firstName: nameParts[0] || 'User',
        lastName: nameParts.slice(1).join(' ') || '',
        email: data.email,
        status: data.status || 'active',
        role: data.role || 'user',
        emailVerified: data.emailVerified || false,
        phoneNumber: data.phoneNumber,
        bio: data.bio,
        location: data.location,
        preferences: data.preferences || {
          notifications: true,
          publicProfile: true,
          showLocation: false,
          showEmail: false,
          allowMessages: true
        }
      };
      
      const firestoreUser = await usersService.createWithUID(firebaseUser.uid, firestoreUserData);
      
      console.log('✅ Firestore user created:', firestoreUser.id);
      
      return {
        success: true,
        firebaseUser,
        firestoreUser,
        message: 'User created successfully in both Firebase Auth and Firestore'
      };
      
    } catch (error: any) {
      console.error('❌ Error creating user with password:', error);
      
      // Se houve erro, tentar limpar o que foi criado
      try {
        const currentUser = auth.currentUser;
        if (currentUser && currentUser.email === data.email) {
          await deleteUser(currentUser);
          console.log('🧹 Cleaned up Firebase Auth user after error');
        }
      } catch (cleanupError) {
        console.warn('⚠️ Could not cleanup Firebase Auth user:', cleanupError);
      }
      
      throw error;
    }
  }
  
  /**
   * Atualiza a senha de um usuário
   */
  async updateUserPassword(data: UpdateUserPasswordData) {
    console.log('🔐 Updating password for user:', data.userId);
    
    try {
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        throw new Error('No user is currently signed in');
      }
      
      if (currentUser.uid !== data.userId) {
        throw new Error('Can only update password for currently signed in user');
      }
      
      await updatePassword(currentUser, data.newPassword);
      
      console.log('✅ Password updated successfully');
      
      return {
        success: true,
        message: 'Password updated successfully'
      };
      
    } catch (error) {
      console.error('❌ Error updating user password:', error);
      throw error;
    }
  }
  
  /**
   * Envia email de reset de senha para um usuário
   */
  async sendPasswordResetEmail(email: string) {
    console.log('📧 Sending password reset email to:', email);
    
    try {
      const { sendPasswordResetEmail } = await import('firebase/auth');
      await sendPasswordResetEmail(auth, email);
      
      console.log('✅ Password reset email sent successfully');
      
      return {
        success: true,
        message: 'Password reset email sent successfully'
      };
      
    } catch (error: any) {
      console.error('❌ Error sending password reset email:', error);
      throw error;
    }
  }
  
  /**
   * Valida se uma senha atende aos critérios mínimos
   */
  validatePassword(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 6) {
      errors.push('Password must be at least 6 characters long');
    }
    
    if (password.length > 128) {
      errors.push('Password must be less than 128 characters');
    }
    
    // Adicione mais validações conforme necessário
    // if (!/[A-Z]/.test(password)) {
    //   errors.push('Password must contain at least one uppercase letter');
    // }
    
    // if (!/[a-z]/.test(password)) {
    //   errors.push('Password must contain at least one lowercase letter');
    // }
    
    // if (!/[0-9]/.test(password)) {
    //   errors.push('Password must contain at least one number');
    // }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Gera uma senha temporária segura
   */
  generateTemporaryPassword(length: number = 12): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    return password;
  }

  /**
   * Deleta um usuário do Firebase Auth usando Admin SDK via API
   */
  async deleteUserFromAuthViaAPI(uid: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🗑️ Attempting to delete user from Firebase Auth via Admin API:', uid);
      
      const response = await fetch('http://localhost:3001/api/admin/users/delete-from-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ uid })
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete user from Auth');
      }

      console.log('✅ User deleted from Firebase Auth via Admin API');
      return result;
    } catch (error: any) {
      console.error('❌ Error deleting user via Admin API:', error);
      return {
        success: false,
        message: `Failed to delete from Firebase Auth via API: ${error.message}`
      };
    }
  }

  /**
   * Verifica se email existe usando Admin SDK via API
   */
  async checkEmailExistsViaAPI(email: string): Promise<boolean> {
    try {
      const response = await fetch('http://localhost:3001/api/admin/users/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email })
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to check email');
      }

      return result.exists;
    } catch (error: any) {
      console.error('❌ Error checking email via Admin API:', error);
      // Em caso de erro, fallback para método cliente
      return false;
    }
  }

  /**
   * Deleta um usuário do Firebase Auth (método original + Admin SDK)
   */
  async deleteUserFromAuth(email: string, password?: string): Promise<{ success: boolean; message: string }> {
    console.log('🗑️ Attempting to delete user from Firebase Auth:', email);
    
    try {
      // Método 1: Tentar deletar se o usuário estiver logado
      const currentUser = auth.currentUser;
      if (currentUser && currentUser.email === email) {
        await deleteUser(currentUser);
        console.log('✅ User deleted from Firebase Auth (was current user)');
        return {
          success: true,
          message: 'User deleted from Firebase Auth successfully'
        };
      }

      // Método 2: Tentar fazer login e deletar (se senha fornecida)
      if (password) {
        try {
          console.log('🔐 Attempting to sign in user for deletion...');
          const userCredential = await signInWithEmailAndPassword(auth, email, password);
          const userToDelete = userCredential.user;
          
          await deleteUser(userToDelete);
          console.log('✅ User deleted from Firebase Auth after sign-in');
          
          return {
            success: true,
            message: 'User deleted from Firebase Auth successfully'
          };
        } catch (signInError: any) {
          console.warn('⚠️ Could not sign in user for deletion:', signInError.message);
          
          // Se falhou o login, pode ser que a senha esteja incorreta ou usuário não exista
          if (signInError.code === 'auth/user-not-found') {
            return {
              success: true,
              message: 'User does not exist in Firebase Auth (already deleted or never created)'
            };
          }
        }
      }

      // Método 3: Tentar usar Admin SDK via API
      console.log('🔧 Attempting to use Admin SDK via API...');
      
      // Primeiro, precisamos do UID. Vamos tentar obter pelo email
      try {
        // Se temos o usuário no Firestore, podemos usar o ID (que é o UID)
        const firestoreUser = await usersService.getByEmail(email);
        if (firestoreUser) {
          const adminResult = await this.deleteUserFromAuthViaAPI(firestoreUser.id);
          if (adminResult.success) {
            return adminResult;
          }
        }
      } catch (adminError) {
        console.warn('⚠️ Admin SDK deletion failed:', adminError);
      }

      // Método 4: Informar que precisa de intervenção manual
      console.warn('⚠️ Cannot delete user from Firebase Auth without Admin SDK or password');
      return {
        success: false,
        message: 'User deletion from Firebase Auth requires Admin SDK, user password, or manual intervention. User removed from Firestore only.'
      };

    } catch (error: any) {
      console.error('❌ Error deleting user from Firebase Auth:', error);
      return {
        success: false,
        message: `Failed to delete from Firebase Auth: ${error.message}`
      };
    }
  }

  /**
   * Deleta um usuário completamente (Firestore + Firebase Auth)
   * Agora usa UID como chave primária para ambos os sistemas
   */
  async deleteUserCompletely(userIdOrEmail: string, userPassword?: string): Promise<{
    firestoreDeleted: boolean;
    authDeleted: boolean;
    message: string;
    userUID?: string;
  }> {
    console.log('🗑️ Starting complete user deletion for:', userIdOrEmail);
    
    let firestoreDeleted = false;
    let authDeleted = false;
    const messages: string[] = [];
    let userUID: string | undefined;
    let userEmail: string | undefined;

    try {
      // 1. Primeiro, encontrar o usuário no Firestore para obter UID e email
      let firestoreUser;
      
      if (userIdOrEmail.includes('@')) {
        console.log('🔍 Looking up user by email:', userIdOrEmail);
        firestoreUser = await usersService.getByEmail(userIdOrEmail);
        userEmail = userIdOrEmail;
      } else {
        console.log('🔍 Looking up user by UID:', userIdOrEmail);
        firestoreUser = await usersService.getById(userIdOrEmail);
        userUID = userIdOrEmail;
        userEmail = firestoreUser?.email;
      }

      if (firestoreUser) {
        userUID = firestoreUser.id; // Este é o UID do Firebase Auth
        userEmail = firestoreUser.email;
        console.log('✅ Found user:', { uid: userUID, email: userEmail });
      } else {
        console.warn('⚠️ User not found in Firestore');
        messages.push('⚠️ User not found in Firestore');
      }

      // 2. Deletar do Firestore usando UID
      if (firestoreUser && userUID) {
        try {
          console.log('🗑️ Deleting user from Firestore with UID:', userUID);
          await usersService.delete(userUID);
          firestoreDeleted = true;
          messages.push('✅ User deleted from Firestore');
          console.log('✅ User deleted from Firestore successfully');
        } catch (firestoreError: any) {
          console.error('❌ Error deleting from Firestore:', firestoreError);
          messages.push(`❌ Firestore deletion failed: ${firestoreError.message}`);
        }
      }

      // 3. Tentar deletar do Firebase Auth usando email e UID
      if (userEmail && userUID) {
        try {
          console.log('🗑️ Attempting to delete user from Firebase Auth...');
          
          // Primeiro tentar via Admin SDK
          const adminResult = await this.deleteUserFromAuthViaAPI(userUID);
          if (adminResult.success) {
            authDeleted = true;
            messages.push('✅ User deleted from Firebase Auth via Admin SDK');
          } else {
            // Fallback para método original
            const authResult = await this.deleteUserFromAuth(userEmail, userPassword);
            if (authResult.success) {
              authDeleted = true;
              messages.push('✅ User deleted from Firebase Auth');
            } else {
              messages.push(`⚠️ Firebase Auth: ${authResult.message}`);
            }
          }
        } catch (authError: any) {
          console.error('❌ Error deleting from Firebase Auth:', authError);
          messages.push(`❌ Firebase Auth deletion failed: ${authError.message}`);
        }
      } else {
        messages.push('⚠️ Cannot delete from Firebase Auth: email or UID not found');
      }

    } catch (error: any) {
      console.error('❌ Unexpected error during complete deletion:', error);
      messages.push(`❌ Unexpected error: ${error.message}`);
    }

    const finalMessage = messages.join('\n');
    console.log('🏁 Complete deletion result:', { 
      userUID, 
      userEmail, 
      firestoreDeleted, 
      authDeleted, 
      finalMessage 
    });

    return {
      firestoreDeleted,
      authDeleted,
      message: finalMessage,
      userUID
    };
  }
}

// Exportar instância singleton
export const adminPasswordService = new AdminPasswordService();

// Exportar classe para testes
export { AdminPasswordService }; 