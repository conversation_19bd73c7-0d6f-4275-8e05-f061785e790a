import { PinsDataConnectService, Pin as DataConnectPin, CreatePinData } from './dataconnect/pinsDataConnectService';
import { BoardsDataConnectService } from './dataconnect/boardsDataConnectService';
import { usersApiService } from './api/usersApiService';
import { useAuthStore } from '@/store/authStore';
import { Pin } from '@/types/pin';

export interface PinInput extends CreatePinData {}

// Função para converter dados do DataConnect para o formato esperado pelos componentes
const convertDataConnectPinToPin = (dcPin: any): Pin => {
  console.log('🔄 Converting DataConnect pin to Pin:', dcPin);
  console.log('🔍 User data in dcPin:', dcPin.user);
  
  const convertedPin = {
    id: dcPin.id,
    name: dcPin.title || dcPin.name || 'Untitled Pin',
    image: dcPin.imageUrl || dcPin.image || '',
    description: dcPin.description,
    
    // Novos campos do schema
    origin: dcPin.origin,
    releaseYear: dcPin.releaseYear,
    originalPrice: dcPin.originalPrice,
    pinNumber: dcPin.pinNumber,
    tradable: dcPin.tradable || false,
    
    // Campos legados (manter para compatibilidade)
    category: 'other' as const, // Default category
    rarity: 'common' as const, // Default rarity
    condition: 'excellent' as const, // Default condition
    year: dcPin.releaseYear, // Mapeamento para compatibilidade
    series: dcPin.origin, // Mapeamento para compatibilidade
    tags: [],
    isForTrade: dcPin.tradable || false, // Mapeamento para compatibilidade
    
    // Campos de sistema
    likes: dcPin.likesCount || 0,
    comments: 0, // Default
    isLiked: false, // Default
    isSaved: false, // Default
    userId: dcPin.userId || (dcPin.user ? dcPin.user.id : ''),
    createdAt: dcPin.createdAt ? new Date(dcPin.createdAt) : new Date(),
    updatedAt: dcPin.updatedAt ? new Date(dcPin.updatedAt) : new Date(),
    owner: dcPin.user ? {
      id: dcPin.user.id || dcPin.userId,
      name: dcPin.user.displayName || dcPin.user.username || 'Unknown User',
      username: dcPin.user.username,
      avatar: dcPin.user.avatarUrl
    } : (console.log('⚠️ No user data in dcPin'), undefined)
  };
  
  console.log('✅ Converted pin:', convertedPin);
  console.log('🔍 Owner data in converted pin:', convertedPin.owner);
  return convertedPin;
};

class PinsService {
  private dataConnectService = new PinsDataConnectService();
  private boardsDataConnectService = new BoardsDataConnectService();
  // private userService = new PostgreSQLUserService(); // Disabled for frontend compatibility

  async getById(id: string): Promise<Pin | null> {
    const dcPin = await this.dataConnectService.getById(id);
    return dcPin ? convertDataConnectPinToPin(dcPin) : null;
  }

  async getUserPins(userId: string, limit: number = 20): Promise<Pin[]> {
    try {
      console.log('🔄 Getting user pins from PostgreSQL API:', { userId, limit });
      
      const response = await fetch(`http://localhost:3001/api/pins/user/${userId}?limit=${limit}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch user pins: ${response.statusText}`);
      }
      
      const pins = await response.json();
      console.log('✅ Found user pins from PostgreSQL:', pins.length);
      
      // Convert to Pin format
      return pins.map((pin: any) => ({
        id: pin.id,
        name: pin.title,
        image: pin.imageUrl,
        description: pin.description,
        origin: pin.origin,
        releaseYear: pin.releaseYear,
        originalPrice: pin.originalPrice,
        pinNumber: pin.pinNumber,
        tradable: pin.tradable,
        category: 'other',
        rarity: 'common',
        condition: 'excellent',
        year: pin.releaseYear,
        series: pin.origin,
        tags: [],
        isForTrade: pin.tradable,
        likes: pin.likesCount,
        comments: pin.commentsCount,
        isLiked: false, // This would need to be checked separately
        isSaved: false, // This would need to be checked separately
        userId: pin.userId,
        createdAt: new Date(pin.createdAt),
        updatedAt: new Date(pin.updatedAt),
        owner: pin.user
      }));
    } catch (error) {
      console.error('❌ Error fetching user pins from PostgreSQL:', error);
      return [];
    }
  }

  async create(data: PinInput): Promise<{ id: string }> {
    const { user } = useAuthStore.getState();
    if (!user) throw new Error('Unauthenticated');

    // await this.userService.ensureUserExists({
    //   id: user.id,
    //   email: user.email!,
    //   username: user.username!,
    //   displayName: user.displayName!
    // }); // Disabled for frontend compatibility

    return this.dataConnectService.create({
      userId: user.id,
      title: data.title,
      description: data.description,
      imageUrl: data.imageUrl,
      origin: data.origin,
      releaseYear: data.releaseYear,
      originalPrice: data.originalPrice,
      pinNumber: data.pinNumber,
      tradable: data.tradable,
      isPublic: data.isPublic,
      boardId: data.boardId // Pass boardId to automatically add to board
    });
  }

  async update(id: string, data: Partial<CreatePinData>): Promise<void> {
    console.log('🔄 pinsService.update iniciado');
    console.log('📝 Dados recebidos no pinsService:', data);
    
    try {
      // Use PostgreSQL API instead of Firebase DataConnect
      const response = await fetch(`http://localhost:3001/api/pins/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.text();
        throw new Error(`Failed to update pin: ${error}`);
      }
      
      const updatedPin = await response.json();
      console.log('✅ Pin updated successfully via PostgreSQL API:', updatedPin);
      
    } catch (error) {
      console.error('❌ Error updating pin via PostgreSQL API:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    return this.dataConnectService.delete(id);
  }

  async addPinToBoard(pinId: string, boardId: string): Promise<void> {
    return this.boardsDataConnectService.addPinToBoard(boardId, pinId);
  }

  async getBoardPins(boardId: string, limit: number = 20): Promise<Pin[]> {
    console.log('🔄 Getting board pins for board:', boardId);
    const dcPins = await this.boardsDataConnectService.getBoardPins(boardId, limit);
    return dcPins.map(convertDataConnectPinToPin);
  }

  async updateLikes(pinId: string, likes: number, isLiked: boolean): Promise<void> {
    const { user } = useAuthStore.getState();
    if (!user) throw new Error('Unauthenticated');

    try {
      const response = await fetch(`http://localhost:3001/api/pins/${pinId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          userId: user.id, 
          isLiked 
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update likes: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('✅ Likes updated successfully:', result);
    } catch (error) {
      console.error('❌ Error updating likes:', error);
      throw error;
    }
  }

  async toggleSaved(pinId: string, isSaved: boolean): Promise<void> {
    const { user } = useAuthStore.getState();
    if (!user) throw new Error('Unauthenticated');

    try {
      const response = await fetch(`http://localhost:3001/api/pins/${pinId}/save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          userId: user.id, 
          isSaved 
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to toggle saved: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('✅ Saved status updated successfully:', result);
    } catch (error) {
      console.error('❌ Error toggling saved:', error);
      throw error;
    }
  }

  async getPinBoards(pinId: string): Promise<any[]> {
    try {
      console.log('🔄 Getting boards for pin:', pinId);
      
      // Use PostgreSQL API to get boards that contain this pin
      const response = await fetch(`http://localhost:3001/api/pins/${pinId}/boards`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch pin boards: ${response.statusText}`);
      }
      
      const boards = await response.json();
      console.log('✅ Found boards for pin:', boards);
      
      return boards;
    } catch (error) {
      console.error('❌ Error fetching pin boards:', error);
      return [];
    }
  }

  // ===== COMMENTS METHODS =====

  async getComments(pinId: string, limit: number = 20, offset: number = 0): Promise<any[]> {
    try {
      console.log('🔄 Getting comments for pin:', pinId);
      
      const response = await fetch(`http://localhost:3001/api/pins/${pinId}/comments?limit=${limit}&offset=${offset}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch comments: ${response.statusText}`);
      }
      
      const comments = await response.json();
      console.log('✅ Found comments for pin:', comments.length);
      
      return comments;
    } catch (error) {
      console.error('❌ Error fetching comments:', error);
      return [];
    }
  }

  async createComment(pinId: string, content: string, parentCommentId?: string): Promise<any> {
    const { user } = useAuthStore.getState();
    if (!user) throw new Error('Unauthenticated');

    try {
      const response = await fetch(`http://localhost:3001/api/pins/${pinId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          userId: user.id, 
          content,
          parentCommentId 
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create comment: ${response.statusText}`);
      }

      const comment = await response.json();
      console.log('✅ Comment created successfully:', comment);
      return comment;
    } catch (error) {
      console.error('❌ Error creating comment:', error);
      throw error;
    }
  }

  async updateComment(commentId: string, content: string): Promise<any> {
    const { user } = useAuthStore.getState();
    if (!user) throw new Error('Unauthenticated');

    try {
      const response = await fetch(`http://localhost:3001/api/comments/${commentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          userId: user.id, 
          content 
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update comment: ${response.statusText}`);
      }

      const comment = await response.json();
      console.log('✅ Comment updated successfully:', comment);
      return comment;
    } catch (error) {
      console.error('❌ Error updating comment:', error);
      throw error;
    }
  }

  async deleteComment(commentId: string): Promise<void> {
    const { user } = useAuthStore.getState();
    if (!user) throw new Error('Unauthenticated');

    try {
      const response = await fetch(`http://localhost:3001/api/comments/${commentId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          userId: user.id 
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to delete comment: ${response.statusText}`);
      }

      console.log('✅ Comment deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting comment:', error);
      throw error;
    }
  }

  // ===== STATUS CHECK METHODS =====

  async checkLikeStatus(pinId: string): Promise<boolean> {
    const { user } = useAuthStore.getState();
    if (!user) return false;

    try {
      const response = await fetch(`http://localhost:3001/api/pins/${pinId}/likes/check/${user.id}`);
      
      if (!response.ok) {
        return false;
      }
      
      const result = await response.json();
      return result.isLiked;
    } catch (error) {
      console.error('❌ Error checking like status:', error);
      return false;
    }
  }

  async checkSaveStatus(pinId: string): Promise<boolean> {
    const { user } = useAuthStore.getState();
    if (!user) return false;

    try {
      const response = await fetch(`http://localhost:3001/api/pins/${pinId}/save/check/${user.id}`);
      
      if (!response.ok) {
        return false;
      }
      
      const result = await response.json();
      return result.isSaved;
    } catch (error) {
      console.error('❌ Error checking save status:', error);
      return false;
    }
  }

  async getLikes(pinId: string, limit: number = 20, offset: number = 0): Promise<any[]> {
    try {
      const response = await fetch(`http://localhost:3001/api/pins/${pinId}/likes?limit=${limit}&offset=${offset}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch likes: ${response.statusText}`);
      }
      
      const likes = await response.json();
      console.log('✅ Found likes for pin:', likes.length);
      
      return likes;
    } catch (error) {
      console.error('❌ Error fetching likes:', error);
      return [];
    }
  }

  async getSavedPins(userId?: string, limit: number = 20, offset: number = 0): Promise<Pin[]> {
    const { user } = useAuthStore.getState();
    const targetUserId = userId || user?.id;
    
    if (!targetUserId) throw new Error('User ID required');

    try {
      const response = await fetch(`http://localhost:3001/api/users/${targetUserId}/saved-pins?limit=${limit}&offset=${offset}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch saved pins: ${response.statusText}`);
      }
      
      const savedPins = await response.json();
      console.log('✅ Found saved pins for user:', savedPins.length);
      
      return savedPins;
    } catch (error) {
      console.error('❌ Error fetching saved pins:', error);
      return [];
    }
  }

  // ===== COMMENT LIKES METHODS =====

  async toggleCommentLike(commentId: string, isLiked: boolean): Promise<{ success: boolean; isLiked: boolean; likeCount: number }> {
    const { user } = useAuthStore.getState();
    if (!user) throw new Error('Unauthenticated');

    try {
      const response = await fetch(`http://localhost:3001/api/comments/${commentId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          userId: user.id, 
          isLiked 
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to toggle comment like: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('✅ Comment like toggled successfully:', result);
      return result;
    } catch (error) {
      console.error('❌ Error toggling comment like:', error);
      throw error;
    }
  }

  async checkCommentLikeStatus(commentId: string): Promise<boolean> {
    const { user } = useAuthStore.getState();
    if (!user) return false;

    try {
      const response = await fetch(`http://localhost:3001/api/comments/${commentId}/likes/check/${user.id}`);
      
      if (response.status === 404) {
        // Comment not found, return false
        console.log(`⚠️ Comment ${commentId} not found, returning false`);
        return false;
      }
      
      if (!response.ok) {
        throw new Error(`Failed to check comment like status: ${response.statusText}`);
      }

      const result = await response.json();
      return result.isLiked;
    } catch (error) {
      console.error('❌ Error checking comment like status:', error);
      return false;
    }
  }

  async getCommentLikes(commentId: string, limit: number = 20, offset: number = 0): Promise<any[]> {
    try {
      const response = await fetch(`http://localhost:3001/api/comments/${commentId}/likes?limit=${limit}&offset=${offset}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch comment likes: ${response.statusText}`);
      }

      const likes = await response.json();
      console.log('✅ Comment likes fetched successfully:', likes.length);
      return likes;
    } catch (error) {
      console.error('❌ Error fetching comment likes:', error);
      throw error;
    }
  }
}

export const pinsService = new PinsService();