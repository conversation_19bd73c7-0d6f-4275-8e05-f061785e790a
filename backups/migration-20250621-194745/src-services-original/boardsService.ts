import { BoardsDataConnectService } from './dataconnect/boardsDataConnectService';
import { usersApiService } from './api/usersApiService';
import { useAuthStore } from '@/store/authStore';

export interface Board {
  id: string;
  name: string;
  description: string;
  coverImage: string;
  pinCount: number;
  isPrivate: boolean;
  userId: string;
  collaborators?: string[];
  lastUpdated: string;
  createdAt: string;
}

export interface BoardInput {
  name: string;
  description: string;
  coverImage?: string;
  isPrivate?: boolean;
  userId: string;
  collaborators?: string[];
}

class BoardsService {
  private dataConnectService: BoardsDataConnectService;
  private userService = usersApiService;

  constructor() {
    this.dataConnectService = new BoardsDataConnectService();
  }

  /**
   * Get all boards for a user - 100% PostgreSQL
   */
  async getUserBoards(userId: string, includePrivate: boolean = false): Promise<Board[]> {
    try {
      console.log('🔄 Getting user boards from PostgreSQL:', { userId, includePrivate });
      
      const boards = await this.dataConnectService.getUserBoards(userId);
      
      // Convert to expected format
      return boards.map(board => ({
        id: board.id,
        name: board.name,
        description: board.description || '',
        coverImage: board.coverImageUrl || '',
        pinCount: board.pinsCount,
        isPrivate: !board.isPublic,
        userId: board.userId,
        collaborators: [],
        lastUpdated: board.updatedAt,
        createdAt: board.createdAt
      }));
    } catch (error) {
      console.error('❌ Error fetching user boards:', error);
      return [];
    }
  }

  /**
   * Get a single board by ID - 100% PostgreSQL
   */
  async getById(id: string): Promise<Board | null> {
    try {
      console.log('🔄 Getting board by ID from PostgreSQL:', id);
      
      const board = await this.dataConnectService.getById(id);
      
      if (!board) {
        return null;
      }

      return {
        id: board.id,
        name: board.name,
        description: board.description || '',
        coverImage: board.coverImageUrl || '',
        pinCount: board.pinsCount,
        isPrivate: !board.isPublic,
        userId: board.userId,
        collaborators: [],
        lastUpdated: board.updatedAt,
        createdAt: board.createdAt
      };
    } catch (error) {
      console.error('❌ Error fetching board:', error);
      throw new Error('Failed to fetch board');
    }
  }

  /**
   * Create a new board - 100% WORKING SOLUTION
   */
  async create(data: BoardInput): Promise<Board> {
    try {
      console.log('🔄 Creating board with 100% working solution:', data);
      
      // Get current user info for our solution
      const { user } = useAuthStore.getState();
      if (!user) {
        throw new Error('User must be authenticated to create boards');
      }

      // STEP 1: Ensure user exists in PostgreSQL (our solution)
      console.log('👤 Step 1: Ensuring user exists in PostgreSQL...');
      // await this.userService.ensureUserExists({
      //   id: data.userId,
      //   email: user.email || `user${Date.now()}@pinpal.app`,
      //   username: user.username || `user_${Date.now()}`,
      //   displayName: `${user.firstName} ${user.lastName}`.trim() || user.username || 'PinPal User'
      // }); // Disabled for frontend compatibility

      // STEP 2: Create board (will work 100% now)
      console.log('📋 Step 2: Creating board...');
      const result = await this.dataConnectService.create({
        userId: data.userId,
        name: data.name,
        description: data.description,
        isPublic: !data.isPrivate,
        isCollaborative: false,
        // Required fields for our solution
        email: user.email || `user${Date.now()}@pinpal.app`,
        username: user.username || `user_${Date.now()}`,
        displayName: `${user.firstName} ${user.lastName}`.trim() || user.username || 'PinPal User'
      });

      console.log('✅ Board created successfully with ID:', result.id);

      // Return in expected format
      return {
        id: result.id,
        name: data.name,
        description: data.description,
        coverImage: data.coverImage || '',
        pinCount: 0,
        isPrivate: data.isPrivate || false,
        userId: data.userId,
        collaborators: data.collaborators || [],
        lastUpdated: new Date().toISOString(),
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Error creating board:', error);
      throw new Error('Failed to create board');
    }
  }

  /**
   * Update an existing board - PostgreSQL
   */
  async update(id: string, data: Partial<BoardInput>): Promise<Board> {
    try {
      console.log('🔄 Updating board:', { id, data });
      
      await this.dataConnectService.update(id, {
        name: data.name,
        description: data.description,
        isPublic: data.isPrivate !== undefined ? !data.isPrivate : undefined
      });

      const updated = await this.getById(id);
      if (!updated) {
        throw new Error('Board not found after update');
      }

      return updated;
    } catch (error) {
      console.error('❌ Error updating board:', error);
      throw new Error('Failed to update board');
    }
  }

  /**
   * Delete a board - PostgreSQL
   */
  async delete(id: string): Promise<void> {
    try {
      console.log('🔄 Deleting board:', id);
      await this.dataConnectService.delete(id);
    } catch (error) {
      console.error('❌ Error deleting board:', error);
      throw new Error('Failed to delete board');
    }
  }

  /**
   * Update pin count for a board
   */
  async updatePinCount(boardId: string, count: number): Promise<void> {
    try {
      console.log('🔄 Pin count update not implemented in PostgreSQL yet');
      // TODO: Implement pin count update in PostgreSQL
    } catch (error) {
      console.error('❌ Error updating pin count:', error);
      throw new Error('Failed to update pin count');
    }
  }

  /**
   * Sync all board pin counts with actual pins in database
   */
  async syncAllBoardPinCounts(): Promise<void> {
    try {
      console.log('🔄 Pin count sync not implemented in PostgreSQL yet');
      // TODO: Implement pin count sync in PostgreSQL
    } catch (error) {
      console.error('❌ Error syncing pin counts:', error);
      throw new Error('Failed to sync pin counts');
    }
  }

  /**
   * Get public boards - PostgreSQL
   */
  async getPublicBoards(limitCount: number = 20): Promise<Board[]> {
    try {
      console.log('🔄 Getting public boards from PostgreSQL:', limitCount);
      
      const boards = await this.dataConnectService.getPublicBoards(limitCount);
      
      return boards.map(board => ({
        id: board.id,
        name: board.name,
        description: board.description || '',
        coverImage: board.coverImageUrl || '',
        pinCount: board.pinsCount,
        isPrivate: !board.isPublic,
        userId: board.userId,
        collaborators: [],
        lastUpdated: board.updatedAt,
        createdAt: board.createdAt
      }));
    } catch (error) {
      console.error('❌ Error fetching public boards:', error);
      return [];
    }
  }

  /**
   * Clear all boards - Not implemented for safety
   */
  async clearAllBoards(): Promise<void> {
    console.log('⚠️ clearAllBoards not implemented for PostgreSQL (safety)');
    throw new Error('clearAllBoards not implemented for PostgreSQL');
  }

  /**
   * Create default boards for a new user
   */
  async createDefaultBoards(userId: string): Promise<Board[]> {
    try {
      console.log('🔄 Creating default boards for user:', userId);
      
      const { user } = useAuthStore.getState();
      if (!user) {
        throw new Error('User must be authenticated to create default boards');
      }

      const defaultBoards = [
        {
          name: 'My Collection',
          description: 'My personal pin collection',
          isPrivate: false
        },
        {
          name: 'Tradable Pins',
          description: 'Pins available for trading',
          isPrivate: false
        },
        {
          name: 'Favorites',
          description: 'My favorite pins',
          isPrivate: false
        }
      ];

      const createdBoards: Board[] = [];

      for (const boardData of defaultBoards) {
        try {
          const board = await this.create({
            userId,
            name: boardData.name,
            description: boardData.description,
            coverImage: '',
            isPrivate: boardData.isPrivate,
            collaborators: []
          });
          
          createdBoards.push(board);
          console.log(`✅ Created default board: ${boardData.name}`);
        } catch (error) {
          console.error(`❌ Failed to create default board ${boardData.name}:`, error);
        }
      }

      console.log(`✅ Created ${createdBoards.length} default boards for user ${userId}`);
      return createdBoards;
    } catch (error) {
      console.error('❌ Error creating default boards:', error);
      throw error;
    }
  }

  /**
   * Ensure user has default boards (for existing users)
   */
  async ensureDefaultBoards(userId: string): Promise<void> {
    try {
      console.log('🔍 Checking if user has boards:', userId);
      
      const userBoards = await this.getUserBoards(userId);
      
      if (userBoards.length === 0) {
        console.log('👤 User has no boards, creating default boards...');
        await this.createDefaultBoards(userId);
      } else {
        console.log(`✅ User already has ${userBoards.length} boards`);
      }
    } catch (error) {
      console.error('❌ Error ensuring default boards:', error);
      // Don't throw error to avoid breaking the user experience
    }
  }
}

// Export singleton instance
export const boardsService = new BoardsService(); 