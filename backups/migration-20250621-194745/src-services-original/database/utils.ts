/**
 * Database utility functions
 */

/**
 * Remove undefined fields from object (Firestore doesn't accept undefined values)
 */
export const removeUndefinedFields = (obj: any): any => {
  const cleaned: any = {};
  Object.keys(obj).forEach(key => {
    if (obj[key] !== undefined) {
      cleaned[key] = obj[key];
    }
  });
  return cleaned;
};

/**
 * Validate required fields in an object
 */
export const validateRequiredFields = (obj: any, requiredFields: string[]): void => {
  const missingFields = requiredFields.filter(field => 
    obj[field] === undefined || obj[field] === null || obj[field] === ''
  );
  
  if (missingFields.length > 0) {
    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
  }
};

/**
 * Sanitize string fields (trim whitespace, remove empty strings)
 */
export const sanitizeStringFields = (obj: any, stringFields: string[]): any => {
  const sanitized = { ...obj };
  
  stringFields.forEach(field => {
    if (typeof sanitized[field] === 'string') {
      sanitized[field] = sanitized[field].trim();
      if (sanitized[field] === '') {
        sanitized[field] = undefined;
      }
    }
  });
  
  return sanitized;
};

/**
 * Convert Date objects to Firestore Timestamps
 */
export const convertDatesToTimestamps = (obj: any, dateFields: string[]): any => {
  const converted = { ...obj };
  
  dateFields.forEach(field => {
    if (converted[field] instanceof Date) {
      const { Timestamp } = require('firebase/firestore');
      converted[field] = Timestamp.fromDate(converted[field]);
    }
  });
  
  return converted;
};

/**
 * Prepare data for Firestore (combines all sanitization functions)
 */
export const prepareForFirestore = (
  obj: any, 
  options: {
    requiredFields?: string[];
    stringFields?: string[];
    dateFields?: string[];
  } = {}
): any => {
  let prepared = { ...obj };
  
  // Validate required fields
  if (options.requiredFields) {
    validateRequiredFields(prepared, options.requiredFields);
  }
  
  // Sanitize string fields
  if (options.stringFields) {
    prepared = sanitizeStringFields(prepared, options.stringFields);
  }
  
  // Convert dates to timestamps
  if (options.dateFields) {
    prepared = convertDatesToTimestamps(prepared, options.dateFields);
  }
  
  // Remove undefined fields
  prepared = removeUndefinedFields(prepared);
  
  return prepared;
}; 