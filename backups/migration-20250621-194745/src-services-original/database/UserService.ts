// User Service - Preparado para migração futura para Data Connect
import { 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  query, 
  where, 
  getDocs, 
  orderBy, 
  limit,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db, User, UserRole, UserStatus, COLLECTIONS } from '../../config/firebase';

export class UserService {
  private static instance: UserService;
  
  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  // Criar/atualizar usuário (equivalente ao UpsertUser do Data Connect)
  async upsertUser(userData: {
    id: string;
    email: string;
    username: string;
    displayName: string;
    firstName?: string;
    lastName?: string;
    avatarUrl?: string;
    bio?: string;
    location?: string;
  }): Promise<User> {
    const userRef = doc(db, COLLECTIONS.USERS, userData.id);
    const existingUser = await getDoc(userRef);
    
    const now = new Date();
    const userDoc: User = {
      ...userData,
      // Valores padrão compatíveis com Data Connect
      isActive: true,
      isVerified: false,
      emailVerified: false,
      role: UserRole.USER,
      status: UserStatus.ACTIVE,
      notificationsEnabled: true,
      publicProfile: true,
      showLocation: false,
      showEmail: false,
      allowMessages: true,
      createdAt: existingUser.exists() ? existingUser.data().createdAt : now,
      updatedAt: now,
      lastLoginAt: now,
      // Sobrescrever com dados fornecidos
      ...userData
    };

    await setDoc(userRef, {
      ...userDoc,
      createdAt: existingUser.exists() ? existingUser.data().createdAt : serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastLoginAt: serverTimestamp()
    });

    return userDoc;
  }

  // Buscar usuário atual (equivalente ao GetCurrentUser do Data Connect)
  async getCurrentUser(uid: string): Promise<User | null> {
    const userRef = doc(db, COLLECTIONS.USERS, uid);
    const userSnap = await getDoc(userRef);
    
    if (!userSnap.exists()) {
      return null;
    }

    const data = userSnap.data();
    return {
      ...data,
      createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate() : data.createdAt,
      updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate() : data.updatedAt,
      lastLoginAt: data.lastLoginAt instanceof Timestamp ? data.lastLoginAt.toDate() : data.lastLoginAt
    } as User;
  }

  // Buscar usuário por username (equivalente ao GetUserByUsername do Data Connect)
  async getUserByUsername(username: string): Promise<User | null> {
    const q = query(
      collection(db, COLLECTIONS.USERS),
      where('username', '==', username),
      limit(1)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }

    const doc = querySnapshot.docs[0];
    const data = doc.data();
    
    return {
      ...data,
      createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate() : data.createdAt,
      updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate() : data.updatedAt,
      lastLoginAt: data.lastLoginAt instanceof Timestamp ? data.lastLoginAt.toDate() : data.lastLoginAt
    } as User;
  }

  // Listar usuários (equivalente ao ListUsers do Data Connect)
  async listUsers(options: {
    limitCount?: number;
    onlyActive?: boolean;
    onlyPublic?: boolean;
  } = {}): Promise<User[]> {
    const { limitCount = 20, onlyActive = true, onlyPublic = true } = options;
    
    let q = query(
      collection(db, COLLECTIONS.USERS),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    if (onlyActive) {
      q = query(q, where('isActive', '==', true));
    }

    if (onlyPublic) {
      q = query(q, where('publicProfile', '==', true));
    }

    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        ...data,
        createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate() : data.updatedAt,
        lastLoginAt: data.lastLoginAt instanceof Timestamp ? data.lastLoginAt.toDate() : data.lastLoginAt
      } as User;
    });
  }

  // Atualizar configurações do usuário (equivalente ao UpdateUserSettings do Data Connect)
  async updateUserSettings(
    uid: string,
    settings: {
      notificationsEnabled?: boolean;
      publicProfile?: boolean;
      showLocation?: boolean;
      showEmail?: boolean;
      allowMessages?: boolean;
    }
  ): Promise<void> {
    const userRef = doc(db, COLLECTIONS.USERS, uid);
    
    await updateDoc(userRef, {
      ...settings,
      updatedAt: serverTimestamp()
    });
  }

  // Verificar se username está disponível
  async isUsernameAvailable(username: string, excludeUid?: string): Promise<boolean> {
    const q = query(
      collection(db, COLLECTIONS.USERS),
      where('username', '==', username)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return true;
    }

    // Se excluirUid for fornecido, verificar se é o mesmo usuário
    if (excludeUid) {
      const existingUser = querySnapshot.docs[0];
      return existingUser.id === excludeUid;
    }

    return false;
  }

  // Buscar usuários por termo de pesquisa
  async searchUsers(searchTerm: string, limitCount: number = 10): Promise<User[]> {
    // Firestore não tem busca full-text nativa, então fazemos busca por prefixo
    const q = query(
      collection(db, COLLECTIONS.USERS),
      where('username', '>=', searchTerm),
      where('username', '<=', searchTerm + '\uf8ff'),
      where('publicProfile', '==', true),
      where('isActive', '==', true),
      orderBy('username'),
      limit(limitCount)
    );

    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        ...data,
        createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate() : data.updatedAt,
        lastLoginAt: data.lastLoginAt instanceof Timestamp ? data.lastLoginAt.toDate() : data.lastLoginAt
      } as User;
    });
  }
}

// Export singleton instance
export const userService = UserService.getInstance(); 