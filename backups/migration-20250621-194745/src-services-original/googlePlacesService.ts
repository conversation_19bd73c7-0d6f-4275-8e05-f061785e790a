/**
 * Google Places Service
 * Integração com Google Places API para obter informações de localização
 */

export interface LocationData {
  formatted_address: string;
  city: string;
  state: string;
  country: string;
  country_code: string;
  latitude: number;
  longitude: number;
  place_id: string;
}

export interface PlacePrediction {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
}

class GooglePlacesService {
  private apiKey: string;
  private autocompleteService: google.maps.places.AutocompleteService | null = null;
  private placesService: google.maps.places.PlacesService | null = null;
  private geocoder: google.maps.Geocoder | null = null;
  private isApiBlocked: boolean = false;
  private lastError: string | null = null;

  constructor() {
    // Tenta usar a API key específica do Google Places, senão usa a do Firebase
    this.apiKey = import.meta.env.VITE_GOOGLE_PLACES_API_KEY || 
                  import.meta.env.VITE_FIREBASE_API_KEY || 
                  import.meta.env.VITE_GOOGLE_MAPS_API_KEY ||
                  'AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc'; // Fallback para a API key do Firebase
    
    if (!this.apiKey) {
      console.warn('🚨 Google Places API key not found. Location features will be limited.');
      this.isApiBlocked = true;
    } else {
      console.log('🗺️ Google Places API initialized with key:', this.apiKey.substring(0, 10) + '...');
    }

    // Escuta erros globais da API do Google Maps
    this.setupErrorHandling();
  }

  /**
   * Configura tratamento de erros da API do Google Maps
   */
  private setupErrorHandling(): void {
    // Intercepta erros do console relacionados ao Google Maps
    const originalConsoleError = console.error;
    console.error = (...args: any[]) => {
      const errorMessage = args.join(' ');
      
      if (errorMessage.includes('ApiTargetBlockedMapError') || 
          errorMessage.includes('Google Maps JavaScript API error')) {
        this.isApiBlocked = true;
        this.lastError = errorMessage;
        console.warn('🚨 Google Maps API blocked or misconfigured:', errorMessage);
        console.warn('💡 Check your API key configuration in Google Cloud Console');
      }
      
      originalConsoleError.apply(console, args);
    };

    // Escuta erros de window
    window.addEventListener('error', (event) => {
      if (event.message && event.message.includes('Google Maps')) {
        this.isApiBlocked = true;
        this.lastError = event.message;
      }
    });
  }

  /**
   * Inicializa os serviços do Google Maps
   */
  private async initializeServices(): Promise<void> {
    if (this.isApiBlocked) {
      throw new Error(`Google Maps API is blocked: ${this.lastError || 'Unknown error'}`);
    }

    if (!window.google) {
      await this.loadGoogleMapsScript();
    }

    if (!this.autocompleteService) {
      this.autocompleteService = new google.maps.places.AutocompleteService();
    }

    if (!this.placesService) {
      // Cria um elemento div temporário para o PlacesService
      const div = document.createElement('div');
      this.placesService = new google.maps.places.PlacesService(div);
    }

    if (!this.geocoder) {
      this.geocoder = new google.maps.Geocoder();
    }
  }

  /**
   * Carrega o script do Google Maps
   */
  private loadGoogleMapsScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (window.google) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${this.apiKey}&libraries=places`;
      script.async = true;
      script.defer = true;
      
      script.onload = () => {
        console.log('✅ Google Maps script loaded successfully');
        resolve();
      };
      
      script.onerror = (error) => {
        console.error('❌ Failed to load Google Maps script:', error);
        this.isApiBlocked = true;
        this.lastError = 'Failed to load Google Maps script';
        reject(new Error('Failed to load Google Maps script'));
      };
      
      document.head.appendChild(script);
    });
  }

  /**
   * Obtém sugestões de lugares baseado no texto digitado
   */
  async getPlacePredictions(input: string): Promise<PlacePrediction[]> {
    if (!input.trim() || input.length < 2) {
      return [];
    }

    try {
      await this.initializeServices();

      if (!this.autocompleteService) {
        throw new Error('Autocomplete service not available');
      }

      return new Promise((resolve, reject) => {
        this.autocompleteService!.getPlacePredictions(
          {
            input,
            types: ['(cities)'], // Foca em cidades
            componentRestrictions: { country: [] } // Permite todos os países
          },
          (predictions, status) => {
            if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
              const formattedPredictions: PlacePrediction[] = predictions.map(prediction => ({
                place_id: prediction.place_id!,
                description: prediction.description,
                structured_formatting: {
                  main_text: prediction.structured_formatting?.main_text || '',
                  secondary_text: prediction.structured_formatting?.secondary_text || ''
                }
              }));
              resolve(formattedPredictions);
            } else if (status === google.maps.places.PlacesServiceStatus.REQUEST_DENIED) {
              console.error('🚨 Google Places API request denied. Check your API key configuration.');
              this.isApiBlocked = true;
              this.lastError = 'REQUEST_DENIED';
              resolve([]);
            } else {
              console.warn('⚠️ Places API returned status:', status);
              resolve([]);
            }
          }
        );
      });
    } catch (error) {
      console.error('Error getting place predictions:', error);
      if (error instanceof Error && error.message.includes('blocked')) {
        this.isApiBlocked = true;
      }
      return [];
    }
  }

  /**
   * Obtém detalhes completos de um lugar pelo place_id
   */
  async getPlaceDetails(placeId: string): Promise<LocationData | null> {
    try {
      await this.initializeServices();

      if (!this.placesService) {
        throw new Error('Places service not available');
      }

      return new Promise((resolve, reject) => {
        this.placesService!.getDetails(
          {
            placeId,
            fields: ['formatted_address', 'address_components', 'geometry', 'place_id']
          },
          (place, status) => {
            if (status === google.maps.places.PlacesServiceStatus.OK && place) {
              const locationData = this.parseLocationData(place);
              resolve(locationData);
            } else if (status === google.maps.places.PlacesServiceStatus.REQUEST_DENIED) {
              console.error('🚨 Google Places API request denied for place details.');
              this.isApiBlocked = true;
              resolve(null);
            } else {
              console.warn('⚠️ Place details API returned status:', status);
              resolve(null);
            }
          }
        );
      });
    } catch (error) {
      console.error('Error getting place details:', error);
      return null;
    }
  }

  /**
   * Obtém localização atual do usuário usando geolocalização
   */
  async getCurrentLocation(): Promise<LocationData | null> {
    if (!navigator.geolocation) {
      throw new Error('Geolocation is not supported by this browser');
    }

    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutos
        });
      });

      const { latitude, longitude } = position.coords;
      return await this.reverseGeocode(latitude, longitude);
    } catch (error) {
      console.error('Error getting current location:', error);
      throw error;
    }
  }

  /**
   * Converte coordenadas em endereço (reverse geocoding)
   */
  async reverseGeocode(latitude: number, longitude: number): Promise<LocationData | null> {
    try {
      await this.initializeServices();

      if (!this.geocoder) {
        throw new Error('Geocoder service not available');
      }

      return new Promise((resolve, reject) => {
        this.geocoder!.geocode(
          { location: { lat: latitude, lng: longitude } },
          (results, status) => {
            if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
              const locationData = this.parseLocationData(results[0]);
              resolve(locationData);
            } else if (status === google.maps.GeocoderStatus.REQUEST_DENIED) {
              console.error('🚨 Google Geocoding API request denied.');
              this.isApiBlocked = true;
              resolve(null);
            } else {
              console.warn('⚠️ Geocoding API returned status:', status);
              resolve(null);
            }
          }
        );
      });
    } catch (error) {
      console.error('Error in reverse geocoding:', error);
      return null;
    }
  }

  /**
   * Extrai dados de localização de um resultado do Google Places/Geocoding
   */
  private parseLocationData(place: google.maps.places.PlaceResult | google.maps.GeocoderResult): LocationData {
    const addressComponents = place.address_components || [];
    
    let city = '';
    let state = '';
    let country = '';
    let country_code = '';

    // Extrai componentes do endereço
    addressComponents.forEach(component => {
      const types = component.types;
      
      if (types.includes('locality') || types.includes('administrative_area_level_2')) {
        city = component.long_name;
      } else if (types.includes('administrative_area_level_1')) {
        state = component.long_name;
      } else if (types.includes('country')) {
        country = component.long_name;
        country_code = component.short_name;
      }
    });

    // Se não encontrou cidade, tenta sublocality
    if (!city) {
      const sublocality = addressComponents.find(component => 
        component.types.includes('sublocality') || 
        component.types.includes('sublocality_level_1')
      );
      if (sublocality) {
        city = sublocality.long_name;
      }
    }

    const geometry = place.geometry;
    const location = geometry?.location;
    
    let latitude = 0;
    let longitude = 0;
    
    if (location) {
      // Usa any para evitar problemas de tipo com a API do Google Maps
      const loc = location as any;
      if (typeof loc.lat === 'function') {
        latitude = loc.lat();
        longitude = loc.lng();
      } else {
        latitude = loc.lat;
        longitude = loc.lng;
      }
    }

    return {
      formatted_address: place.formatted_address || '',
      city,
      state,
      country,
      country_code,
      latitude,
      longitude,
      place_id: place.place_id || ''
    };
  }

  /**
   * Formata dados de localização para exibição no perfil
   */
  formatLocationForProfile(locationData: LocationData): string {
    const parts = [];
    
    if (locationData.city) {
      parts.push(locationData.city);
    }
    
    if (locationData.state) {
      parts.push(locationData.state);
    }
    
    if (locationData.country) {
      parts.push(locationData.country);
    }
    
    return parts.join(', ');
  }

  /**
   * Verifica se o serviço está disponível
   */
  isAvailable(): boolean {
    if (this.isApiBlocked) {
      return false;
    }
    
    return !!(this.apiKey && this.apiKey !== 'YOUR_API_KEY_HERE');
  }

  /**
   * Obtém informações sobre o status da API
   */
  getStatus(): { available: boolean; error?: string; apiKey?: string } {
    return {
      available: this.isAvailable(),
      error: this.lastError || undefined,
      apiKey: this.apiKey ? this.apiKey.substring(0, 10) + '...' : undefined
    };
  }
}

// Exporta uma instância singleton
export const googlePlacesService = new GooglePlacesService();

// Tipos para TypeScript
declare global {
  interface Window {
    google: typeof google;
  }
} 