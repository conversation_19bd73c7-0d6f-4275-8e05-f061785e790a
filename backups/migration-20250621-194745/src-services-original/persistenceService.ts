/**
 * Persistence Service - Gerencia diferentes backends de armazenamento
 * Suporta localStorage, IndexedDB, Firebase, e futuramente outros backends
 */

export type StorageBackend = 'localStorage' | 'indexedDB' | 'firebase' | 'memory';

export interface PersistenceConfig {
  backend: StorageBackend;
  autoBackup?: boolean;
  syncInterval?: number;
  maxRetries?: number;
}

export interface StorageAdapter {
  get(key: string): Promise<any>;
  set(key: string, value: any): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
  keys(): Promise<string[]>;
}

class LocalStorageAdapter implements StorageAdapter {
  async get(key: string): Promise<any> {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('LocalStorage get error:', error);
      return null;
    }
  }

  async set(key: string, value: any): Promise<void> {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('LocalStorage set error:', error);
      throw error;
    }
  }

  async remove(key: string): Promise<void> {
    localStorage.removeItem(key);
  }

  async clear(): Promise<void> {
    localStorage.clear();
  }

  async keys(): Promise<string[]> {
    return Object.keys(localStorage);
  }
}

class IndexedDBAdapter implements StorageAdapter {
  private dbName = 'pinpal-project-db';
  private version = 1;
  private storeName = 'project-data';

  private async getDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.createObjectStore(this.storeName);
        }
      };
    });
  }

  async get(key: string): Promise<any> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.get(key);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
      });
    } catch (error) {
      console.error('IndexedDB get error:', error);
      return null;
    }
  }

  async set(key: string, value: any): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.put(value, key);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      console.error('IndexedDB set error:', error);
      throw error;
    }
  }

  async remove(key: string): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.delete(key);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      console.error('IndexedDB remove error:', error);
      throw error;
    }
  }

  async clear(): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.clear();
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      console.error('IndexedDB clear error:', error);
      throw error;
    }
  }

  async keys(): Promise<string[]> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.getAllKeys();
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result as string[]);
      });
    } catch (error) {
      console.error('IndexedDB keys error:', error);
      return [];
    }
  }
}

class MemoryAdapter implements StorageAdapter {
  private storage = new Map<string, any>();

  async get(key: string): Promise<any> {
    return this.storage.get(key) || null;
  }

  async set(key: string, value: any): Promise<void> {
    this.storage.set(key, value);
  }

  async remove(key: string): Promise<void> {
    this.storage.delete(key);
  }

  async clear(): Promise<void> {
    this.storage.clear();
  }

  async keys(): Promise<string[]> {
    return Array.from(this.storage.keys());
  }
}

export class PersistenceService {
  private adapter: StorageAdapter;
  private config: PersistenceConfig;

  constructor(config: PersistenceConfig = { backend: 'localStorage' }) {
    this.config = config;
    this.adapter = this.createAdapter(config.backend);
  }

  private createAdapter(backend: StorageBackend): StorageAdapter {
    switch (backend) {
      case 'localStorage':
        return new LocalStorageAdapter();
      case 'indexedDB':
        return new IndexedDBAdapter();
      case 'memory':
        return new MemoryAdapter();
      default:
        console.warn(`Backend ${backend} not implemented, falling back to localStorage`);
        return new LocalStorageAdapter();
    }
  }

  async get(key: string): Promise<any> {
    return this.adapter.get(key);
  }

  async set(key: string, value: any): Promise<void> {
    await this.adapter.set(key, value);
    
    if (this.config.autoBackup) {
      await this.createBackup(key, value);
    }
  }

  async remove(key: string): Promise<void> {
    return this.adapter.remove(key);
  }

  async clear(): Promise<void> {
    return this.adapter.clear();
  }

  async keys(): Promise<string[]> {
    return this.adapter.keys();
  }

  async exportData(): Promise<{ [key: string]: any }> {
    const keys = await this.keys();
    const data: { [key: string]: any } = {};
    
    for (const key of keys) {
      if (key.startsWith('pinpal-') || key.startsWith('ai-')) {
        data[key] = await this.get(key);
      }
    }
    
    return data;
  }

  async importData(data: { [key: string]: any }): Promise<void> {
    for (const [key, value] of Object.entries(data)) {
      await this.set(key, value);
    }
  }

  private async createBackup(key: string, value: any): Promise<void> {
    try {
      const backupKey = `backup_${key}_${Date.now()}`;
      await this.adapter.set(backupKey, {
        originalKey: key,
        data: value,
        timestamp: new Date().toISOString()
      });
      
      // Manter apenas os últimos 5 backups por chave
      await this.cleanupBackups(key);
    } catch (error) {
      console.error('Backup creation failed:', error);
    }
  }

  private async cleanupBackups(key: string): Promise<void> {
    try {
      const allKeys = await this.keys();
      const backupKeys = allKeys
        .filter(k => k.startsWith(`backup_${key}_`))
        .sort()
        .reverse();
      
      // Remover backups antigos (manter apenas os 5 mais recentes)
      for (let i = 5; i < backupKeys.length; i++) {
        await this.remove(backupKeys[i]);
      }
    } catch (error) {
      console.error('Backup cleanup failed:', error);
    }
  }

  async getBackups(key: string): Promise<Array<{ key: string; timestamp: string; data: any }>> {
    try {
      const allKeys = await this.keys();
      const backupKeys = allKeys.filter(k => k.startsWith(`backup_${key}_`));
      
      const backups = [];
      for (const backupKey of backupKeys) {
        const backup = await this.get(backupKey);
        if (backup) {
          backups.push({
            key: backupKey,
            timestamp: backup.timestamp,
            data: backup.data
          });
        }
      }
      
      return backups.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    } catch (error) {
      console.error('Get backups failed:', error);
      return [];
    }
  }

  async restoreFromBackup(backupKey: string): Promise<void> {
    try {
      const backup = await this.get(backupKey);
      if (backup && backup.originalKey && backup.data) {
        await this.set(backup.originalKey, backup.data);
        console.log(`Restored ${backup.originalKey} from backup ${backupKey}`);
      }
    } catch (error) {
      console.error('Restore from backup failed:', error);
      throw error;
    }
  }

  // Migrar dados entre backends
  async migrate(newBackend: StorageBackend): Promise<void> {
    try {
      console.log(`Migrating from ${this.config.backend} to ${newBackend}...`);
      
      // Exportar dados do backend atual
      const data = await this.exportData();
      
      // Criar novo adapter
      const newAdapter = this.createAdapter(newBackend);
      
      // Importar dados para o novo backend
      for (const [key, value] of Object.entries(data)) {
        await newAdapter.set(key, value);
      }
      
      // Atualizar configuração
      this.config.backend = newBackend;
      this.adapter = newAdapter;
      
      console.log(`Migration to ${newBackend} completed successfully`);
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }
}

// Singleton instance
export const persistenceService = new PersistenceService({
  backend: 'localStorage',
  autoBackup: true
});

export default persistenceService; 