import { Pin } from '@/types/pin';
import { useAuthStore } from '@/store/authStore';

export interface UserInterests {
  categories: Record<string, number>; // categoria -> score de interesse
  rarities: Record<string, number>;   // raridade -> score de preferência  
  series: Record<string, number>;     // série -> score de afinidade
  priceRanges: Record<string, number>; // faixa de preço -> score
  totalInteractions: number;
}

export interface PersonalizedFeedItem {
  pin: Pin;
  relevanceScore: number;
  reason: 'following_activity' | 'similar_to_saved' | 'category_match' | 'recommendation';
  socialProof?: {
    followedUsersWhoLiked: string[]; // IDs dos usuários seguidos que curtiram
    mutualConnections: number;
  };
  metadata: {
    userInterestMatch: number;
    socialSignals: number;
    behaviorPattern: number;
    freshness: number;
  };
}

export interface Recommendation {
  id: string;
  type: 'pin' | 'user' | 'category' | 'trade_opportunity';
  title: string;
  description: string;
  actionUrl: string;
  relevanceScore: number;
  reason: string;
  data?: any;
}

class PersonalizationService {
  private baseUrl = 'http://localhost:3001/api';

  /**
   * Analisa os interesses do usuário baseado no histórico de interações
   */
  async analyzeUserInterests(userId: string): Promise<UserInterests> {
    try {
      console.log('🔍 Analyzing user interests for:', userId);

      // Buscar pins salvos/curtidos pelo usuário
      const [savedPins, likedPins] = await Promise.all([
        this.getUserSavedPins(userId),
        this.getUserLikedPins(userId)
      ]);

      const allInteractions = [...savedPins, ...likedPins];
      console.log('📊 Total interactions found:', allInteractions.length);

      // Analisar padrões de interesse
      const interests: UserInterests = {
        categories: {},
        rarities: {},
        series: {},
        priceRanges: {},
        totalInteractions: allInteractions.length
      };

      allInteractions.forEach(pin => {
        // Categorias/Séries
        if (pin.series) {
          interests.categories[pin.series] = (interests.categories[pin.series] || 0) + 1;
          interests.series[pin.series] = (interests.series[pin.series] || 0) + 1;
        }

        // Raridades
        if (pin.rarity) {
          interests.rarities[pin.rarity] = (interests.rarities[pin.rarity] || 0) + 1;
        }

        // Faixas de preço (baseado no ano como proxy)
        const priceRange = this.getPriceRangeFromYear(pin.year);
        interests.priceRanges[priceRange] = (interests.priceRanges[priceRange] || 0) + 1;
      });

      // Normalizar scores (0-1)
      this.normalizeScores(interests.categories);
      this.normalizeScores(interests.rarities);
      this.normalizeScores(interests.series);
      this.normalizeScores(interests.priceRanges);

      console.log('✅ User interests analyzed:', interests);
      return interests;
    } catch (error) {
      console.error('❌ Error analyzing user interests:', error);
      throw new Error('Failed to analyze user interests');
    }
  }

  /**
   * Gera feed personalizado para o usuário
   */
  async getPersonalizedFeed(userId: string, limit: number = 20): Promise<PersonalizedFeedItem[]> {
    try {
      console.log('🎯 Generating personalized feed for:', userId);

      // Analisar interesses do usuário
      const userInterests = await this.analyzeUserInterests(userId);
      
      // Buscar pins de usuários seguidos
      const followingPins = await this.getFollowingActivityPins(userId);
      
      // Buscar pins similares aos salvos
      const similarPins = await this.getSimilarToSavedPins(userId, userInterests);
      
      // Buscar pins por categoria de interesse
      const categoryPins = await this.getCategoryMatchPins(userId, userInterests);

      // Combinar e pontuar todos os pins
      const allCandidates = [
        ...followingPins.map(pin => ({ pin, source: 'following_activity' as const })),
        ...similarPins.map(pin => ({ pin, source: 'similar_to_saved' as const })),
        ...categoryPins.map(pin => ({ pin, source: 'category_match' as const }))
      ];

      // Remover duplicatas
      const uniquePins = this.removeDuplicatePins(allCandidates);
      console.log('📦 Unique pins after deduplication:', uniquePins.length);

      // Calcular scores de personalização
      const scoredPins = await Promise.all(
        uniquePins.map(async ({ pin, source }) => {
          const score = await this.calculatePersonalizationScore(pin, userId, userInterests, source);
          return {
            pin,
            relevanceScore: score.total,
            reason: source,
            socialProof: await this.getSocialProof(pin, userId),
            metadata: score.breakdown
          } as PersonalizedFeedItem;
        })
      );

      // Ordenar por relevância e limitar
      const sortedFeed = scoredPins
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .slice(0, limit);

      console.log('🏆 Personalized feed generated:', sortedFeed.length, 'items');
      return sortedFeed;
    } catch (error) {
      console.error('❌ Error generating personalized feed:', error);
      throw new Error('Failed to generate personalized feed');
    }
  }

  /**
   * Gera recomendações inteligentes para o usuário
   */
  async getRecommendations(userId: string): Promise<Recommendation[]> {
    try {
      console.log('💡 Generating recommendations for:', userId);

      const userInterests = await this.analyzeUserInterests(userId);
      const recommendations: Recommendation[] = [];

      // Recomendação de categorias populares não exploradas
      const unexploredCategories = await this.getUnexploredCategories(userId, userInterests);
      if (unexploredCategories.length > 0) {
        recommendations.push({
          id: 'explore-new-categories',
          type: 'category',
          title: 'Discover New Categories',
          description: `Based on your interests, you might enjoy ${unexploredCategories[0]}`,
          actionUrl: `/explore?category=${unexploredCategories[0]}`,
          relevanceScore: 0.8,
          reason: 'Similar users also enjoy this category',
          data: { categories: unexploredCategories }
        });
      }

      // Recomendação de usuários para seguir
      const suggestedUsers = await this.getSuggestedUsers(userId);
      if (suggestedUsers.length > 0) {
        recommendations.push({
          id: 'follow-suggestions',
          type: 'user',
          title: 'Connect with Pin Collectors',
          description: `${suggestedUsers.length} collectors with similar interests`,
          actionUrl: '/explore?tab=users',
          relevanceScore: 0.7,
          reason: 'Users with similar pin collections',
          data: { users: suggestedUsers }
        });
      }

      // Recomendação de oportunidades de trade
      const tradeOpportunities = await this.getTradeOpportunities(userId, userInterests);
      if (tradeOpportunities.length > 0) {
        recommendations.push({
          id: 'trade-opportunities',
          type: 'trade_opportunity',
          title: 'Perfect Trade Matches',
          description: `${tradeOpportunities.length} pins you want are available for trade`,
          actionUrl: '/trade',
          relevanceScore: 0.9,
          reason: 'High-value trade opportunities',
          data: { opportunities: tradeOpportunities }
        });
      }

      console.log('✅ Recommendations generated:', recommendations.length);
      return recommendations.sort((a, b) => b.relevanceScore - a.relevanceScore);
    } catch (error) {
      console.error('❌ Error generating recommendations:', error);
      throw new Error('Failed to generate recommendations');
    }
  }

  // ========== MÉTODOS AUXILIARES ==========

  private async getUserSavedPins(userId: string): Promise<Pin[]> {
    try {
      const response = await fetch(`${this.baseUrl}/users/${userId}/saved-pins`);
      if (!response.ok) return [];
      return await response.json();
    } catch (error) {
      console.warn('Could not fetch saved pins:', error);
      return [];
    }
  }

  private async getUserLikedPins(userId: string): Promise<Pin[]> {
    try {
      const response = await fetch(`${this.baseUrl}/users/${userId}/liked-pins`);
      if (!response.ok) return [];
      return await response.json();
    } catch (error) {
      console.warn('Could not fetch liked pins:', error);
      return [];
    }
  }

  private async getFollowingActivityPins(userId: string): Promise<Pin[]> {
    try {
      // Buscar usuários seguidos
      const followingResponse = await fetch(`${this.baseUrl}/users/${userId}/following`);
      if (!followingResponse.ok) return [];
      
      const following = await followingResponse.json();
      if (following.length === 0) return [];

      // Buscar atividade recente dos usuários seguidos
      const followingIds = following.map((f: any) => f.followingId || f.id).slice(0, 10);
      const activityPins: Pin[] = [];

      for (const followingId of followingIds) {
        try {
          const pinsResponse = await fetch(`${this.baseUrl}/users/${followingId}/recent-pins?limit=5`);
          if (pinsResponse.ok) {
            const pins = await pinsResponse.json();
            activityPins.push(...pins);
          }
        } catch (error) {
          console.warn(`Could not fetch pins for user ${followingId}:`, error);
        }
      }

      return activityPins;
    } catch (error) {
      console.warn('Could not fetch following activity:', error);
      return [];
    }
  }

  private async getSimilarToSavedPins(userId: string, interests: UserInterests): Promise<Pin[]> {
    try {
      // Buscar pins das categorias/séries favoritas do usuário
      const topCategories = Object.entries(interests.categories)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([category]) => category);

      const similarPins: Pin[] = [];
      
      for (const category of topCategories) {
        try {
          const response = await fetch(`${this.baseUrl}/pins?series=${encodeURIComponent(category)}&limit=10&userId=${userId}`);
          if (response.ok) {
            const pins = await response.json();
            similarPins.push(...pins);
          }
        } catch (error) {
          console.warn(`Could not fetch pins for category ${category}:`, error);
        }
      }

      return similarPins;
    } catch (error) {
      console.warn('Could not fetch similar pins:', error);
      return [];
    }
  }

  private async getCategoryMatchPins(userId: string, interests: UserInterests): Promise<Pin[]> {
    try {
      // Buscar pins das categorias de interesse médio (para descoberta)
      const mediumInterestCategories = Object.entries(interests.categories)
        .filter(([, score]) => score >= 0.3 && score <= 0.7)
        .map(([category]) => category);

      const categoryPins: Pin[] = [];
      
      for (const category of mediumInterestCategories.slice(0, 2)) {
        try {
          const response = await fetch(`${this.baseUrl}/pins?series=${encodeURIComponent(category)}&limit=8&userId=${userId}`);
          if (response.ok) {
            const pins = await response.json();
            categoryPins.push(...pins);
          }
        } catch (error) {
          console.warn(`Could not fetch pins for category ${category}:`, error);
        }
      }

      return categoryPins;
    } catch (error) {
      console.warn('Could not fetch category match pins:', error);
      return [];
    }
  }

  private async calculatePersonalizationScore(
    pin: Pin, 
    userId: string, 
    interests: UserInterests, 
    source: string
  ): Promise<{ total: number; breakdown: any }> {
    // Calcular interesse do usuário (0-1)
    const userInterestMatch = this.calculateUserInterestMatch(pin, interests);
    
    // Calcular sinais sociais (0-1)
    const socialSignals = await this.calculateSocialSignals(pin, userId);
    
    // Calcular padrão comportamental (0-1)
    const behaviorPattern = this.calculateBehaviorPattern(pin, source);
    
    // Calcular frescor (0-1)
    const freshness = this.calculateFreshness(pin);

    // Aplicar pesos do algoritmo
    const total = (
      userInterestMatch * 0.4 +
      socialSignals * 0.3 +
      behaviorPattern * 0.2 +
      freshness * 0.1
    );

    return {
      total,
      breakdown: {
        userInterestMatch,
        socialSignals,
        behaviorPattern,
        freshness
      }
    };
  }

  private calculateUserInterestMatch(pin: Pin, interests: UserInterests): number {
    let score = 0;
    let factors = 0;

    // Similaridade de categoria/série
    if (pin.series && interests.series[pin.series]) {
      score += interests.series[pin.series];
      factors++;
    }

    // Preferência de raridade
    if (pin.rarity && interests.rarities[pin.rarity]) {
      score += interests.rarities[pin.rarity];
      factors++;
    }

    // Faixa de preço
    const priceRange = this.getPriceRangeFromYear(pin.year);
    if (interests.priceRanges[priceRange]) {
      score += interests.priceRanges[priceRange];
      factors++;
    }

    return factors > 0 ? score / factors : 0.1;
  }

  private async calculateSocialSignals(pin: Pin, userId: string): Promise<number> {
    try {
      // Verificar se usuários seguidos curtiram este pin
      const socialProof = await this.getSocialProof(pin, userId);
      
      if (socialProof && socialProof.followedUsersWhoLiked.length > 0) {
        // Quanto mais usuários seguidos curtiram, maior o sinal social
        return Math.min(socialProof.followedUsersWhoLiked.length * 0.3, 1.0);
      }

      // Sinal social baseado na popularidade geral
      const popularity = ((pin.likes || 0) + (pin.comments || 0) * 2) / 100; // Normalizar
      return Math.min(popularity, 0.5); // Máximo 0.5 para popularidade geral
    } catch (error) {
      return 0.1; // Score baixo se não conseguir calcular
    }
  }

  private calculateBehaviorPattern(pin: Pin, source: string): number {
    // Dar pontuação baseada na fonte
    switch (source) {
      case 'following_activity': return 0.9; // Alta relevância
      case 'similar_to_saved': return 0.8;   // Muito relevante
      case 'category_match': return 0.6;     // Moderadamente relevante
      default: return 0.4;                   // Baixa relevância
    }
  }

  private calculateFreshness(pin: Pin): number {
    if (!pin.createdAt) return 0.5;
    
    const now = Date.now();
    const pinTime = new Date(pin.createdAt).getTime();
    const ageHours = (now - pinTime) / (1000 * 60 * 60);
    
    // Pins mais frescos (últimas 24h) têm score maior
    if (ageHours <= 24) return 1.0;
    if (ageHours <= 48) return 0.8;
    if (ageHours <= 168) return 0.6; // 1 semana
    return 0.3;
  }

  private async getSocialProof(pin: Pin, userId: string): Promise<{ followedUsersWhoLiked: string[]; mutualConnections: number } | undefined> {
    try {
      // Buscar usuários seguidos que curtiram este pin
      const response = await fetch(`${this.baseUrl}/pins/${pin.id}/social-proof?userId=${userId}`);
      if (!response.ok) return undefined;
      
      return await response.json();
    } catch (error) {
      return undefined;
    }
  }

  private removeDuplicatePins(candidates: { pin: Pin; source: string }[]): { pin: Pin; source: string }[] {
    const seen = new Set<string>();
    return candidates.filter(({ pin }) => {
      if (seen.has(pin.id)) return false;
      seen.add(pin.id);
      return true;
    });
  }

  private normalizeScores(scores: Record<string, number>): void {
    const values = Object.values(scores);
    const max = Math.max(...values);
    
    if (max > 0) {
      Object.keys(scores).forEach(key => {
        scores[key] = scores[key] / max;
      });
    }
  }

  private getPriceRangeFromYear(year?: number): string {
    if (!year) return 'unknown';
    if (year >= 2020) return 'modern';
    if (year >= 2010) return 'recent';
    if (year >= 2000) return 'vintage';
    return 'classic';
  }

  private async getUnexploredCategories(userId: string, interests: UserInterests): Promise<string[]> {
    // Categorias populares que o usuário ainda não explorou
    const allCategories = ['Disney', 'Marvel', 'Star Wars', 'Pixar', 'Nintendo', 'Pokemon'];
    const exploredCategories = Object.keys(interests.categories);
    
    return allCategories.filter(cat => !exploredCategories.includes(cat));
  }

  private async getSuggestedUsers(userId: string): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/users/${userId}/suggested-follows?limit=5`);
      if (!response.ok) return [];
      return await response.json();
    } catch (error) {
      return [];
    }
  }

  private async getTradeOpportunities(userId: string, interests: UserInterests): Promise<any[]> {
    try {
      // Buscar oportunidades de trade baseadas nos interesses
      const topCategories = Object.keys(interests.categories).slice(0, 3);
      const response = await fetch(`${this.baseUrl}/trade/opportunities?userId=${userId}&categories=${topCategories.join(',')}`);
      if (!response.ok) return [];
      return await response.json();
    } catch (error) {
      return [];
    }
  }
}

export const personalizationService = new PersonalizationService(); 