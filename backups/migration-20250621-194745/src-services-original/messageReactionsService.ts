/**
 * Message Reactions Service
 * Handles API calls for message reactions (add/remove/get)
 */

const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export interface MessageReaction {
  emoji: string;
  users: string[];
  count: number;
}

export interface AddReactionResponse {
  success: boolean;
  reaction: {
    id: string;
    message_id: string;
    user_id: string;
    emoji: string;
    created_at: string;
  };
}

export interface RemoveReactionResponse {
  success: boolean;
  message: string;
}

export interface GetReactionsResponse {
  reactions: MessageReaction[];
}

class MessageReactionsService {
  /**
   * Add a reaction to a message
   */
  async addReaction(messageId: string, userId: string, emoji: string): Promise<AddReactionResponse> {
    console.log('🎯 MessageReactionsService.addReaction called:', { messageId, userId, emoji });
    
    try {
      const response = await fetch(`${BASE_URL}/api/messages/${messageId}/reactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          emoji,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add reaction');
      }

      const data = await response.json();
      console.log('✅ Reaction added successfully:', data);
      return data;
    } catch (error) {
      console.error('❌ Error adding reaction:', error);
      throw error;
    }
  }

  /**
   * Remove a reaction from a message
   */
  async removeReaction(messageId: string, userId: string, emoji: string): Promise<RemoveReactionResponse> {
    console.log('🎯 MessageReactionsService.removeReaction called:', { messageId, userId, emoji });
    
    try {
      const response = await fetch(`${BASE_URL}/api/messages/${messageId}/reactions`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          emoji,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove reaction');
      }

      const data = await response.json();
      console.log('✅ Reaction removed successfully:', data);
      return data;
    } catch (error) {
      console.error('❌ Error removing reaction:', error);
      throw error;
    }
  }

  /**
   * Get all reactions for a message
   */
  async getReactions(messageId: string): Promise<GetReactionsResponse> {
    console.log('🎯 MessageReactionsService.getReactions called:', { messageId });
    
    try {
      const response = await fetch(`${BASE_URL}/api/messages/${messageId}/reactions`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get reactions');
      }

      const data = await response.json();
      console.log('✅ Reactions fetched successfully:', data);
      return data;
    } catch (error) {
      console.error('❌ Error getting reactions:', error);
      throw error;
    }
  }
}

export const messageReactionsService = new MessageReactionsService(); 