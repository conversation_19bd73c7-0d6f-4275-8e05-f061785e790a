/**
 * Follow API Service - PostgreSQL Backend
 * Handles follow functionality via PostgreSQL APIs
 */

interface FollowResponse {
  success: boolean;
  follow?: {
    follower_id: string;
    followed_id: string;
    created_at: string;
  };
  message?: string;
}

interface FollowStatusResponse {
  isFollowing: boolean;
  follow: {
    follower_id: string;
    followed_id: string;
    created_at: string;
  } | null;
}

interface FollowUser {
  id: string;
  username: string;
  displayName: string;
  firstName: string | null;
  lastName: string | null;
  avatarUrl: string | null;
  followedAt: string;
}

interface FollowersResponse {
  followers: FollowUser[];
  total: number;
}

interface FollowingResponse {
  following: FollowUser[];
  total: number;
}

interface FollowStatsResponse {
  followersCount: number;
  followingCount: number;
}

class FollowApiService {
  private baseUrl = '/api';

  /**
   * Follow a user
   */
  async followUser(userId: string, followerId: string): Promise<FollowResponse> {
    try {
      console.log(`🔄 Following user ${userId} from ${followerId}`);
      
      const response = await fetch(`${this.baseUrl}/users/${userId}/follow`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ followerId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to follow user');
      }

      const result = await response.json();
      console.log(`✅ Successfully followed user ${userId}`);
      
      return result;
    } catch (error) {
      console.error('❌ Error following user:', error);
      throw error;
    }
  }

  /**
   * Unfollow a user
   */
  async unfollowUser(userId: string, followerId: string): Promise<FollowResponse> {
    try {
      console.log(`🔄 Unfollowing user ${userId} from ${followerId}`);
      
      const response = await fetch(`${this.baseUrl}/users/${userId}/follow`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ followerId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to unfollow user');
      }

      const result = await response.json();
      console.log(`✅ Successfully unfollowed user ${userId}`);
      
      return result;
    } catch (error) {
      console.error('❌ Error unfollowing user:', error);
      throw error;
    }
  }

  /**
   * Check if user A is following user B
   */
  async isFollowing(userId: string, followerId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/users/${userId}/follow/${followerId}`);

      if (!response.ok) {
        console.error('Failed to check follow status');
        return false;
      }

      const result: FollowStatusResponse = await response.json();
      return result.isFollowing;
    } catch (error) {
      console.error('❌ Error checking follow status:', error);
      return false;
    }
  }

  /**
   * Get follow status details
   */
  async getFollowStatus(userId: string, followerId: string): Promise<FollowStatusResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/users/${userId}/follow/${followerId}`);

      if (!response.ok) {
        throw new Error('Failed to get follow status');
      }

      return await response.json();
    } catch (error) {
      console.error('❌ Error getting follow status:', error);
      throw error;
    }
  }

  /**
   * Get followers of a user
   */
  async getFollowers(userId: string, limit = 50, offset = 0): Promise<FollowersResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/users/${userId}/followers?limit=${limit}&offset=${offset}`);

      if (!response.ok) {
        throw new Error('Failed to get followers');
      }

      return await response.json();
    } catch (error) {
      console.error('❌ Error getting followers:', error);
      throw error;
    }
  }

  /**
   * Get users that a user is following
   */
  async getFollowing(userId: string, limit = 50, offset = 0): Promise<FollowingResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/users/${userId}/following?limit=${limit}&offset=${offset}`);

      if (!response.ok) {
        throw new Error('Failed to get following');
      }

      return await response.json();
    } catch (error) {
      console.error('❌ Error getting following:', error);
      throw error;
    }
  }

  /**
   * Get follow statistics for a user
   */
  async getFollowStats(userId: string): Promise<FollowStatsResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/users/${userId}/follow-stats`);

      if (!response.ok) {
        throw new Error('Failed to get follow stats');
      }

      return await response.json();
    } catch (error) {
      console.error('❌ Error getting follow stats:', error);
      throw error;
    }
  }

  /**
   * Toggle follow status (follow if not following, unfollow if following)
   */
  async toggleFollow(userId: string, followerId: string): Promise<{ isFollowing: boolean; action: 'followed' | 'unfollowed' }> {
    try {
      // Check current status
      const isCurrentlyFollowing = await this.isFollowing(userId, followerId);
      
      if (isCurrentlyFollowing) {
        await this.unfollowUser(userId, followerId);
        return { isFollowing: false, action: 'unfollowed' };
      } else {
        await this.followUser(userId, followerId);
        return { isFollowing: true, action: 'followed' };
      }
    } catch (error) {
      console.error('❌ Error toggling follow:', error);
      throw error;
    }
  }
}

export const followApiService = new FollowApiService();
export type { FollowUser, FollowStatsResponse, FollowersResponse, FollowingResponse }; 