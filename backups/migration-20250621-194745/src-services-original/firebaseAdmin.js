import { initializeApp, cert, getApps } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';

// Configuração do Firebase Admin
const firebaseAdminConfig = {
  projectId: process.env.VITE_FIREBASE_PROJECT_ID || 'iconpal-cf925',
  // Para desenvolvimento, usar credenciais padrão do ambiente
  // Para produção, configure as credenciais adequadamente
};

let adminApp;
let adminAuth;
let adminFirestore;

// Inicializar Firebase Admin (apenas uma vez)
function initializeFirebaseAdmin() {
  if (getApps().length === 0) {
    try {
      // Em desenvolvimento, pode usar credenciais padrão
      // Em produção, configure as credenciais adequadamente
      adminApp = initializeApp(firebaseAdminConfig);
      adminAuth = getAuth(adminApp);
      adminFirestore = getFirestore(adminApp);
      
      console.log('✅ Firebase Admin SDK initialized');
    } catch (error) {
      console.error('❌ Error initializing Firebase Admin SDK:', error);
      throw error;
    }
  } else {
    adminApp = getApps()[0];
    adminAuth = getAuth(adminApp);
    adminFirestore = getFirestore(adminApp);
  }
}

// Serviços administrativos avançados
export class FirebaseAdminService {
  constructor() {
    initializeFirebaseAdmin();
  }

  // Deletar usuário do Firebase Auth (com privilégios admin)
  async deleteUserFromAuth(uid) {
    try {
      await adminAuth.deleteUser(uid);
      console.log('✅ User deleted from Firebase Auth via Admin SDK:', uid);
      return {
        success: true,
        message: 'User deleted from Firebase Auth successfully'
      };
    } catch (error) {
      console.error('❌ Error deleting user from Firebase Auth:', error);
      return {
        success: false,
        message: `Failed to delete from Firebase Auth: ${error.message}`
      };
    }
  }

  // Obter usuário do Firebase Auth
  async getUserFromAuth(uid) {
    try {
      const userRecord = await adminAuth.getUser(uid);
      return userRecord;
    } catch (error) {
      console.error('❌ Error getting user from Firebase Auth:', error);
      throw error;
    }
  }

  // Listar todos os usuários do Firebase Auth
  async listAllUsers(maxResults = 1000) {
    try {
      const listUsersResult = await adminAuth.listUsers(maxResults);
      return listUsersResult.users;
    } catch (error) {
      console.error('❌ Error listing users from Firebase Auth:', error);
      throw error;
    }
  }

  // Atualizar usuário no Firebase Auth
  async updateUserInAuth(uid, properties) {
    try {
      const userRecord = await adminAuth.updateUser(uid, properties);
      console.log('✅ User updated in Firebase Auth:', uid);
      return userRecord;
    } catch (error) {
      console.error('❌ Error updating user in Firebase Auth:', error);
      throw error;
    }
  }

  // Criar usuário no Firebase Auth (com privilégios admin)
  async createUserInAuth(userData) {
    try {
      const userRecord = await adminAuth.createUser(userData);
      console.log('✅ User created in Firebase Auth via Admin SDK:', userRecord.uid);
      return userRecord;
    } catch (error) {
      console.error('❌ Error creating user in Firebase Auth:', error);
      throw error;
    }
  }

  // Verificar se email existe no Firebase Auth
  async checkEmailExists(email) {
    try {
      await adminAuth.getUserByEmail(email);
      return true;
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        return false;
      }
      throw error;
    }
  }

  // Sincronizar usuários entre Auth e Firestore
  async syncAuthWithFirestore() {
    try {
      const authUsers = await this.listAllUsers();
      const firestoreUsers = await adminFirestore.collection('users').get();
      
      const authUIDs = new Set(authUsers.map(user => user.uid));
      const firestoreUIDs = new Set(firestoreUsers.docs.map(doc => doc.id));
      
      // Usuários órfãos no Auth (não estão no Firestore)
      const orphanedInAuth = authUsers.filter(user => !firestoreUIDs.has(user.uid));
      
      // Usuários órfãos no Firestore (não estão no Auth)
      const orphanedInFirestore = firestoreUsers.docs.filter(doc => !authUIDs.has(doc.id));
      
      return {
        authUsers: authUsers.length,
        firestoreUsers: firestoreUsers.size,
        orphanedInAuth: orphanedInAuth.length,
        orphanedInFirestore: orphanedInFirestore.length,
        orphanedAuthUsers: orphanedInAuth,
        orphanedFirestoreUsers: orphanedInFirestore
      };
    } catch (error) {
      console.error('❌ Error syncing Auth with Firestore:', error);
      throw error;
    }
  }

  // Limpar usuários órfãos
  async cleanupOrphanedUsers(deleteFromAuth = false, deleteFromFirestore = false) {
    try {
      const syncResult = await this.syncAuthWithFirestore();
      const results = {
        deletedFromAuth: 0,
        deletedFromFirestore: 0,
        errors: []
      };

      // Deletar órfãos do Auth
      if (deleteFromAuth && syncResult.orphanedAuthUsers) {
        for (const user of syncResult.orphanedAuthUsers) {
          try {
            await this.deleteUserFromAuth(user.uid);
            results.deletedFromAuth++;
          } catch (error) {
            results.errors.push(`Failed to delete ${user.uid} from Auth: ${error.message}`);
          }
        }
      }

      // Deletar órfãos do Firestore
      if (deleteFromFirestore && syncResult.orphanedFirestoreUsers) {
        for (const doc of syncResult.orphanedFirestoreUsers) {
          try {
            await doc.ref.delete();
            results.deletedFromFirestore++;
          } catch (error) {
            results.errors.push(`Failed to delete ${doc.id} from Firestore: ${error.message}`);
          }
        }
      }

      return results;
    } catch (error) {
      console.error('❌ Error cleaning up orphaned users:', error);
      throw error;
    }
  }
}

// Instância singleton
export const firebaseAdminService = new FirebaseAdminService(); 