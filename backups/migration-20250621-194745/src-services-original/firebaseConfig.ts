// Configuração centralizada do Firebase
// Usa projetos Firebase reais (desenvolvimento e produção)

import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';
import { getStorage } from 'firebase/storage';
import { getFirestore } from 'firebase/firestore';
import { getFirebaseEnvironment } from '@/config/devMode';
import { getFirebaseConfig as getProjectConfig } from '@/config/firebaseProjects';

// Obter configuração baseada no ambiente
const environment = getFirebaseEnvironment();
const firebaseConfig = getProjectConfig(environment);

// Estado da configuração
interface FirebaseConfigState {
  isInitialized: boolean;
  environment: 'development' | 'production';
  projectId: string;
}

const configState: FirebaseConfigState = {
  isInitialized: false,
  environment,
  projectId: firebaseConfig.projectId
};

// Initialize Firebase (with duplicate check)
import { getApp, getApps } from 'firebase/app';

let app;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApp();
}

// Firebase services
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();
export const storage = getStorage(app);
export const db = getFirestore(app);

// Função para inicializar configuração
const initializeFirebaseConfig = () => {
  if (configState.isInitialized) {
    return configState;
  }

  console.log('🔥 Firebase Configuration:', {
    environment: configState.environment,
    projectId: configState.projectId,
    isDevelopment: import.meta.env.DEV,
    mode: import.meta.env.MODE
  });

  if (configState.environment === 'development') {
    console.log('🛠️ Using Firebase DEVELOPMENT project');
    console.log('💡 Safe for development - separate from production data');
  } else {
    console.log('🚀 Using Firebase PRODUCTION project');
    console.log('⚠️ WARNING: You are using PRODUCTION data!');
  }

  configState.isInitialized = true;
  return configState;
};

// Funções utilitárias
export const getFirebaseConfig = () => {
  return initializeFirebaseConfig();
};

export const isUsingEmulators = () => {
  return false; // Nunca mais usar emuladores
};

export const isAuthEmulatorConnected = () => {
  return false;
};

export const isFirestoreEmulatorConnected = () => {
  return false;
};

export const isStorageEmulatorConnected = () => {
  return false;
};

export const getEmulatorUrls = () => {
  return {
    auth: null,
    firestore: null,
    storage: null,
    ui: null
  };
};

export const getConfigErrors = () => {
  return [];
};

export const getCurrentEnvironment = () => {
  return configState.environment;
};

export const getCurrentProjectId = () => {
  return configState.projectId;
};

// Função para reconfigurar (útil para testes)
export const reconfigureFirebase = () => {
  configState.isInitialized = false;
  return initializeFirebaseConfig();
};

// Inicializar automaticamente
initializeFirebaseConfig();

// Log de otimização de rede (apenas no browser)
if (typeof window !== 'undefined') {
  console.log('🔧 Firebase initialized successfully');
  console.log(`📊 Environment: ${configState.environment}`);
  console.log(`🆔 Project ID: ${configState.projectId}`);
} 