export interface PinReport {
  id: string;
  pinId: string;
  reportedBy: string;
  category: 'inappropriate' | 'spam' | 'copyright' | 'fake' | 'harassment' | 'other';
  reason: string;
  status: 'pending' | 'resolved' | 'dismissed';
  createdAt: string;
  resolvedAt?: string;
  resolvedBy?: string;
  adminNotes?: string;
}

export interface CreatePinReportData {
  reportedBy: string;
  category: PinReport['category'];
  reason: string;
}

class ReportsService {
  private baseUrl = 'http://localhost:3001/api';

  /**
   * Report a pin for inappropriate content or violations
   */
  async reportPin(pinId: string, data: CreatePinReportData): Promise<PinReport> {
    try {
      console.log('🚨 Reporting pin:', { pinId, ...data });

      const response = await fetch(`${this.baseUrl}/pins/${pinId}/report`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        
        if (response.status === 409) {
          throw new Error('You have already reported this pin');
        }
        
        throw new Error(errorData.error || 'Failed to submit report');
      }

      const report = await response.json();
      console.log('✅ Pin reported successfully:', report);
      
      return report;
    } catch (error) {
      console.error('❌ Error reporting pin:', error);
      throw error;
    }
  }

  /**
   * Get pin reports (admin only)
   */
  async getPinReports(params?: {
    status?: 'pending' | 'resolved' | 'dismissed';
    limit?: number;
    offset?: number;
  }): Promise<{
    reports: PinReport[];
    pagination: {
      limit: number;
      offset: number;
      total: number;
    };
  }> {
    try {
      const searchParams = new URLSearchParams();
      
      if (params?.status) searchParams.append('status', params.status);
      if (params?.limit) searchParams.append('limit', params.limit.toString());
      if (params?.offset) searchParams.append('offset', params.offset.toString());

      const response = await fetch(`${this.baseUrl}/admin/pins/reports?${searchParams}`);

      if (!response.ok) {
        throw new Error('Failed to fetch pin reports');
      }

      return await response.json();
    } catch (error) {
      console.error('❌ Error fetching pin reports:', error);
      throw error;
    }
  }

  /**
   * Resolve a pin report (admin only)
   */
  async resolvePinReport(
    reportId: string,
    data: {
      status: 'resolved' | 'dismissed';
      resolvedBy: string;
      adminNotes?: string;
    }
  ): Promise<PinReport> {
    try {
      const response = await fetch(`${this.baseUrl}/admin/pins/reports/${reportId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to resolve pin report');
      }

      return await response.json();
    } catch (error) {
      console.error('❌ Error resolving pin report:', error);
      throw error;
    }
  }
}

export const reportsService = new ReportsService(); 