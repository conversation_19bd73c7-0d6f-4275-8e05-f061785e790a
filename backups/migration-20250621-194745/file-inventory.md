## INVENTÁRIO DE ARQUIVOS PRÉ-MIGRAÇÃO
Criado em: Sat Jun 21 19:47:46 EDT 2025

### Rotas Backend
- admin.js:      884 linhas, 25K
- auth.js:      164 linhas, 4.2K
- boards.js:      168 linhas, 4.8K
- comments.js:      252 linhas, 7.2K
- feature-flags.js:      435 linhas, 12K
- index.js:       56 linhas, 1.7K
- messages.js:      467 linhas, 14K
- notifications.js:      321 linhas, 9.2K
- pins.js:      867 linhas, 25K
- trade.js:      105 linhas, 3.1K
- trading-points.js:      454 linhas, 12K
- users.js:     1005 linhas, 29K
- utility.js:      180 linhas, 4.7K

### Serviços Frontend
- schema.ts:      158 linhas, 3.3K
- utils.ts:       96 linhas, 2.3K
- UserService.ts:      220 linhas, 6.4K
- pushNotificationService.ts:      269 linhas, 8.1K
- usersApiService.ts:      162 linhas, 4.4K
- fileUploadService.ts:      226 linhas, 5.9K
- newPlacePhotosService.ts:      183 linhas, 5.1K
- tradingPointPhotoOptimizationService.ts:      426 linhas, 13K
- imageOptimizer.ts:      260 linhas, 7.4K
- locationPhotosService.ts:      221 linhas, 6.9K
- firebaseAdmin.ts:      198 linhas, 6.3K
- firebaseAnalytics.ts:      224 linhas, 6.9K
- googlePlacesService.ts:      408 linhas, 12K
- notificationService.ts:      362 linhas, 10K
- firebase.ts:       18 linhas, 435B
- backgroundRemovalService.ts:      345 linhas, 10K
- usernameService.ts:      294 linhas, 9.8K
- persistenceService.ts:      356 linhas, 9.8K
- commentsService.ts:      119 linhas, 2.3K
- tradingPointsService.ts:      116 linhas, 3.6K
- firebase-migration.ts:      382 linhas, 11K
- mcpPostgreSQLService.ts:      229 linhas, 6.4K
- types.ts:       15 linhas, 526B
- tradingPointsDataConnectService.ts:      621 linhas, 20K
- pinsDataConnectService.ts:      504 linhas, 15K
- boardsDataConnectService.ts:      363 linhas, 11K
- messagesDataConnectService.ts:      847 linhas, 27K
- index.ts:       20 linhas, 1.2K
- config.ts:       15 linhas, 646B
- usersDataConnectService.ts:      114 linhas, 3.1K
- messagesApiService.ts:      367 linhas, 11K
- adminPasswordService.ts:      448 linhas, 15K
- discoveryService.ts:      606 linhas, 19K
- api.ts:       29 linhas, 662B
- storageService.ts:      547 linhas, 17K
- followApiService.ts:      227 linhas, 5.8K
- tradingPointPhotoService.ts:      271 linhas, 8.4K
- followService.ts:      533 linhas, 16K
- websocketService.ts:      212 linhas, 6.0K
- authIntegrationService.ts:      221 linhas, 7.4K
- boardsService.ts:      330 linhas, 9.7K
- firebaseConfig.ts:      124 linhas, 3.2K
- simpleBgRemovalService.ts:      650 linhas, 20K
- googleAccountService.ts:      222 linhas, 6.9K
- messagingPollingService.ts:      155 linhas, 4.6K
- MigrationService.ts:      622 linhas, 18K
- photoEditorService.ts:      147 linhas, 3.6K
- usersApiService.ts:       82 linhas, 2.2K
- authApiService.ts:      105 linhas, 3.4K
- boardsApiService.ts:       85 linhas, 2.4K
- personalizationService.ts:      506 linhas, 17K
- usersService.ts:     1507 linhas, 48K
- checkInsService.ts:      345 linhas, 9.8K
- reportsService.ts:      124 linhas, 3.1K
- messageReactionsService.ts:      122 linhas, 3.2K
- pinsService.ts:      519 linhas, 16K
- favoritesService.ts:      341 linhas, 9.3K
- settingsService.ts:        0 linhas, 1B
- exploreService.ts:       96 linhas, 3.5K
