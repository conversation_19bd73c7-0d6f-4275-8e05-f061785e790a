/// <reference types="cypress" />

describe('Pin Creation Test', () => {
  beforeEach(() => {
    // Ignorar erros de aplicação que não afetam o teste
    cy.on('uncaught:exception', (err, runnable) => {
      if (err.message.includes('Failed to fetch dynamically imported module') ||
          err.message.includes('Loading chunk') ||
          err.message.includes('ChunkLoadError') ||
          err.message.includes('operation') ||
          err.message.includes('Failed to fetch')) {
        return false;
      }
      return true;
    });
  });

  it('should test pin creation functionality', () => {
    // Visitar a página inicial
    cy.visit('http://localhost:5773');
    cy.wait(3000);
    
    // Verificar se estamos na página de login ou se já estamos logados
    cy.get('body').then(($body) => {
      const bodyText = $body.text();
      
      if (bodyText.includes('Login') || bodyText.includes('Sign in')) {
        cy.log('📝 Página de login detectada');
        
        // Tentar fazer login (se houver campos disponíveis)
        if ($body.find('input[type="email"]').length > 0) {
          cy.get('input[type="email"]').type('<EMAIL>');
          
          if ($body.find('input[type="password"]').length > 0) {
            cy.get('input[type="password"]').type('password123');
            
            // Procurar botão de login
            const loginButton = $body.find('button').filter(':contains("Login")').first();
            if (loginButton.length > 0) {
              cy.wrap(loginButton).click();
              cy.wait(3000);
            }
          }
        }
      } else {
        cy.log('✅ Usuário já parece estar logado ou na página principal');
      }
    });
    
    // Tentar navegar para uma página de board ou pins
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);
    
    // Verificar se há botão Add Pin
    cy.get('body').then(($body) => {
      const bodyText = $body.text();
      cy.log('📄 Conteúdo da página my-pins:', bodyText.substring(0, 300));
      
      // Procurar botão Add Pin
      if (bodyText.includes('Add Pin') || bodyText.includes('Add')) {
        cy.log('🎯 Botão Add encontrado');
        
        // Tentar diferentes seletores para o botão
        const addButton = $body.find('button').filter((i, el) => {
          const text = Cypress.$(el).text();
          return text.includes('Add') || text.includes('Pin');
        }).first();
        
        if (addButton.length > 0) {
          cy.log('✅ Clicando no botão Add Pin');
          cy.wrap(addButton).click();
          cy.wait(2000);
          
          // Verificar se modal abriu
          cy.get('body').then(($modalBody) => {
            const modalText = $modalBody.text();
            
            if (modalText.includes('Title') || modalText.includes('Create') || modalText.includes('Pin')) {
              cy.log('🎉 Modal de criação abriu!');
              
              // Tentar preencher campos
              const titleInput = $modalBody.find('input').filter((i, el) => {
                const placeholder = Cypress.$(el).attr('placeholder') || '';
                const name = Cypress.$(el).attr('name') || '';
                return placeholder.toLowerCase().includes('title') || 
                       name.toLowerCase().includes('title');
              }).first();
              
              if (titleInput.length > 0) {
                cy.wrap(titleInput).clear().type('Pin de Teste Cypress');
                cy.log('✅ Campo Title preenchido');
                
                // Procurar campo de descrição
                const descInput = $modalBody.find('textarea, input').filter((i, el) => {
                  const placeholder = Cypress.$(el).attr('placeholder') || '';
                  const name = Cypress.$(el).attr('name') || '';
                  return placeholder.toLowerCase().includes('description') || 
                         name.toLowerCase().includes('description');
                }).first();
                
                if (descInput.length > 0) {
                  cy.wrap(descInput).clear().type('Descrição de teste criada pelo Cypress');
                  cy.log('✅ Campo Description preenchido');
                }
                
                // Procurar campo Origin/Store
                const originInput = $modalBody.find('input').filter((i, el) => {
                  const placeholder = Cypress.$(el).attr('placeholder') || '';
                  const name = Cypress.$(el).attr('name') || '';
                  return placeholder.toLowerCase().includes('origin') || 
                         placeholder.toLowerCase().includes('store') ||
                         name.toLowerCase().includes('origin');
                }).first();
                
                if (originInput.length > 0) {
                  cy.wrap(originInput).clear().type('Loja de Teste Cypress');
                  cy.log('✅ Campo Origin preenchido');
                }
                
                // Procurar campo Release Year
                const yearInput = $modalBody.find('input').filter((i, el) => {
                  const placeholder = Cypress.$(el).attr('placeholder') || '';
                  const name = Cypress.$(el).attr('name') || '';
                  const type = Cypress.$(el).attr('type') || '';
                  return placeholder.toLowerCase().includes('year') || 
                         name.toLowerCase().includes('year') ||
                         type === 'number';
                }).first();
                
                if (yearInput.length > 0) {
                  cy.wrap(yearInput).clear().type('2024');
                  cy.log('✅ Campo Release Year preenchido');
                }
                
                // Procurar campo Original Price
                const priceInput = $modalBody.find('input').filter((i, el) => {
                  const placeholder = Cypress.$(el).attr('placeholder') || '';
                  const name = Cypress.$(el).attr('name') || '';
                  return placeholder.toLowerCase().includes('price') || 
                         name.toLowerCase().includes('price');
                }).first();
                
                if (priceInput.length > 0) {
                  cy.wrap(priceInput).clear().type('15.99');
                  cy.log('✅ Campo Original Price preenchido');
                }
                
                // Procurar campo Pin Number
                const pinNumberInput = $modalBody.find('input').filter((i, el) => {
                  const placeholder = Cypress.$(el).attr('placeholder') || '';
                  const name = Cypress.$(el).attr('name') || '';
                  return placeholder.toLowerCase().includes('number') || 
                         name.toLowerCase().includes('number');
                }).first();
                
                if (pinNumberInput.length > 0) {
                  cy.wrap(pinNumberInput).clear().type('TEST-001');
                  cy.log('✅ Campo Pin Number preenchido');
                }
                
                cy.wait(1000);
                
                // Procurar botão de submit/create
                const submitButton = $modalBody.find('button').filter((i, el) => {
                  const text = Cypress.$(el).text();
                  return text.includes('Create') || text.includes('Save') || text.includes('Add');
                }).first();
                
                if (submitButton.length > 0) {
                  cy.log('🚀 Tentando submeter o formulário');
                  cy.wrap(submitButton).click();
                  cy.wait(3000);
                  
                  // Verificar se houve sucesso
                  cy.get('body').then(($finalBody) => {
                    const finalText = $finalBody.text();
                    if (finalText.includes('success') || finalText.includes('created') || 
                        finalText.includes('Pin de Teste Cypress')) {
                      cy.log('🎉 Pin criado com sucesso!');
                    } else {
                      cy.log('⚠️ Resultado da criação incerto');
                    }
                  });
                } else {
                  cy.log('⚠️ Botão de submit não encontrado');
                }
              } else {
                cy.log('⚠️ Campo Title não encontrado no modal');
              }
            } else {
              cy.log('⚠️ Modal não parece ter aberto corretamente');
            }
          });
        } else {
          cy.log('⚠️ Botão Add Pin não encontrado');
        }
      } else {
        cy.log('⚠️ Texto "Add Pin" não encontrado na página');
      }
    });
    
    // Capturar screenshot final
    cy.screenshot('pin-creation-test-final');
  });
}); 