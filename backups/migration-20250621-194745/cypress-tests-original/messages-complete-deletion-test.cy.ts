describe('Sistema de Mensagens - Exclusão Completa de Conversa', () => {
  const API_BASE = 'http://localhost:3001';
  const USER1_ID = 'test-123';
  const USER2_ID = 'admin-user-001';
  
  let testConversationId: string;
  const testMessageIds: string[] = [];

  it('deve permitir exclusão completa e permanente de conversa', () => {
    cy.log('🧪 Testando exclusão completa de conversa');
    
    // PASSO 1: Criar nova conversa
    cy.request('POST', `${API_BASE}/api/messages/conversations`, {
      participants: [USER1_ID, USER2_ID],
      type: 'DIRECT'
    }).then((response) => {
      expect(response.status).to.eq(201);
      testConversationId = response.body.id;
      cy.log(`✅ Conversa criada: ${testConversationId}`);
    });

    // PASSO 2: Enviar 3 mensagens na conversa
    cy.wrap([1, 2, 3]).each((messageNumber: number) => {
      cy.request('POST', `${API_BASE}/api/messages/send`, {
        conversationId: testConversationId,
        senderId: messageNumber % 2 === 1 ? USER1_ID : USER2_ID,
        content: `Mensagem ${messageNumber} para teste de exclusão`,
        contentType: 'TEXT'
      }).then((response) => {
        expect(response.status).to.eq(201);
        testMessageIds.push(response.body.id);
        cy.log(`✅ Mensagem ${messageNumber} enviada: ${response.body.id}`);
      });
    });

    // PASSO 3: Adicionar reações às mensagens
    cy.then(() => {
      cy.request('POST', `${API_BASE}/api/messages/${testMessageIds[0]}/reactions`, {
        userId: USER2_ID,
        emoji: '👍'
      }).then((response) => {
        expect(response.status).to.eq(201);
        cy.log('✅ Reação 👍 adicionada');
      });

      cy.request('POST', `${API_BASE}/api/messages/${testMessageIds[1]}/reactions`, {
        userId: USER1_ID,
        emoji: '❤️'
      }).then((response) => {
        expect(response.status).to.eq(201);
        cy.log('✅ Reação ❤️ adicionada');
      });
    });

    // PASSO 4: Verificar que a conversa existe
    cy.request('GET', `${API_BASE}/api/messages/conversations/user/${USER1_ID}`)
      .then((response) => {
        expect(response.status).to.eq(200);
        const conversations = response.body.conversations;
        const foundConversation = conversations.find((c: any) => c.id === testConversationId);
        
        expect(foundConversation).to.exist;
        cy.log(`✅ Conversa encontrada na lista do usuário`);
      });

    // PASSO 5: Verificar mensagens na conversa
    cy.request('GET', `${API_BASE}/api/messages/conversations/${testConversationId}/messages`)
      .then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.messages).to.have.length(3);
        cy.log(`✅ Encontradas ${response.body.messages.length} mensagens na conversa`);
      });

    // PASSO 6: EXCLUSÃO COMPLETA DA CONVERSA
    cy.log('🗑️ EXCLUINDO CONVERSA COMPLETAMENTE...');
    cy.request('DELETE', `${API_BASE}/api/messages/conversations/${testConversationId}/user/${USER1_ID}`)
      .then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.success).to.be.true;
        expect(response.body.message).to.contain('completely deleted');
        expect(response.body.deletedMessages).to.eq(3);
        cy.log(`✅ Conversa completamente excluída! Mensagens deletadas: ${response.body.deletedMessages}`);
      });

    // PASSO 7: Verificar que a conversa não existe mais
    cy.log('🔍 Verificando exclusão completa...');
    
    // Tentar buscar mensagens da conversa excluída (deve falhar)
    cy.request({
      method: 'GET',
      url: `${API_BASE}/api/messages/conversations/${testConversationId}/messages`,
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.be.oneOf([404, 500]);
      cy.log('✅ Conversa não encontrada (exclusão confirmada)');
    });

    // Verificar se sumiu da lista de conversas do usuário
    cy.request('GET', `${API_BASE}/api/messages/conversations/user/${USER1_ID}`)
      .then((response) => {
        expect(response.status).to.eq(200);
        const conversations = response.body.conversations;
        const stillExists = conversations.find((c: any) => c.id === testConversationId);
        
        expect(stillExists).to.not.exist;
        cy.log('✅ Conversa removida da lista do usuário');
      });

    // PASSO 8: Verificar que reações também foram excluídas
    cy.then(() => {
      testMessageIds.forEach((messageId) => {
        cy.request({
          method: 'GET',
          url: `${API_BASE}/api/messages/${messageId}/reactions`,
          failOnStatusCode: false
        }).then((response) => {
          // Como a mensagem foi excluída, as reações também devem ter sido
          expect(response.status).to.be.oneOf([404, 500]);
        });
      });
      cy.log('✅ Reações também foram excluídas junto com as mensagens');
    });

    cy.log('🎯 TESTE CONCLUÍDO: Exclusão de conversa é COMPLETA e PERMANENTE!');
  });

  it('deve validar que apenas participantes podem excluir conversa', () => {
    cy.log('🔒 Testando segurança: apenas participantes podem excluir');
    
    // Criar conversa de teste
    cy.request('POST', `${API_BASE}/api/messages/conversations`, {
      participants: [USER1_ID, USER2_ID],
      type: 'DIRECT'
    }).then((response) => {
      const conversationId = response.body.id;
      
      // Tentar excluir com usuário que não participa
      cy.request({
        method: 'DELETE',
        url: `${API_BASE}/api/messages/conversations/${conversationId}/user/fake-user-id`,
        failOnStatusCode: false
      }).then((response) => {
        expect(response.status).to.eq(404);
        expect(response.body.error).to.contain('participant not found');
        cy.log('✅ Usuário não participante não pode excluir conversa');
      });
      
      // Limpar: excluir conversa com usuário válido
      cy.request('DELETE', `${API_BASE}/api/messages/conversations/${conversationId}/user/${USER1_ID}`);
    });
  });

  it('deve executar teste Node.js completo do sistema', () => {
    cy.log('🧪 Executando teste Node.js completo...');
    
    cy.exec('node scripts/test-conversation-deletion.js', { timeout: 30000 })
      .then((result) => {
        expect(result.code).to.eq(0);
        expect(result.stdout).to.contain('Demonstração concluída com sucesso');
        expect(result.stdout).to.contain('CONVERSA COMPLETAMENTE EXCLUÍDA');
        expect(result.stdout).to.contain('Mensagens deletadas:');
        cy.log('✅ Teste Node.js executado com sucesso');
      });
  });

  it('deve verificar responsividade da interface de mensagens', () => {
    cy.log('📱 Testando responsividade...');
    
    // Testar em diferentes viewports
    const viewports = [
      { width: 375, height: 667, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1920, height: 1080, name: 'Desktop' }
    ];

    viewports.forEach((viewport) => {
      cy.viewport(viewport.width, viewport.height);
      cy.visit('/messages', { failOnStatusCode: false });
      cy.log(`✅ Interface testada em ${viewport.name} (${viewport.width}x${viewport.height})`);
    });
  });
}); 