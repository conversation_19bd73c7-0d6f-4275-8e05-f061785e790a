/// <reference types="cypress" />

describe('Sistema de Mensagens - Teste Completo', () => {
  const testUsers = {
    user1: {
      email: '<EMAIL>',
      password: 'Test123!',
      username: 'testuser1',
      displayName: 'Test User 1'
    },
    user2: {
      email: '<EMAIL>', 
      password: 'Test123!',
      username: 'testuser2',
      displayName: 'Test User 2'
    }
  };

  beforeEach(() => {
    // Interceptar APIs de mensagens
    cy.intercept('GET', '/api/messages/conversations/user/*').as('getConversations');
    cy.intercept('POST', '/api/messages/send').as('sendMessage');
    cy.intercept('DELETE', '/api/messages/*').as('deleteMessage');
    cy.intercept('POST', '/api/messages/*/reactions').as('addReaction');
    cy.intercept('DELETE', '/api/messages/*/reactions').as('removeReaction');
    cy.intercept('GET', '/api/messages/*/reactions').as('getReactions');
    cy.intercept('POST', '/api/messages/*/replies').as('sendReply');
    cy.intercept('GET', '/api/messages/*/replies').as('getReplies');

    // Visitar página de mensagens
    cy.visit('/messages');
    
    // Aguardar carregamento
    cy.wait(1000);
  });

  describe('1. Navegação e Interface Básica', () => {
    it('deve carregar a página de mensagens corretamente', () => {
      cy.url().should('include', '/messages');
      
      // Verificar elementos principais da interface
      cy.get('[data-testid="messages-container"]', { timeout: 10000 })
        .should('be.visible')
        .or('get')
        .contains('Messages', { timeout: 5000 })
        .should('be.visible');
        
      // Verificar se existe área de conversas ou estado vazio
      cy.get('body').then(($body) => {
        if ($body.find('[data-testid="conversation-list"]').length > 0) {
          cy.get('[data-testid="conversation-list"]').should('be.visible');
        } else {
          // Estado vazio é aceitável
          cy.contains('No conversations', { timeout: 5000 }).should('be.visible')
            .or('contains')
            .text('Start a conversation');
        }
      });
    });

    it('deve mostrar interface responsiva', () => {
      // Testar em diferentes viewports
      cy.viewport(375, 667); // Mobile
      cy.get('[data-testid="messages-container"]').should('be.visible');
      
      cy.viewport(768, 1024); // Tablet  
      cy.get('[data-testid="messages-container"]').should('be.visible');
      
      cy.viewport(1280, 720); // Desktop
      cy.get('[data-testid="messages-container"]').should('be.visible');
    });
  });

  describe('2. Funcionalidades de Conversa', () => {
    it('deve permitir iniciar nova conversa', () => {
      // Procurar botão de nova conversa
      cy.get('body').then(($body) => {
        if ($body.find('[data-testid="new-conversation-btn"]').length > 0) {
          cy.get('[data-testid="new-conversation-btn"]').click();
        } else if ($body.find('button').filter(':contains("New"), :contains("Start"), :contains("Conversation")').length > 0) {
          cy.get('button').filter(':contains("New"), :contains("Start"), :contains("Conversation")').first().click();
        } else {
          // Se não encontrar botão específico, pode ser que já esteja na conversa
          cy.log('Botão de nova conversa não encontrado - possível interface simplificada');
        }
      });
    });

    it('deve carregar conversas existentes', () => {
      cy.wait('@getConversations', { timeout: 10000 }).then((interception) => {
        expect(interception.response?.statusCode).to.be.oneOf([200, 404]);
        
        if (interception.response?.statusCode === 200) {
          const conversations = interception.response.body.conversations;
          cy.log(`Conversas encontradas: ${conversations.length}`);
          
          if (conversations.length > 0) {
            // Verificar se as conversas são exibidas
            cy.get('[data-testid="conversation-item"]').should('have.length.gte', 1)
              .or('get')
              .contains(conversations[0].title || 'Direct Message');
          }
        }
      });
    });
  });

  describe('3. Envio e Recebimento de Mensagens', () => {
    it('deve permitir enviar mensagem de texto', () => {
      const messageText = `Mensagem de teste Cypress - ${new Date().toLocaleTimeString()}`;
      
      // Procurar campo de input de mensagem
      cy.get('[data-testid="message-input"]', { timeout: 10000 })
        .should('be.visible')
        .type(messageText)
        .or('get')
        .get('textarea[placeholder*="message"], input[placeholder*="message"]')
        .should('be.visible')
        .type(messageText);
        
      // Procurar botão de envio
      cy.get('[data-testid="send-button"]')
        .should('be.enabled')
        .click()
        .or('get')
        .get('button').filter(':contains("Send"), :contains("Enviar")')
        .should('be.enabled')  
        .click();
        
      // Verificar se mensagem foi enviada
      cy.wait('@sendMessage', { timeout: 10000 }).then((interception) => {
        expect(interception.response?.statusCode).to.equal(201);
        cy.log('Mensagem enviada com sucesso');
      });
      
      // Verificar se mensagem aparece na interface
      cy.contains(messageText, { timeout: 10000 }).should('be.visible');
    });

    it('deve permitir enviar mensagem com anexo', () => {
      // Procurar botão de anexo
      cy.get('[data-testid="attachment-button"]', { timeout: 5000 })
        .should('be.visible')
        .or('get')
        .get('button').filter(':contains("📎"), [aria-label*="attach"]')
        .should('be.visible');
        
      // Simular upload de arquivo (se disponível)
      cy.get('input[type="file"]').then(($input) => {
        if ($input.length > 0) {
          const fileName = 'test-image.jpg';
          cy.fixture('images/test-image.jpg', 'base64').then(fileContent => {
            cy.get('input[type="file"]').selectFile({
              contents: Cypress.Buffer.from(fileContent, 'base64'),
              fileName,
              mimeType: 'image/jpeg'
            }, { force: true });
          });
        }
      });
    });

    it('deve mostrar indicador de digitação', () => {
      // Começar a digitar
      cy.get('[data-testid="message-input"]')
        .type('Teste de digitação...')
        .or('get')
        .get('textarea[placeholder*="message"]')
        .type('Teste de digitação...');
        
      // Verificar indicador de digitação (se implementado)
      cy.get('[data-testid="typing-indicator"]', { timeout: 3000 })
        .should('be.visible')
        .or('contains')
        .text('typing');
    });
  });

  describe('4. Exclusão de Mensagens', () => {
    it('deve permitir excluir própria mensagem', () => {
      // Primeiro enviar uma mensagem
      const messageText = `Mensagem para deletar - ${Date.now()}`;
      
      cy.get('[data-testid="message-input"]')
        .type(messageText)
        .or('get')
        .get('textarea[placeholder*="message"]')
        .type(messageText);
        
      cy.get('[data-testid="send-button"]').click()
        .or('get')
        .get('button').filter(':contains("Send")').click();
        
      // Aguardar mensagem aparecer
      cy.contains(messageText, { timeout: 10000 }).should('be.visible');
      
      // Procurar opção de delete (long press ou menu)
      cy.contains(messageText)
        .parent()
        .within(() => {
          // Tentar menu de contexto
          cy.get('[data-testid="message-menu"]').click({ force: true })
            .or('get')
            .get('button').filter(':contains("⋯"), [aria-label*="more"]').click({ force: true })
            .or('trigger')
            .trigger('contextmenu');
        });
        
      // Procurar opção de delete
      cy.get('[data-testid="delete-message"]', { timeout: 5000 })
        .click()
        .or('get')
        .contains('Delete').click()
        .or('get')
        .contains('🗑️').click();
        
      // Confirmar exclusão se necessário
      cy.get('[data-testid="confirm-delete"]', { timeout: 5000 })
        .click()
        .or('get')
        .contains('Confirm').click()
        .or('get')
        .contains('Delete').click();
        
      // Verificar se requisição foi feita
      cy.wait('@deleteMessage', { timeout: 10000 }).then((interception) => {
        expect(interception.response?.statusCode).to.equal(200);
      });
    });

    it('deve mostrar modal de confirmação para exclusão', () => {
      // Implementar teste de modal se aplicável
      cy.log('Teste de modal de confirmação - a ser implementado baseado na UI');
    });
  });

  describe('5. Reações às Mensagens', () => {
    it('deve permitir adicionar reação', () => {
      // Encontrar uma mensagem existente
      cy.get('[data-testid="message-item"]', { timeout: 10000 })
        .first()
        .within(() => {
          // Procurar botão de reação
          cy.get('[data-testid="add-reaction"]').click({ force: true })
            .or('get')
            .get('button').filter(':contains("😀"), :contains("+"), [aria-label*="react"]')
            .click({ force: true });
        })
        .or('get')
        .get('[data-testid="message-bubble"]')
        .first()
        .trigger('mouseover')
        .within(() => {
          cy.get('[data-testid="reaction-button"]').click({ force: true });
        });
        
      // Selecionar emoji
      cy.get('[data-testid="emoji-picker"]', { timeout: 5000 })
        .should('be.visible')
        .within(() => {
          cy.contains('👍').click();
        })
        .or('get')
        .contains('👍').click();
        
      // Verificar se reação foi adicionada
      cy.wait('@addReaction', { timeout: 10000 }).then((interception) => {
        expect(interception.response?.statusCode).to.equal(201);
      });
      
      // Verificar se reação aparece na mensagem
      cy.get('[data-testid="message-reactions"]', { timeout: 5000 })
        .should('contain', '👍')
        .or('contains')
        .text('👍').should('be.visible');
    });

    it('deve permitir remover reação', () => {
      // Encontrar mensagem com reação
      cy.get('[data-testid="message-reactions"]', { timeout: 10000 })
        .contains('👍')
        .click()
        .or('get')
        .get('[data-testid="reaction-button"]')
        .contains('👍')
        .click();
        
      // Verificar se reação foi removida
      cy.wait('@removeReaction', { timeout: 10000 }).then((interception) => {
        expect(interception.response?.statusCode).to.equal(200);
      });
    });

    it('deve mostrar contagem de reações', () => {
      cy.get('[data-testid="reaction-count"]', { timeout: 5000 })
        .should('be.visible')
        .and('contain.text', /\d+/)
        .or('get')
        .get('[data-testid="message-reactions"]')
        .should('contain.text', /\d+/);
    });
  });

  describe('6. Respostas (Replies)', () => {
    it('deve permitir responder a uma mensagem', () => {
      const replyText = `Resposta de teste - ${Date.now()}`;
      
      // Encontrar mensagem para responder
      cy.get('[data-testid="message-item"]', { timeout: 10000 })
        .first()
        .within(() => {
          cy.get('[data-testid="reply-button"]').click({ force: true })
            .or('get')
            .get('button').filter(':contains("Reply"), [aria-label*="reply"]')
            .click({ force: true });
        });
        
      // Digitar resposta
      cy.get('[data-testid="reply-input"]', { timeout: 5000 })
        .type(replyText)
        .or('get')
        .get('textarea[placeholder*="reply"]')
        .type(replyText);
        
      // Enviar resposta
      cy.get('[data-testid="send-reply"]').click()
        .or('get')
        .contains('Send Reply').click();
        
      // Verificar se resposta foi enviada
      cy.wait('@sendReply', { timeout: 10000 }).then((interception) => {
        expect(interception.response?.statusCode).to.equal(201);
      });
      
      // Verificar se resposta aparece
      cy.contains(replyText, { timeout: 10000 }).should('be.visible');
    });

    it('deve mostrar thread de respostas', () => {
      // Verificar se thread está visível
      cy.get('[data-testid="reply-thread"]', { timeout: 10000 })
        .should('be.visible')
        .or('get')
        .get('[data-testid="message-replies"]')
        .should('be.visible');
        
      // Verificar contador de respostas
      cy.get('[data-testid="reply-count"]', { timeout: 5000 })
        .should('contain.text', /\d+ repl/)
        .or('contains')
        .text(/\d+ response/);
    });

    it('deve permitir resposta aninhada', () => {
      const nestedReplyText = `Resposta aninhada - ${Date.now()}`;
      
      // Encontrar uma resposta existente
      cy.get('[data-testid="reply-item"]', { timeout: 10000 })
        .first()
        .within(() => {
          cy.get('[data-testid="reply-to-reply"]').click({ force: true });
        });
        
      // Digitar resposta aninhada
      cy.get('[data-testid="nested-reply-input"]')
        .type(nestedReplyText);
        
      // Enviar
      cy.get('[data-testid="send-nested-reply"]').click();
      
      // Verificar
      cy.contains(nestedReplyText, { timeout: 10000 }).should('be.visible');
    });
  });

  describe('7. Funcionalidades de Conversa', () => {
    it('deve permitir excluir conversa', () => {
      // Encontrar menu da conversa
      cy.get('[data-testid="conversation-menu"]', { timeout: 10000 })
        .click()
        .or('get')
        .get('[data-testid="conversation-item"]')
        .first()
        .within(() => {
          cy.get('button').filter(':contains("⋯")').click({ force: true });
        });
        
      // Opção de deletar
      cy.contains('Delete Conversation').click()
        .or('contains')
        .text('Leave').click();
        
      // Confirmar
      cy.get('[data-testid="confirm-delete-conversation"]', { timeout: 5000 })
        .click()
        .or('contains')
        .text('Confirm').click();
    });

    it('deve buscar mensagens', () => {
      // Abrir busca
      cy.get('[data-testid="search-messages"]', { timeout: 10000 })
        .click()
        .or('get')
        .get('input[placeholder*="search"]')
        .should('be.visible');
        
      // Digitar termo de busca
      cy.get('[data-testid="search-input"]')
        .type('teste')
        .or('get')
        .get('input[placeholder*="search"]')
        .type('teste');
        
      // Verificar resultados
      cy.get('[data-testid="search-results"]', { timeout: 5000 })
        .should('be.visible')
        .or('get')
        .contains('Results').should('be.visible');
    });

    it('deve marcar mensagens como lidas', () => {
      // Verificar contador de não lidas
      cy.get('[data-testid="unread-count"]', { timeout: 5000 })
        .should('be.visible')
        .or('get')
        .get('[data-testid="unread-indicator"]')
        .should('be.visible');
        
      // Abrir conversa deve marcar como lida
      cy.get('[data-testid="conversation-item"]', { timeout: 10000 })
        .first()
        .click();
        
      // Verificar se foi marcada como lida
      cy.wait(2000);
      cy.get('[data-testid="unread-indicator"]', { timeout: 5000 })
        .should('not.exist')
        .or('not.be.visible');
    });
  });

  describe('8. Estados de Error e Loading', () => {
    it('deve tratar erro de conexão', () => {
      // Simular erro de rede
      cy.intercept('GET', '/api/messages/**', { forceNetworkError: true }).as('networkError');
      
      cy.reload();
      
      // Verificar mensagem de erro
      cy.contains('Connection error', { timeout: 10000 })
        .should('be.visible')
        .or('contains')
        .text('Failed to load').should('be.visible');
    });

    it('deve mostrar loading states', () => {
      // Verificar indicadores de loading
      cy.get('[data-testid="loading-messages"]', { timeout: 5000 })
        .should('be.visible')
        .or('get')
        .get('.loading, .spinner')
        .should('be.visible');
    });

    it('deve tratar mensagem muito longa', () => {
      const longMessage = 'A'.repeat(1000);
      
      cy.get('[data-testid="message-input"]')
        .type(longMessage)
        .or('get')
        .get('textarea[placeholder*="message"]')
        .type(longMessage);
        
      // Verificar limite ou aviso
      cy.get('[data-testid="character-limit"]', { timeout: 3000 })
        .should('be.visible')
        .or('get')
        .contains('too long').should('be.visible');
    });
  });

  describe('9. Performance e UX', () => {
    it('deve carregar rapidamente', () => {
      const startTime = Date.now();
      
      cy.visit('/messages');
      cy.get('[data-testid="messages-container"]', { timeout: 5000 })
        .should('be.visible')
        .then(() => {
          const loadTime = Date.now() - startTime;
          expect(loadTime).to.be.lessThan(5000); // 5 segundos
          cy.log(`Tempo de carregamento: ${loadTime}ms`);
        });
    });

    it('deve manter scroll position', () => {
      // Scroll para baixo
      cy.get('[data-testid="messages-list"]', { timeout: 10000 })
        .scrollTo('bottom')
        .or('get')
        .window().scrollTo('bottom');
        
      // Enviar nova mensagem
      cy.get('[data-testid="message-input"]')
        .type('Nova mensagem{enter}');
        
      // Verificar se scroll manteve posição ou foi para nova mensagem
      cy.get('[data-testid="messages-list"]')
        .should('be.visible');
    });

    it('deve funcionar offline', () => {
      // Simular offline
      cy.log('Simulando modo offline...');
      
      // Tentar enviar mensagem offline
      cy.get('[data-testid="message-input"]')
        .type('Mensagem offline{enter}');
        
      // Verificar indicador offline
      cy.contains('Offline', { timeout: 5000 })
        .should('be.visible')
        .or('get')
        .get('[data-testid="offline-indicator"]')
        .should('be.visible');
    });
  });

  after(() => {
    // Cleanup se necessário
    cy.log('Testes de mensagens concluídos');
  });
}); 