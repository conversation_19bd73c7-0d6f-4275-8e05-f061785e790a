/// <reference types="cypress" />

describe('Board Actions Menu - Simple Test', () => {
  beforeEach(() => {
    // Ignorar erros de aplicação
    cy.on('uncaught:exception', (err, runnable) => {
      if (err.message.includes('Failed to fetch dynamically imported module') ||
          err.message.includes('Loading chunk') ||
          err.message.includes('ChunkLoadError') ||
          err.message.includes('operation') ||
          err.message.includes('Failed to fetch')) {
        return false;
      }
      return true;
    });

    // Visitar a página principal
    cy.visit('http://localhost:5773');
    cy.wait(3000);
  });

  it('should test board card component structure', () => {
    cy.get('body').should('be.visible');
    cy.screenshot('homepage-loaded');
    
    // Procurar por qualquer elemento que possa ser um board card
    cy.get('div').then(($divs) => {
      cy.log(`Found ${$divs.length} div elements on page`);
      
      // Procurar por elementos com classes relacionadas a cards
      const cardElements = $divs.filter('[class*="card"], [class*="board"], [class*="grid"]');
      cy.log(`Found ${cardElements.length} potential card elements`);
      
      if (cardElements.length > 0) {
        cy.wrap(cardElements.first()).should('be.visible');
        cy.log('✅ Found potential board card elements');
      } else {
        cy.log('⚠️ No board card elements found - user may need to be logged in');
      }
    });
  });

  it('should test if user is logged in and can access boards', () => {
    cy.get('body').should('be.visible');
    
    // Verificar se há indicação de login/logout
    cy.get('body').then(($body) => {
      if ($body.text().includes('Login') || $body.text().includes('Sign in')) {
        cy.log('⚠️ User appears to be logged out');
        cy.screenshot('user-logged-out');
      } else if ($body.text().includes('My') || $body.text().includes('Profile')) {
        cy.log('✅ User appears to be logged in');
        cy.screenshot('user-logged-in');
        
        // Tentar navegar para a página de boards
        cy.contains('My', { timeout: 5000 }).should('be.visible').click();
        cy.wait(2000);
        cy.screenshot('after-clicking-my');
      } else {
        cy.log('? Unable to determine login status');
        cy.screenshot('unknown-login-status');
      }
    });
  });

  it('should test navigation and board card functionality', () => {
    cy.get('body').should('be.visible');
    
    // Tentar diferentes estratégias para encontrar boards
    const strategies = [
      () => cy.get('[data-testid*="board"]'),
      () => cy.get('[class*="BoardCard"]'),
      () => cy.get('[class*="board-card"]'),
      () => cy.get('div').contains('board', { matchCase: false }),
      () => cy.get('div').contains('pins'),
      () => cy.get('[class*="grid"] > div'),
    ];

    let foundBoard = false;

    strategies.forEach((strategy, index) => {
      if (!foundBoard) {
        try {
          strategy().then(($elements) => {
            if ($elements.length > 0) {
              cy.log(`✅ Strategy ${index + 1} found ${$elements.length} elements`);
              cy.wrap($elements.first()).should('be.visible');
              foundBoard = true;
              
              // Tentar fazer hover para ver se aparece o menu
              cy.wrap($elements.first()).trigger('mouseover');
              cy.wait(1000);
              cy.screenshot(`strategy-${index + 1}-hover`);
              
              // Procurar por botões de ação
              cy.get('button').then(($buttons) => {
                const actionButtons = $buttons.filter('[title*="option"], [title*="menu"], [class*="ellipsis"]');
                if (actionButtons.length > 0) {
                  cy.log(`✅ Found ${actionButtons.length} potential action buttons`);
                  cy.wrap(actionButtons.first()).should('be.visible');
                } else {
                  cy.log('⚠️ No action buttons found');
                }
              });
            }
          });
        } catch (error) {
          cy.log(`Strategy ${index + 1} failed: ${error.message}`);
        }
      }
    });

    if (!foundBoard) {
      cy.log('⚠️ No board elements found with any strategy');
    }
  });

  it('should capture comprehensive page state', () => {
    cy.get('body').should('be.visible');
    cy.screenshot('full-page-state');
    
    // Capturar informações sobre a página
    cy.title().then((title) => {
      cy.log(`Page title: ${title}`);
    });
    
    cy.url().then((url) => {
      cy.log(`Current URL: ${url}`);
    });
    
    // Listar todos os botões na página
    cy.get('button').then(($buttons) => {
      cy.log(`Found ${$buttons.length} buttons on page`);
      $buttons.each((index, button) => {
        const text = button.textContent?.trim();
        const title = button.getAttribute('title');
        if (text || title) {
          cy.log(`Button ${index + 1}: "${text}" (title: "${title}")`);
        }
      });
    });
    
    // Listar elementos com classes relacionadas a grid/card
    cy.get('[class*="grid"], [class*="card"], [class*="board"]').then(($elements) => {
      cy.log(`Found ${$elements.length} grid/card/board elements`);
    });
  });
}); 