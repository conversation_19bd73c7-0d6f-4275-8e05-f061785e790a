describe('Direct Pin Edit Test', () => {
  it('should directly test pin edit functionality', () => {
    // Interceptar todas as requisições para debug
    cy.intercept('GET', '**/pins/**').as('getPin');
    cy.intercept('PUT', '**/pins/**').as('updatePin');
    cy.intercept('POST', '**/pins/**').as('createPin');

    cy.visit('http://localhost:5773');
    cy.wait(3000);

    // Capturar logs do console
    cy.window().then((win) => {
      win.console.log = cy.stub().as('consoleLog');
      win.console.error = cy.stub().as('consoleError');
    });

    // Navegar diretamente para My Pins
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);

    // Aguardar carregamento e procurar por pins
    cy.get('body').should('be.visible');
    
    // Procurar por qualquer elemento que possa ser um pin
    cy.get('body').then(($body) => {
      // Estratégia mais ampla para encontrar pins
      const allImages = $body.find('img');
      const allCards = $body.find('div[class*="card"], div[class*="pin"], div[class*="grid"]');
      const allClickable = $body.find('[role="button"], button, a, div[onclick]');
      
      cy.log(`📊 Elementos encontrados:`);
      cy.log(`- Imagens: ${allImages.length}`);
      cy.log(`- Cards: ${allCards.length}`);
      cy.log(`- Clicáveis: ${allClickable.length}`);

      // Tentar encontrar pins por diferentes estratégias
      let pinElements = $body.find('img').filter((i, el) => {
        const src = Cypress.$(el).attr('src') || '';
        const alt = Cypress.$(el).attr('alt') || '';
        return src.includes('http') && (alt.includes('pin') || src.includes('pin') || src.includes('image'));
      });

      if (pinElements.length === 0) {
        // Estratégia 2: procurar por cards
        pinElements = $body.find('div').filter((i, el) => {
          const className = Cypress.$(el).attr('class') || '';
          return className.includes('card') && Cypress.$(el).find('img').length > 0;
        });
      }

      if (pinElements.length === 0) {
        // Estratégia 3: procurar por qualquer elemento clicável com imagem
        pinElements = $body.find('*').filter((i, el) => {
          const $el = Cypress.$(el);
          return $el.find('img').length > 0 && $el.is(':visible');
        });
      }

      cy.log(`🎯 Pins encontrados: ${pinElements.length}`);

      if (pinElements.length > 0) {
        // Clicar no primeiro pin encontrado
        cy.wrap(pinElements.first()).click({ force: true });
        cy.wait(2000);

        // Aguardar modal abrir
        cy.get('body').then(($modalBody) => {
          const modals = $modalBody.find('[role="dialog"], .modal, div[class*="modal"], div[class*="fixed"]').filter(':visible');
          
          cy.log(`📱 Modais visíveis: ${modals.length}`);

          if (modals.length > 0) {
            const modal = modals.last();
            
            // Procurar botão More Options
            const moreButtons = modal.find('button').filter((i, el) => {
              const $btn = Cypress.$(el);
              const title = $btn.attr('title') || '';
              const ariaLabel = $btn.attr('aria-label') || '';
              const text = $btn.text();
              
              return title.toLowerCase().includes('more') || 
                     ariaLabel.toLowerCase().includes('more') ||
                     text.includes('⋯') || text.includes('...') ||
                     $btn.find('svg').length > 0;
            });

            cy.log(`🔘 Botões More encontrados: ${moreButtons.length}`);

            if (moreButtons.length > 0) {
              cy.wrap(moreButtons.first()).click({ force: true });
              cy.wait(1000);

              // Procurar opção Edit Pin
              cy.get('body').then(($menuBody) => {
                const editButtons = $menuBody.find('button').filter((i, el) => {
                  const text = Cypress.$(el).text().toLowerCase();
                  return text.includes('edit pin') || text.includes('edit');
                });

                cy.log(`✏️ Botões Edit encontrados: ${editButtons.length}`);

                if (editButtons.length > 0) {
                  cy.wrap(editButtons.first()).click({ force: true });
                  cy.wait(2000);

                  // Agora testar a edição
                  cy.testDirectEdit();
                } else {
                  cy.log('❌ Botão Edit Pin não encontrado');
                  // Listar todos os botões disponíveis
                  const allButtons = $menuBody.find('button');
                  allButtons.each((i, btn) => {
                    const text = Cypress.$(btn).text();
                    cy.log(`Botão ${i}: "${text}"`);
                  });
                }
              });
            } else {
              cy.log('❌ Botão More Options não encontrado');
              // Listar todos os botões no modal
              const allButtons = modal.find('button');
              allButtons.each((i, btn) => {
                const text = Cypress.$(btn).text();
                const title = Cypress.$(btn).attr('title') || '';
                cy.log(`Botão ${i}: "${text}" (title: "${title}")`);
              });
            }
          } else {
            cy.log('❌ Nenhum modal encontrado após clicar no pin');
          }
        });
      } else {
        cy.log('❌ Nenhum pin encontrado na página');
        // Tentar criar um pin primeiro
        cy.createDirectTestPin();
      }
    });
  });
});

// Comando para criar pin de teste
Cypress.Commands.add('createDirectTestPin', () => {
  cy.log('🔄 Criando pin de teste diretamente');
  
  // Procurar botão Add/Create
  cy.get('body').then(($body) => {
    const createButtons = $body.find('button, a').filter((i, el) => {
      const text = Cypress.$(el).text().toLowerCase();
      return text.includes('add') || text.includes('create') || text.includes('new');
    });

    cy.log(`➕ Botões Create encontrados: ${createButtons.length}`);

    if (createButtons.length > 0) {
      cy.wrap(createButtons.first()).click({ force: true });
      cy.wait(2000);

      // Preencher formulário
      cy.get('body').then(($formBody) => {
        const modal = $formBody.find('[role="dialog"], .modal').last();
        
        if (modal.length > 0) {
          const inputs = modal.find('input');
          cy.log(`📝 Inputs encontrados: ${inputs.length}`);

          if (inputs.length > 0) {
            // Preencher primeiro input (nome)
            cy.wrap(inputs.first()).clear().type('Pin Teste Direto');
            
            // Se houver segundo input, preencher com URL
            if (inputs.length > 1) {
              cy.wrap(inputs.eq(1)).clear().type('https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop');
            }

            // Procurar botão save/create
            const saveButtons = modal.find('button').filter((i, el) => {
              const text = Cypress.$(el).text().toLowerCase();
              return text.includes('create') || text.includes('save') || text.includes('add');
            });

            if (saveButtons.length > 0) {
              cy.wrap(saveButtons.first()).click({ force: true });
              cy.wait(3000);
              
              // Recarregar e tentar editar
              cy.reload();
              cy.wait(3000);
              cy.testDirectEdit();
            }
          }
        }
      });
    }
  });
});

// Comando para testar edição direta
Cypress.Commands.add('testDirectEdit', () => {
  cy.log('🔄 Testando edição direta');

  cy.get('body').then(($body) => {
    const editModal = $body.find('[role="dialog"], .modal').filter(':visible').last();
    
    if (editModal.length > 0) {
      cy.log('✅ Modal de edição encontrado');

      // Listar todos os inputs
      const inputs = editModal.find('input');
      cy.log(`📝 Total de inputs: ${inputs.length}`);

      inputs.each((i, input) => {
        const $input = Cypress.$(input);
        const label = $input.closest('div').find('label').text();
        const placeholder = $input.attr('placeholder') || '';
        const value = $input.val();
        const type = $input.attr('type') || 'text';
        
        cy.log(`Input ${i}: type="${type}", label="${label}", placeholder="${placeholder}", value="${value}"`);
      });

      // Procurar especificamente pelo campo nome
      const nameInput = inputs.filter((i, el) => {
        const $el = Cypress.$(el);
        const label = $el.closest('div').find('label').text().toLowerCase();
        const placeholder = $el.attr('placeholder')?.toLowerCase() || '';
        
        return label.includes('pin name') || label.includes('name') || 
               placeholder.includes('pin name') || placeholder.includes('name');
      }).first();

      if (nameInput.length > 0) {
        cy.log('✅ Campo nome encontrado');
        
        const originalValue = nameInput.val();
        const newValue = `Pin Editado Direto ${Date.now()}`;
        
        cy.log(`📝 Valor original: "${originalValue}"`);
        cy.log(`📝 Novo valor: "${newValue}"`);

        // Interceptar requisição antes de editar
        cy.intercept('PUT', '**/pins/**').as('updatePin');

        // Editar campo
        cy.wrap(nameInput).clear().type(newValue, { force: true });
        cy.wait(1000);

        // Verificar se foi inserido
        cy.wrap(nameInput).should('have.value', newValue);

        // Procurar botão Save
        const saveButtons = editModal.find('button').filter((i, el) => {
          const text = Cypress.$(el).text().toLowerCase();
          return text.includes('save') || text.includes('update') || text.includes('salvar');
        });

        cy.log(`💾 Botões Save encontrados: ${saveButtons.length}`);

        if (saveButtons.length > 0) {
          cy.wrap(saveButtons.first()).click({ force: true });

          // Aguardar requisição
          cy.wait('@updatePin', { timeout: 15000 }).then((interception) => {
            if (interception) {
              cy.log('📡 REQUISIÇÃO INTERCEPTADA:');
              cy.log(`URL: ${interception.request.url}`);
              cy.log(`Method: ${interception.request.method}`);
              cy.log(`Headers: ${JSON.stringify(interception.request.headers, null, 2)}`);
              cy.log(`Body: ${JSON.stringify(interception.request.body, null, 2)}`);
              
              const body = interception.request.body;
              if (body && body.title) {
                cy.log(`✅ Campo 'title' ENCONTRADO: "${body.title}"`);
                if (body.title === newValue) {
                  cy.log('✅ Valor correto sendo enviado!');
                } else {
                  cy.log(`❌ Valor incorreto! Esperado: "${newValue}", Enviado: "${body.title}"`);
                }
              } else {
                cy.log('❌ Campo "title" NÃO encontrado na requisição');
                cy.log(`📋 Campos enviados: ${Object.keys(body || {}).join(', ')}`);
              }

              if (interception.response) {
                cy.log(`📡 Status: ${interception.response.statusCode}`);
                cy.log(`Response: ${JSON.stringify(interception.response.body, null, 2)}`);
              }
            }
          }).catch(() => {
            cy.log('⚠️ Nenhuma requisição interceptada - possível problema!');
          });

          cy.wait(3000);

          // Verificar logs do console
          cy.get('@consoleLog').then((stub) => {
            if (stub.callCount > 0) {
              cy.log('📋 LOGS DO CONSOLE:');
              stub.getCalls().slice(-20).forEach((call, i) => {
                const args = call.args.join(' ');
                if (args.includes('🔄') || args.includes('📝') || args.includes('✅') || args.includes('❌')) {
                  cy.log(`${i}: ${args}`);
                }
              });
            }
          });

          // Verificar erros
          cy.get('@consoleError').then((stub) => {
            if (stub.callCount > 0) {
              cy.log('❌ ERROS NO CONSOLE:');
              stub.getCalls().forEach((call, i) => {
                cy.log(`Erro ${i}: ${call.args.join(' ')}`);
              });
            }
          });

        } else {
          cy.log('❌ Botão Save não encontrado');
        }
      } else {
        cy.log('❌ Campo nome não encontrado');
      }
    } else {
      cy.log('❌ Modal de edição não encontrado');
    }
  });
});

declare global {
  namespace Cypress {
    interface Chainable {
      createDirectTestPin(): Chainable<void>;
      testDirectEdit(): Chainable<void>;
    }
  }
} 