/// <reference types="cypress" />

describe('Profile Pins Display', () => {
  const testUser = {
    email: '<EMAIL>',
    password: 'Test123!@#',
    firstName: 'Test',
    lastName: 'User'
  };

  beforeEach(() => {
    // Limpar dados anteriores
    cy.clearLocalStorage();
    cy.clearCookies();
    
    // Ignorar erros de aplicação que não afetam o teste
    cy.on('uncaught:exception', (err, runnable) => {
      if (err.message.includes('Failed to fetch dynamically imported module') ||
          err.message.includes('Loading chunk') ||
          err.message.includes('ChunkLoadError') ||
          err.message.includes('operation "GetUserPins" not found') ||
          err.message.includes('Failed to fetch user pins')) {
        return false;
      }
      return true;
    });
  });

  it('should display pins in profile All Pins section', () => {
    cy.log('🧪 Testing profile pins display...');

    // 1. Visitar a aplicação
    cy.visit('http://localhost:5773');
    cy.wait(3000);

    // 2. Verificar se o usuário está autenticado ou fazer login
    cy.get('body').then(($body) => {
      if ($body.text().includes('Login') || $body.text().includes('Sign In')) {
        cy.log('🔐 User not authenticated, attempting login...');
        
        // Tentar fazer login
        cy.contains(/Login|Sign In/i).click();
        cy.wait(2000);
        
        // Preencher formulário de login se existir
        cy.get('body').then(($loginBody) => {
          if ($loginBody.find('input[name="email"]').length > 0) {
            cy.get('input[name="email"]').type(testUser.email);
            cy.get('input[name="password"]').type(testUser.password);
            cy.get('button[type="submit"]').click();
            cy.wait(3000);
          }
        });
      } else {
        cy.log('✅ User appears to be authenticated');
      }
    });

    // 3. Navegar para o perfil
    cy.log('🔄 Navigating to profile...');
    
    // Tentar encontrar link/botão do perfil
    cy.get('body').then(($body) => {
      // Procurar por diferentes indicadores de perfil
      const profileIndicators = [
        'Profile',
        'Perfil', 
        'My Profile',
        'Meu Perfil',
        'Account',
        'Conta'
      ];
      
      let profileFound = false;
      
      for (const indicator of profileIndicators) {
        if ($body.text().includes(indicator)) {
          cy.contains(indicator).click();
          profileFound = true;
          break;
        }
      }
      
      if (!profileFound) {
        // Tentar navegar diretamente para a rota do perfil
        cy.visit('http://localhost:5773/profile');
      }
    });

    cy.wait(3000);

    // 4. Verificar se está na página do perfil
    cy.log('🔍 Verifying profile page...');
    cy.get('body').should(($body) => {
      const text = $body.text();
      const isProfilePage = text.includes('Pins') || 
                           text.includes('Boards') || 
                           text.includes('All Pins') ||
                           text.includes('Followers') ||
                           text.includes('Following');
      expect(isProfilePage).to.be.true;
    });

    // 5. Procurar pela aba "All Pins"
    cy.log('🔍 Looking for All Pins tab...');
    cy.get('body').then(($body) => {
      const allPinsIndicators = [
        'All Pins',
        'Todos os Pins',
        'Pins'
      ];
      
      let allPinsFound = false;
      
      for (const indicator of allPinsIndicators) {
        if ($body.text().includes(indicator)) {
          cy.log(`✅ Found "${indicator}" tab`);
          cy.contains(indicator).click();
          allPinsFound = true;
          break;
        }
      }
      
      if (!allPinsFound) {
        cy.log('⚠️ All Pins tab not found, checking current view');
      }
    });

    cy.wait(2000);

    // 6. Verificar o estado da seção de pins
    cy.log('🔍 Checking pins section state...');
    
    cy.get('body').then(($body) => {
      const bodyText = $body.text();
      
      if (bodyText.includes('Loading pins') || $body.find('.animate-spin').length > 0) {
        cy.log('⏳ Pins are loading...');
        cy.wait(5000); // Aguardar carregamento
      }
      
      if (bodyText.includes('Error loading pins')) {
        cy.log('❌ Error loading pins detected');
        cy.screenshot('profile-pins-error');
        
        // Tentar recarregar
        if ($body.find('button').filter(':contains("Retry")').length > 0) {
          cy.contains('Retry').click();
          cy.wait(3000);
        }
      }
      
      if (bodyText.includes('No pins yet') || 
          bodyText.includes('Nenhum pin') ||
          bodyText.includes('Start adding pins')) {
        cy.log('📭 No pins found - this is expected if user has no pins');
        cy.screenshot('profile-no-pins');
        
        // Verificar se há sugestão para criar pins
        const hasCreateSuggestion = bodyText.includes('Start adding') ||
                                   bodyText.includes('Create') ||
                                   bodyText.includes('Add');
        
        if (hasCreateSuggestion) {
          cy.log('✅ Empty state with creation suggestion detected');
        }
      }
      
             // Verificar se há pins sendo exibidos
       const pinElements = $body.find('[data-testid*="pin"]').length ||
                          $body.find('.pin-card').length ||
                          $body.find('img').filter((i, el) => {
                            const src = el.getAttribute('src');
                            return !!(src && (src.includes('pin') || src.includes('placeholder')));
                          }).length;
      
      if (pinElements > 0) {
        cy.log(`✅ Found ${pinElements} pin elements displayed`);
        cy.screenshot('profile-pins-displayed');
      }
      
      // Verificar contador de pins
      const pinCountMatch = bodyText.match(/(\d+)\s*pins?/i);
      if (pinCountMatch) {
        const pinCount = parseInt(pinCountMatch[1]);
        cy.log(`📊 Pin count detected: ${pinCount}`);
        
        if (pinCount > 0 && pinElements === 0) {
          cy.log('⚠️ Pin count shows pins but no pins displayed - potential issue');
          cy.screenshot('profile-pins-count-mismatch');
        }
      }
    });

    // 7. Testar criação de pin se não houver pins
    cy.get('body').then(($body) => {
      if ($body.text().includes('No pins yet') || $body.text().includes('Nenhum pin')) {
        cy.log('🔄 Testing pin creation...');
        
        // Executar script de criação de pin via console
        cy.window().then((win) => {
          // Verificar se o usuário está autenticado
          const authStore = (win as any).useAuthStore?.getState?.() || (win as any).__ZUSTAND_STORE__?.auth;
          
          if (authStore && authStore.user) {
            cy.log('✅ User authenticated, attempting to create test pin');
            
            // Criar pin via console
            cy.window().then(async (win) => {
              try {
                const { pinsService } = await (win as any).eval(`
                  import('/src/services/pinsService.ts')
                `);
                
                const testPin = await pinsService.create({
                  title: 'Test Pin for Profile',
                  description: 'Pin created by Cypress test',
                  imageUrl: 'https://via.placeholder.com/300x300/4F46E5/FFFFFF?text=Cypress+Test',
                  origin: 'Cypress Test',
                  releaseYear: 2024,
                  originalPrice: 19.99,
                  pinNumber: `CYPRESS-${Date.now()}`,
                  tradable: true,
                  isPublic: true
                });
                
                cy.log('✅ Test pin created:', testPin);
                
                // Recarregar página para ver o pin
                cy.reload();
                cy.wait(3000);
                
                // Verificar se o pin aparece agora
                cy.get('body').should('not.contain', 'No pins yet');
                cy.log('✅ Pin should now be visible in profile');
                
              } catch (error) {
                cy.log('❌ Failed to create test pin:', error);
              }
            });
          }
        });
      }
    });

    // 8. Screenshot final
    cy.screenshot('profile-pins-final-state');
    cy.log('🎉 Profile pins test completed');
  });

  it('should test pins loading states', () => {
    cy.log('🧪 Testing pins loading states...');

    cy.visit('http://localhost:5773/profile');
    cy.wait(2000);

    // Verificar estado de loading
    cy.get('body').then(($body) => {
      if ($body.find('.animate-spin').length > 0) {
        cy.log('✅ Loading spinner detected');
        cy.screenshot('pins-loading-state');
      }
      
      if ($body.text().includes('Loading pins')) {
        cy.log('✅ Loading text detected');
      }
    });

    // Aguardar carregamento completo
    cy.wait(5000);

    // Verificar estado final
    cy.get('body').should('not.contain', 'Loading pins');
    cy.log('✅ Loading completed');
  });

  it('should test pins error handling', () => {
    cy.log('🧪 Testing pins error handling...');

    // Interceptar requisições para simular erro
    cy.intercept('POST', '**/getUserPins', {
      statusCode: 500,
      body: { error: 'Internal server error' }
    }).as('getUserPinsError');

    cy.visit('http://localhost:5773/profile');
    cy.wait(2000);

    // Clicar na aba All Pins se existir
    cy.get('body').then(($body) => {
      if ($body.text().includes('All Pins')) {
        cy.contains('All Pins').click();
      }
    });

    // Verificar se erro é exibido
    cy.get('body').should('contain', 'Error loading pins');
    cy.log('✅ Error state detected');
    cy.screenshot('pins-error-state');

    // Testar botão de retry se existir
    cy.get('body').then(($body) => {
      if ($body.find('button').filter(':contains("Retry")').length > 0) {
        cy.contains('Retry').click();
        cy.log('✅ Retry button works');
      }
    });
  });
}); 