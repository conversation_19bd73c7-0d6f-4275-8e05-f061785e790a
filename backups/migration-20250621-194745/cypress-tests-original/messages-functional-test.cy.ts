/// <reference types="cypress" />

describe('Sistema de Mensagens - Teste Funcional', () => {
  
  beforeEach(() => {
    // Interceptar APIs principais
    cy.intercept('GET', '/api/messages/conversations/user/*').as('getConversations');
    cy.intercept('POST', '/api/messages/send').as('sendMessage');
    cy.intercept('DELETE', '/api/messages/*').as('deleteMessage');
    cy.intercept('POST', '/api/messages/*/reactions').as('addReaction');
    cy.intercept('DELETE', '/api/messages/*/reactions').as('removeReaction');
    
    // Visitar página de mensagens
    cy.visit('/messages');
    cy.wait(2000); // Aguardar carregamento inicial
  });

  describe('Interface e Navegação Básica', () => {
    it('deve carregar a página de mensagens', () => {
      cy.url().should('include', '/messages');
      
      // Verificar se o título está presente
      cy.contains('Messages').should('be.visible');
      
      // Verificar se existe algum conteúdo principal (lista de conversas ou estado vazio)
      cy.get('body').should('be.visible');
    });

    it('deve mostrar interface responsiva em mobile', () => {
      cy.viewport(375, 667); // iPhone viewport
      cy.reload();
      cy.wait(1000);
      
      // Interface deve carregar sem erros
      cy.contains('Messages').should('be.visible');
    });

    it('deve mostrar interface em desktop', () => {
      cy.viewport(1280, 720); // Desktop viewport
      cy.reload();
      cy.wait(1000);
      
      // Interface deve carregar sem erros
      cy.contains('Messages').should('be.visible');
    });
  });

  describe('Funcionalidades de Conversa', () => {
    it('deve permitir criar nova conversa', () => {
      // Procurar botão de nova mensagem
      cy.get('body').then(($body) => {
        // Tentar diferentes variações de botões de nova conversa
        if ($body.find('button:contains("New Message")').length > 0) {
          cy.contains('button', 'New Message').click();
        } else if ($body.find('button:contains("New")').length > 0) {
          cy.contains('button', 'New').click();
        } else if ($body.find('[data-testid="new-conversation-btn"]').length > 0) {
          cy.get('[data-testid="new-conversation-btn"]').click();
        } else {
          cy.log('Botão de nova conversa não encontrado - interface pode estar simplificada');
        }
      });
    });

    it('deve carregar conversas do usuário', () => {
      // Verificar se a requisição de conversas é feita
      cy.wait('@getConversations', { timeout: 15000 }).then((interception) => {
        const statusCode = interception.response?.statusCode;
        expect(statusCode).to.be.oneOf([200, 404]);
        
        if (statusCode === 200) {
          const conversations = interception.response?.body?.conversations || [];
          cy.log(`Conversas encontradas: ${conversations.length}`);
          
          if (conversations.length > 0) {
            // Se há conversas, verificar se são exibidas
            cy.log('Verificando exibição de conversas existentes');
          } else {
            // Se não há conversas, verificar estado vazio
            cy.log('Nenhuma conversa encontrada - verificando estado vazio');
          }
        }
      });
    });
  });

  describe('Envio de Mensagens', () => {
    it('deve permitir digitar em campo de mensagem', () => {
      const testMessage = `Teste Cypress - ${new Date().toLocaleTimeString()}`;
      
      // Procurar campo de input de mensagem
      cy.get('body').then(($body) => {
        if ($body.find('textarea[placeholder*="message"]').length > 0) {
          cy.get('textarea[placeholder*="message"]')
            .first()
            .should('be.visible')
            .type(testMessage);
            
          // Verificar se o texto foi digitado
          cy.get('textarea[placeholder*="message"]')
            .first()
            .should('have.value', testMessage);
            
        } else if ($body.find('input[placeholder*="message"]').length > 0) {
          cy.get('input[placeholder*="message"]')
            .first()
            .should('be.visible')
            .type(testMessage);
            
          cy.get('input[placeholder*="message"]')
            .first()
            .should('have.value', testMessage);
            
        } else {
          cy.log('Campo de input de mensagem não encontrado - pode não estar em uma conversa ativa');
        }
      });
    });

    it('deve enviar mensagem quando possível', () => {
      const testMessage = 'Mensagem de teste do Cypress';
      
      // Tentar encontrar e usar campo de mensagem
      cy.get('body').then(($body) => {
        if ($body.find('textarea[placeholder*="message"]').length > 0) {
          // Digitar mensagem
          cy.get('textarea[placeholder*="message"]')
            .first()
            .type(testMessage);
          
          // Procurar botão de envio
          if ($body.find('button[type="submit"]').length > 0) {
            cy.get('button[type="submit"]').click();
          } else if ($body.find('button:contains("Send")').length > 0) {
            cy.contains('button', 'Send').click();
          } else {
            // Tentar enviar com Enter
            cy.get('textarea[placeholder*="message"]').type('{enter}');
          }
          
                     // Se conseguiu enviar, verificar a requisição
           cy.wait('@sendMessage', { timeout: 10000 }).then((interception) => {
             if (interception.response?.statusCode === 201) {
               cy.log('✅ Mensagem enviada com sucesso');
             } else {
               cy.log('⚠️ Resposta inesperada do servidor');
             }
           });
          
        } else {
          cy.log('Campo de mensagem não disponível - pode não estar em conversa ativa');
        }
      });
    });
  });

  describe('Funcionalidades Avançadas', () => {
    it('deve verificar se há indicador de conexão', () => {
      // Procurar indicadores de status de conexão
      cy.get('body').then(($body) => {
        if ($body.find(':contains("Online")').length > 0) {
          cy.contains('Online').should('be.visible');
          cy.log('✅ Indicador de conexão online encontrado');
        } else if ($body.find(':contains("Offline")').length > 0) {
          cy.contains('Offline').should('be.visible');
          cy.log('⚠️ Sistema em modo offline');
        } else if ($body.find(':contains("Reconnecting")').length > 0) {
          cy.contains('Reconnecting').should('be.visible');
          cy.log('🔄 Sistema reconectando');
        } else {
          cy.log('Indicador de conexão não encontrado');
        }
      });
    });

    it('deve verificar funcionalidade de busca', () => {
      // Procurar funcionalidade de busca
      cy.get('body').then(($body) => {
        if ($body.find('button[title*="Search"]').length > 0) {
          cy.get('button[title*="Search"]').click();
          cy.log('✅ Botão de busca encontrado e clicado');
        } else if ($body.find('input[placeholder*="search"]').length > 0) {
          cy.get('input[placeholder*="search"]').should('be.visible');
          cy.log('✅ Campo de busca encontrado');
        } else {
          cy.log('Funcionalidade de busca não encontrada');
        }
      });
    });

    it('deve verificar responsividade mobile', () => {
      cy.viewport(375, 667); // Mobile
      cy.wait(1000);
      
      // Verificar se interface mobile carrega
      cy.get('body').should('be.visible');
      cy.contains('Messages').should('be.visible');
      
      // Verificar navegação mobile se houver conversas
      cy.get('body').then(($body) => {
        if ($body.find('button:contains("Back")').length > 0) {
          cy.log('✅ Navegação mobile detectada');
        }
      });
    });
  });

  describe('Estados e Erros', () => {
    it('deve lidar com estado vazio', () => {
      // Verificar se há mensagem de estado vazio
      cy.get('body').then(($body) => {
        if ($body.find(':contains("No conversations")').length > 0) {
          cy.contains('No conversations').should('be.visible');
          cy.log('✅ Estado vazio de conversas detectado');
        } else if ($body.find(':contains("No messages")').length > 0) {
          cy.contains('No messages').should('be.visible');
          cy.log('✅ Estado vazio de mensagens detectado');
        } else if ($body.find(':contains("Select a conversation")').length > 0) {
          cy.contains('Select a conversation').should('be.visible');
          cy.log('✅ Prompt de seleção de conversa detectado');
        } else {
          cy.log('Estado vazio não detectado - pode haver conversas existentes');
        }
      });
    });

    it('deve tratar erro de conexão', () => {
      // Simular erro de rede
      cy.intercept('GET', '/api/messages/**', { forceNetworkError: true }).as('networkError');
      
      cy.reload();
      cy.wait(3000);
      
      // Verificar se há indicação de problema de conexão
      cy.get('body').then(($body) => {
        if ($body.find(':contains("error")').length > 0 || 
            $body.find(':contains("Error")').length > 0 ||
            $body.find(':contains("failed")').length > 0 ||
            $body.find(':contains("Failed")').length > 0) {
          cy.log('✅ Indicação de erro de conexão detectada');
        } else {
          cy.log('Sistema pode estar funcionando em modo fallback');
        }
      });
    });

    it('deve mostrar indicadores de loading', () => {
      cy.reload();
      
      // Procurar indicadores de loading no início
      cy.get('body').then(($body) => {
        if ($body.find(':contains("Loading")').length > 0) {
          cy.contains('Loading').should('be.visible');
          cy.log('✅ Indicador de loading detectado');
        } else if ($body.find('.spinner').length > 0) {
          cy.get('.spinner').should('be.visible');
          cy.log('✅ Spinner de loading detectado');
        } else {
          cy.log('Indicadores de loading não detectados - carregamento pode ser muito rápido');
        }
      });
    });
  });

  describe('Performance', () => {
    it('deve carregar em tempo razoável', () => {
      const startTime = Date.now();
      
      cy.visit('/messages');
      cy.contains('Messages', { timeout: 10000 }).should('be.visible').then(() => {
        const loadTime = Date.now() - startTime;
        expect(loadTime).to.be.lessThan(10000); // 10 segundos
        cy.log(`⏱️ Tempo de carregamento: ${loadTime}ms`);
      });
    });

    it('deve funcionar em diferentes resoluções', () => {
      const viewports = [
        [320, 568],   // iPhone SE
        [375, 667],   // iPhone 8
        [768, 1024],  // iPad
        [1280, 720],  // Desktop
        [1920, 1080]  // Full HD
      ];
      
      viewports.forEach(([width, height]) => {
        cy.viewport(width, height);
        cy.wait(500);
        cy.contains('Messages').should('be.visible');
        cy.log(`✅ Funcionando em ${width}x${height}`);
      });
    });
  });

  after(() => {
    cy.log('🎉 Testes de mensagens concluídos com sucesso!');
  });
}); 