/// <reference types="cypress" />

describe('Board Complete Workflow', () => {
  let createdBoardId: string;
  let boardName: string;

  beforeEach(() => {
    // Ignorar erros de aplicação que não afetam o teste
    cy.on('uncaught:exception', (err, runnable) => {
      if (err.message.includes('Failed to fetch dynamically imported module') ||
          err.message.includes('Loading chunk') ||
          err.message.includes('ChunkLoadError') ||
          err.message.includes('operation') ||
          err.message.includes('Failed to fetch')) {
        return false;
      }
      return true;
    });

    // Gerar nome único para o board
    boardName = `Test Board ${Date.now()}`;
  });

  it('should complete full board workflow: create → edit → add pin → delete', () => {
    // 1. NAVEGAÇÃO INICIAL
    cy.visit('http://localhost:5773');
    cy.wait(3000);
    cy.screenshot('01-homepage');

    // Verificar se usuário está logado, se não, fazer login
    cy.get('body').then(($body) => {
      if ($body.text().includes('Login') || $body.text().includes('Sign in')) {
        cy.log('🔐 User needs to login');
        // Tentar fazer login automático ou pular se não conseguir
        cy.contains('Login', { timeout: 5000 }).should('be.visible').click();
        cy.wait(2000);
        cy.screenshot('02-login-page');
        
        // Se houver campos de login, tentar preencher
        cy.get('body').then(($loginBody) => {
          if ($loginBody.find('input[type="email"]').length > 0) {
            cy.get('input[type="email"]').type('<EMAIL>');
            cy.get('input[type="password"]').type('123456');
            cy.get('button[type="submit"]').click();
            cy.wait(3000);
          }
        });
      }
    });

    // 2. NAVEGAR PARA MY PINS
    cy.log('📋 Navigating to My Pins');
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);
    cy.screenshot('03-my-pins-page');

    // 3. CRIAR NOVO BOARD
    cy.log('➕ Creating new board');
    cy.contains('New Board', { timeout: 10000 }).should('be.visible').click();
    cy.wait(1000);
    cy.screenshot('04-new-board-modal');

    // Preencher formulário de criação
    cy.get('input[placeholder*="board name"], input[id="name"]', { timeout: 5000 })
      .should('be.visible')
      .clear()
      .type(boardName);

    cy.get('textarea[placeholder*="describe"], textarea[id="description"]')
      .should('be.visible')
      .clear()
      .type('Board criado automaticamente pelo teste Cypress');

    // Submeter formulário
    cy.contains('Create Board', { timeout: 5000 }).should('be.visible').click();
    cy.wait(3000);
    cy.screenshot('05-board-created');

    // Verificar se o board foi criado
    cy.contains(boardName, { timeout: 10000 }).should('be.visible');
    cy.log('✅ Board created successfully');

    // 4. EDITAR O BOARD
    cy.log('✏️ Editing the board');
    
    // Encontrar o card do board criado e fazer hover
    cy.contains(boardName).parents('[class*="bg-white"], [class*="card"]').within(() => {
      // Fazer hover para mostrar o menu
      cy.get('[class*="group"]').trigger('mouseover');
      cy.wait(1000);
      
      // Clicar no menu de 3 pontinhos
      cy.get('button[title*="options"], button[title*="Board options"]', { timeout: 5000 })
        .should('be.visible')
        .click();
      
      // Clicar em Edit
      cy.contains('Edit').should('be.visible').click();
    });

    cy.wait(1000);
    cy.screenshot('06-edit-modal');

    // Editar o nome do board
    const editedName = `${boardName} - Edited`;
    cy.get('input[value*="Test Board"], input[id="name"]', { timeout: 5000 })
      .should('be.visible')
      .clear()
      .type(editedName);

    cy.get('textarea[id="description"]')
      .clear()
      .type('Board editado pelo teste Cypress');

    // Salvar edição
    cy.contains('Update Board', { timeout: 5000 }).should('be.visible').click();
    cy.wait(3000);
    cy.screenshot('07-board-edited');

    // Verificar se a edição foi aplicada
    cy.contains(editedName, { timeout: 10000 }).should('be.visible');
    cy.log('✅ Board edited successfully');

    // 5. NAVEGAR PARA O BOARD E ADICIONAR PIN
    cy.log('📌 Adding pin to board');
    
    // Clicar no board para abrir
    cy.contains(editedName).click();
    cy.wait(3000);
    cy.screenshot('08-board-view');

    // Procurar pelo botão Add Pin
    cy.get('button').contains('Add Pin', { timeout: 10000 }).should('be.visible').click();
    cy.wait(1000);
    cy.screenshot('09-add-pin-modal');

    // Preencher formulário do pin
    const pinName = `Test Pin ${Date.now()}`;
    cy.get('input[placeholder*="pin name"], input[placeholder*="title"]', { timeout: 5000 })
      .should('be.visible')
      .clear()
      .type(pinName);

    cy.get('textarea[placeholder*="description"]')
      .should('be.visible')
      .clear()
      .type('Pin criado automaticamente pelo teste Cypress');

    // Tentar adicionar uma imagem (se houver campo de upload)
    cy.get('body').then(($body) => {
      if ($body.find('input[type="file"]').length > 0) {
        cy.get('input[type="file"]').selectFile('cypress/fixtures/test-image.jpg', { force: true });
        cy.wait(2000);
      }
    });

    // Submeter formulário do pin
    cy.contains('Create', { timeout: 5000 }).should('be.visible').click();
    cy.wait(3000);
    cy.screenshot('10-pin-created');

    // Verificar se o pin foi criado
    cy.contains(pinName, { timeout: 10000 }).should('be.visible');
    cy.log('✅ Pin added successfully');

    // 6. VOLTAR PARA MY PINS
    cy.log('🔙 Going back to My Pins');
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);
    cy.screenshot('11-back-to-my-pins');

    // 7. DELETAR O BOARD
    cy.log('🗑️ Deleting the board');
    
    // Encontrar o board editado e abrir menu
    cy.contains(editedName).parents('[class*="bg-white"], [class*="card"]').within(() => {
      // Fazer hover para mostrar o menu
      cy.get('[class*="group"]').trigger('mouseover');
      cy.wait(1000);
      
      // Clicar no menu de 3 pontinhos
      cy.get('button[title*="options"], button[title*="Board options"]', { timeout: 5000 })
        .should('be.visible')
        .click();
      
      // Clicar em Delete
      cy.contains('Delete').should('be.visible').click();
    });

    cy.wait(1000);
    cy.screenshot('12-delete-confirmation');

    // Verificar modal de confirmação
    cy.contains('Delete Board', { timeout: 5000 }).should('be.visible');
    cy.contains('This action cannot be undone').should('be.visible');
    cy.contains('pins in this board').should('be.visible');

    // Confirmar exclusão
    cy.get('button').contains('Delete Board').should('be.visible').click();
    cy.wait(3000);
    cy.screenshot('13-board-deleted');

    // Verificar se o board foi removido
    cy.contains(editedName).should('not.exist');
    cy.log('✅ Board deleted successfully');

    cy.log('🎉 Complete workflow finished successfully!');
  });

  it('should test individual components separately', () => {
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);

    // Testar criação de board isoladamente
    cy.log('🧪 Testing board creation component');
    cy.contains('New Board').should('be.visible').click();
    cy.get('input[placeholder*="board name"], input[id="name"]').should('be.visible');
    cy.get('textarea[placeholder*="describe"], textarea[id="description"]').should('be.visible');
    cy.contains('Cancel').click(); // Fechar modal

    // Testar se existem boards para testar edição/exclusão
    cy.get('body').then(($body) => {
      const boardCards = $body.find('[class*="bg-white"][class*="rounded"]');
      if (boardCards.length > 0) {
        cy.log('🧪 Testing board actions menu');
        cy.wrap(boardCards.first()).within(() => {
          cy.get('[class*="group"]').trigger('mouseover');
          cy.get('button[title*="options"]', { timeout: 5000 }).should('be.visible');
        });
      } else {
        cy.log('⚠️ No boards found to test actions menu');
      }
    });

    cy.screenshot('component-tests');
  });

  it('should capture detailed page state for debugging', () => {
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);

    // Capturar estado da página
    cy.get('body').should('be.visible');
    cy.screenshot('debug-page-state');

    // Listar todos os elementos interativos
    cy.get('button').then(($buttons) => {
      cy.log(`Found ${$buttons.length} buttons on page`);
      $buttons.each((index, button) => {
        const text = button.textContent?.trim();
        const title = button.getAttribute('title');
        if (text || title) {
          cy.log(`Button ${index + 1}: "${text}" (title: "${title}")`);
        }
      });
    });

    // Listar cards de board
    cy.get('[class*="bg-white"], [class*="card"]').then(($cards) => {
      cy.log(`Found ${$cards.length} potential board cards`);
    });

    // Verificar se há grids
    cy.get('[class*="grid"]').then(($grids) => {
      cy.log(`Found ${$grids.length} grid elements`);
    });
  });
}); 