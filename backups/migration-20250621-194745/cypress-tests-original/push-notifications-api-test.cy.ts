/// <reference types="cypress" />

describe('Push Notifications API E2E Test', () => {
  const API_BASE_URL = 'http://localhost:3001/api/admin/push-notifications';

  beforeEach(() => {
    // Verify backend server is running before each test
    cy.request({
      method: 'GET',
      url: `${API_BASE_URL}/stats`,
      failOnStatusCode: false
    }).then((response) => {
      if (response.status !== 200) {
        cy.log('❌ Backend server is not running on port 3001');
        cy.log('💡 Please start the server with: POSTGRES_PORT=5433 node server.js');
        throw new Error('Backend server not available');
      } else {
        cy.log('✅ Backend server is running');
      }
    });
  });

  it('should send a test notification via API and verify response', () => {
    cy.log('🧪 Testing Push Notification API directly');

    // Step 1: Get current statistics
    cy.request('GET', `${API_BASE_URL}/stats`).then((statsResponse) => {
      expect(statsResponse.status).to.eq(200);
      expect(statsResponse.body).to.have.property('totalTokens');
      expect(statsResponse.body).to.have.property('sentToday');
      expect(statsResponse.body).to.have.property('sentThisWeek');
      expect(statsResponse.body).to.have.property('sentThisMonth');
      expect(statsResponse.body).to.have.property('successRate');
      cy.log(`📊 Current stats: ${JSON.stringify(statsResponse.body)}`);
    });

    // Step 2: Send test notification
    const testNotification = {
      title: `🧪 Cypress API Test ${Date.now()}`,
      body: `API test notification sent at ${new Date().toLocaleTimeString()}`,
      data: {
        test: true,
        source: 'cypress-api-test',
        timestamp: new Date().toISOString()
      }
    };

    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/test`,
      body: testNotification
    }).then((response) => {
      // Step 3: Verify response structure
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('message');
      expect(response.body).to.have.property('totalSent');
      expect(response.body).to.have.property('successCount');
      expect(response.body).to.have.property('failureCount');

      // Verify the counts make sense
      expect(response.body.successCount).to.be.a('number');
      expect(response.body.failureCount).to.be.a('number');
      expect(response.body.totalSent).to.eq(response.body.successCount + response.body.failureCount);

      cy.log(`✅ Test notification sent successfully!`);
      cy.log(`📊 Results: ${response.body.successCount}/${response.body.totalSent} delivered`);
    });

    // Step 4: Verify notification appears in history
    cy.wait(1000); // Brief wait for database write
    cy.request('GET', `${API_BASE_URL}/history?limit=5`).then((historyResponse) => {
      expect(historyResponse.status).to.eq(200);
      expect(historyResponse.body).to.be.an('array');
      
      if (historyResponse.body.length > 0) {
        const latestNotification = historyResponse.body[0];
        expect(latestNotification).to.have.property('title');
        expect(latestNotification).to.have.property('body');
        expect(latestNotification).to.have.property('type');
        expect(latestNotification).to.have.property('recipientCount');
        expect(latestNotification).to.have.property('successCount');
        expect(latestNotification).to.have.property('sentAt');
        expect(latestNotification).to.have.property('sentBy');
        
        cy.log(`📚 Latest notification in history: ${latestNotification.title}`);
      }
    });
  });

  it('should validate API error handling for missing fields', () => {
    cy.log('🔍 Testing API validation');

    // Test missing title
    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/test`,
      body: { body: 'Test body without title' },
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.eq(400);
      expect(response.body).to.have.property('error');
      expect(response.body.error).to.include('title and body are required');
    });

    // Test missing body
    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/test`,
      body: { title: 'Test title without body' },
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.eq(400);
      expect(response.body).to.have.property('error');
      expect(response.body.error).to.include('title and body are required');
    });

    // Test empty request
    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/test`,
      body: {},
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.eq(400);
      expect(response.body).to.have.property('error');
    });

    cy.log('✅ API validation working correctly');
  });

  it('should test send-all endpoint', () => {
    cy.log('📢 Testing send-all notification endpoint');

    const broadcastNotification = {
      title: `📢 Cypress Broadcast Test ${Date.now()}`,
      body: `Broadcast test notification sent at ${new Date().toLocaleTimeString()}`,
      actionUrl: 'https://localhost:5773/explore',
      data: {
        test: true,
        source: 'cypress-broadcast-test',
        timestamp: new Date().toISOString()
      }
    };

    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/send-all`,
      body: broadcastNotification
    }).then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('totalSent');
      expect(response.body).to.have.property('successCount');
      expect(response.body).to.have.property('failureCount');

      cy.log(`✅ Broadcast notification sent successfully!`);
      cy.log(`📊 Results: ${response.body.successCount}/${response.body.totalSent} delivered`);
    });
  });

  it('should test send-specific endpoint', () => {
    cy.log('👥 Testing send-specific notification endpoint');

    const specificNotification = {
      userIds: ['test-user-1', 'test-user-2'],
      title: `👥 Cypress Specific Test ${Date.now()}`,
      body: `Specific users test notification sent at ${new Date().toLocaleTimeString()}`,
      actionUrl: 'https://localhost:5773/profile',
      data: {
        test: true,
        source: 'cypress-specific-test',
        timestamp: new Date().toISOString()
      }
    };

    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/send-specific`,
      body: specificNotification
    }).then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('totalSent');
      expect(response.body).to.have.property('successCount');
      expect(response.body).to.have.property('failureCount');

      cy.log(`✅ Specific notification sent successfully!`);
      cy.log(`📊 Results: ${response.body.successCount}/${response.body.totalSent} delivered`);
    });

    // Test validation for send-specific
    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/send-specific`,
      body: {
        title: 'Test',
        body: 'Test message'
        // Missing userIds
      },
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.eq(400);
      expect(response.body).to.have.property('error');
      expect(response.body.error).to.include('userIds array is required');
    });
  });

  it('should test templates endpoint', () => {
    cy.log('📋 Testing templates endpoint');

    // Get existing templates
    cy.request('GET', `${API_BASE_URL}/templates`).then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body).to.be.an('array');
      cy.log(`📋 Found ${response.body.length} existing templates`);
    });

    // Create a new template
    const newTemplate = {
      name: `Cypress Test Template ${Date.now()}`,
      title: '🧪 Test Template',
      body: 'This is a test template created by Cypress',
      category: 'announcement'
    };

    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/templates`,
      body: newTemplate
    }).then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('id');
      cy.log(`✅ Template created with ID: ${response.body.id}`);
    });

    // Test template validation
    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/templates`,
      body: {
        name: 'Incomplete Template'
        // Missing required fields
      },
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.eq(400);
      expect(response.body).to.have.property('error');
    });
  });

  it('should test tokens endpoint', () => {
    cy.log('📱 Testing FCM tokens endpoint');

    // Save a test FCM token
    const tokenData = {
      userId: `cypress-test-user-${Date.now()}`,
      token: `cypress-test-token-${Date.now()}`,
      platform: 'web',
      userAgent: 'Cypress Test Browser'
    };

    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/tokens`,
      body: tokenData
    }).then((response) => {
      expect(response.status).to.eq(200);
      cy.log('✅ Test FCM token saved successfully');
    });

    // Get tokens count
    cy.request('GET', `${API_BASE_URL}/tokens/count`).then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('count');
      expect(response.body.count).to.be.a('number');
      expect(response.body.count).to.be.at.least(0);
      cy.log(`📱 Total FCM tokens: ${response.body.count}`);
    });
  });

  it('should test complete notification workflow', () => {
    cy.log('🔄 Testing complete notification workflow');

    let initialStats;
    
    // Step 1: Get initial statistics
    cy.request('GET', `${API_BASE_URL}/stats`).then((response) => {
      initialStats = response.body;
      cy.log(`📊 Initial stats: ${JSON.stringify(initialStats)}`);
    });

    // Step 2: Send a test notification
    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/test`,
      body: {
        title: '🔄 Workflow Test',
        body: 'Testing complete workflow',
        data: { workflow: 'complete' }
      }
    }).then((response) => {
      expect(response.body.success).to.be.true;
      cy.log(`✅ Notification sent: ${response.body.successCount}/${response.body.totalSent}`);
    });

    // Step 3: Verify updated statistics
    cy.wait(1000); // Wait for database update
    cy.request('GET', `${API_BASE_URL}/stats`).then((response) => {
      const newStats = response.body;
      
      // Verify stats were updated (at least sentToday should increase)
      expect(newStats.sentToday).to.be.at.least(initialStats.sentToday);
      cy.log(`📊 Updated stats: ${JSON.stringify(newStats)}`);
    });

    // Step 4: Verify notification in history
    cy.request('GET', `${API_BASE_URL}/history?limit=1`).then((response) => {
      expect(response.body).to.be.an('array');
      if (response.body.length > 0) {
        const latestNotification = response.body[0];
        expect(latestNotification.title).to.include('🔄 Workflow Test');
        cy.log('✅ Notification found in history');
      }
    });

    cy.log('🎉 Complete workflow test passed!');
  });

  it('should simulate WebSocket broadcasting (mock FCM)', () => {
    cy.log('📡 Testing WebSocket broadcasting functionality');

    // The backend uses mockSendFCMNotification which broadcasts via WebSocket
    // We can test this by sending a notification and checking the response
    
    const webSocketTestNotification = {
      title: '📡 WebSocket Test',
      body: 'Testing WebSocket broadcasting functionality',
      data: {
        test: true,
        webSocket: true,
        timestamp: new Date().toISOString()
      }
    };

    cy.request({
      method: 'POST',
      url: `${API_BASE_URL}/test`,
      body: webSocketTestNotification
    }).then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body.success).to.be.true;
      
      // The mock FCM function should have "broadcast" to WebSocket clients
      // In a real scenario, connected clients would receive this notification
      cy.log('✅ WebSocket broadcast simulation completed');
      cy.log(`📊 Mock broadcast results: ${response.body.successCount}/${response.body.totalSent}`);
    });
  });
});