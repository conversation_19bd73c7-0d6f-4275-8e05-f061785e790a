/// <reference types="cypress" />

describe('Push Notifications System E2E Test', () => {
  beforeEach(() => {
    // Set up authenticated admin user before each test
    cy.visit('http://localhost:5773/login', {
      onBeforeLoad: (win) => {
        // Clear any existing auth data
        win.localStorage.clear();
        
        // Set up authenticated admin user
        const mockAuthData = {
          state: {
            user: {
              id: "admin-test-user",
              email: "<EMAIL>",
              firstName: "Admin",
              lastName: "User",
              username: "admin-user",
              displayName: "Admin User",
              avatarUrl: "",
              bio: "",
              location: "",
              phoneNumber: "",
              emailVerified: true,
              isActive: true,
              isVerified: true,
              role: "admin",
              status: "active",
              preferences: {
                theme: "dark",
                language: "en",
                notifications: {
                  email: true,
                  push: true,
                  trades: true,
                  messages: true
                }
              }
            },
            adminUser: {
              id: "admin-test-user",
              email: "<EMAIL>",
              firstName: "Admin",
              lastName: "User",
              username: "admin-user",
              displayName: "Admin User",
              role: "admin",
              status: "active",
              emailVerified: true
            },
            isAuthenticated: true,
            authLoaded: true,
            permissions: {
              canAccessAdmin: true,
              canManageUsers: true,
              canManageTradingPoints: true,
              canModerate: true,
              isAmbassador: false
            }
          },
          version: 0
        };

        win.localStorage.setItem('auth-storage', JSON.stringify(mockAuthData));
        console.log('✅ Admin auth data configured');
      }
    });

    // Login with test credentials
    cy.get('input[name="email"]', { timeout: 10000 }).should('be.visible').type('<EMAIL>');
    cy.get('input[name="password"]').should('be.visible').type('password123');
    cy.get('button[type="submit"]').click();

    // Wait for redirect and verify authentication
    cy.url({ timeout: 10000 }).should('not.include', '/login');
    cy.wait(2000);
  });

  it('should access push notifications page and send test notification', () => {
    cy.log('🚀 Starting push notifications test');

    // Navigate to admin push notifications page
    cy.visit('http://localhost:5773/admin/push-notifications');
    cy.wait(3000);

    // Verify we're on the correct page
    cy.url().should('include', '/admin/push-notifications');
    cy.contains('Push Notifications').should('be.visible');
    cy.contains('Send notifications to users across the platform').should('be.visible');

    // Verify the tabs are present
    cy.contains('Send Notification').should('be.visible');
    cy.contains('Templates').should('be.visible');
    cy.contains('History').should('be.visible');
    cy.contains('Statistics').should('be.visible');

    // Fill out notification form
    cy.log('📝 Filling out notification form');
    
    // Find and fill title field
    cy.get('input[placeholder*="title"]', { timeout: 10000 })
      .should('be.visible')
      .clear()
      .type('🧪 Cypress Test Notification');

    // Find and fill message field
    cy.get('textarea[placeholder*="message"]', { timeout: 10000 })
      .should('be.visible')
      .clear()
      .type('This is a test notification sent from Cypress E2E test at ' + new Date().toLocaleString());

    // Optional: Fill action URL
    cy.get('input[placeholder*="https://example.com"]')
      .should('be.visible')
      .clear()
      .type('https://localhost:5773/explore');

    // Verify preview appears
    cy.contains('Preview').should('be.visible');
    cy.contains('🧪 Cypress Test Notification').should('be.visible');

    // Send test notification
    cy.log('🧪 Sending test notification');
    cy.contains('button', 'Enviar Notificação de Teste').click();

    // Wait for response and verify success
    cy.contains('Test notification sent successfully', { timeout: 15000 }).should('be.visible');

    // Verify success message appears
    cy.get('.Toastify__toast--success', { timeout: 10000 }).should('be.visible');

    cy.log('✅ Test notification sent successfully');
  });

  it('should verify notification appears in history', () => {
    cy.log('📊 Testing notification history');

    // Navigate to admin push notifications page
    cy.visit('http://localhost:5773/admin/push-notifications');
    cy.wait(3000);

    // First send a test notification
    cy.get('input[placeholder*="title"]')
      .clear()
      .type('🧪 History Test Notification');

    cy.get('textarea[placeholder*="message"]')
      .clear()
      .type('This notification should appear in history');

    cy.contains('button', 'Enviar Notificação de Teste').click();
    cy.contains('Test notification sent successfully', { timeout: 15000 }).should('be.visible');

    // Switch to History tab
    cy.contains('History').click();
    cy.wait(2000);

    // Verify notification appears in history
    cy.contains('🧪 TEST: 🧪 History Test Notification').should('be.visible');
    cy.contains('[Test Mode] This notification should appear in history').should('be.visible');
    cy.contains('admin-test').should('be.visible');

    cy.log('✅ Notification found in history');
  });

  it('should display statistics correctly', () => {
    cy.log('📈 Testing statistics display');

    // Navigate to admin push notifications page
    cy.visit('http://localhost:5773/admin/push-notifications');
    cy.wait(3000);

    // Switch to Statistics tab
    cy.contains('Statistics').click();
    cy.wait(2000);

    // Verify statistics elements are present
    cy.contains('Registered Devices').should('be.visible');
    cy.contains('Sent Today').should('be.visible');
    cy.contains('Success Rate').should('be.visible');
    cy.contains('This Week').should('be.visible');
    cy.contains('This Month').should('be.visible');

    // Verify numeric values are displayed
    cy.get('[class*="text-2xl"]').should('have.length.at.least', 5);

    cy.log('✅ Statistics displayed correctly');
  });

  it('should test form validation', () => {
    cy.log('🔍 Testing form validation');

    // Navigate to admin push notifications page
    cy.visit('http://localhost:5773/admin/push-notifications');
    cy.wait(3000);

    // Try to send without title
    cy.contains('button', 'Enviar Notificação de Teste').click();
    cy.contains('Title and message are required').should('be.visible');

    // Add title but no message
    cy.get('input[placeholder*="title"]').type('Test Title');
    cy.contains('button', 'Enviar Notificação de Teste').click();
    cy.contains('Title and message are required').should('be.visible');

    // Add message and verify it works
    cy.get('textarea[placeholder*="message"]').type('Test message');
    cy.contains('button', 'Enviar Notificação de Teste').click();
    cy.contains('Test notification sent successfully', { timeout: 15000 }).should('be.visible');

    cy.log('✅ Form validation working correctly');
  });

  it('should test notification type selection', () => {
    cy.log('👥 Testing notification type selection');

    // Navigate to admin push notifications page
    cy.visit('http://localhost:5773/admin/push-notifications');
    cy.wait(3000);

    // Verify "All Users" is selected by default
    cy.contains('All Users').parent().should('have.class', 'border-blue-500');

    // Click "Specific Users"
    cy.contains('Specific Users').click();
    cy.contains('Specific Users').parent().should('have.class', 'border-blue-500');

    // Verify user selection interface appears
    cy.contains('Select Users').should('be.visible');
    cy.get('input[placeholder*="Search users"]').should('be.visible');

    // Switch back to "All Users"
    cy.contains('All Users').click();
    cy.contains('All Users').parent().should('have.class', 'border-blue-500');

    // Verify user selection interface disappears
    cy.contains('Select Users').should('not.exist');

    cy.log('✅ Notification type selection working correctly');
  });

  it('should handle network errors gracefully', () => {
    cy.log('🌐 Testing network error handling');

    // Navigate to admin push notifications page
    cy.visit('http://localhost:5773/admin/push-notifications');
    cy.wait(3000);

    // Intercept the API call and force it to fail
    cy.intercept('POST', '**/admin/push-notifications/test', {
      statusCode: 500,
      body: { error: 'Server error' }
    }).as('failedNotification');

    // Fill form
    cy.get('input[placeholder*="title"]').type('Error Test');
    cy.get('textarea[placeholder*="message"]').type('This should fail');

    // Try to send
    cy.contains('button', 'Enviar Notificação de Teste').click();

    // Wait for the intercepted request
    cy.wait('@failedNotification');

    // Verify error message appears
    cy.contains('Error sending test notification', { timeout: 10000 }).should('be.visible');

    cy.log('✅ Network error handled correctly');
  });
}); 