describe('Manual Title Test', () => {
  it('should manually test title editing step by step', () => {
    cy.visit('http://localhost:5773');
    cy.wait(3000);

    // Interceptar requisições
    cy.intercept('PUT', '**/pins/**').as('updatePin');
    cy.intercept('GET', '**/pins/**').as('getPin');

    // Capturar logs
    cy.window().then((win) => {
      win.console.log = cy.stub().as('consoleLog');
      win.console.error = cy.stub().as('consoleError');
    });

    // Ir para My Pins
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);

    // Procurar por qualquer imagem (pin)
    cy.get('img').first().click();
    cy.wait(2000);

    // Procurar botão More Options (três pontos)
    cy.get('button[title*="More"]').click();
    cy.wait(1000);

    // Clicar em Edit Pin
    cy.contains('button', 'Edit pin').click();
    cy.wait(2000);

    // Encontrar campo Pin Name e editar
    cy.get('input').each(($input) => {
      const label = $input.closest('div').find('label').text();
      if (label.toLowerCase().includes('pin name') || label.toLowerCase().includes('name')) {
        const newTitle = `Teste Manual ${Date.now()}`;
        
        cy.log(`📝 Editando campo: ${label}`);
        cy.log(`📝 Novo valor: ${newTitle}`);
        
        cy.wrap($input).clear().type(newTitle);
        cy.wrap($input).should('have.value', newTitle);
        
        // Salvar
        cy.contains('button', 'Save').click();
        
        // Aguardar requisição
        cy.wait('@updatePin', { timeout: 10000 }).then((interception) => {
          cy.log('📡 Requisição capturada:');
          cy.log(JSON.stringify(interception.request.body, null, 2));
          
          const body = interception.request.body;
          if (body && body.title) {
            cy.log(`✅ Title presente: ${body.title}`);
          } else {
            cy.log('❌ Title ausente!');
            cy.log(`Campos: ${Object.keys(body || {}).join(', ')}`);
          }
        });
        
        return false; // Para sair do each
      }
    });

    cy.wait(3000);

    // Verificar logs
    cy.get('@consoleLog').then((stub) => {
      cy.log('📋 Logs do console:');
      stub.getCalls().slice(-30).forEach((call, i) => {
        const message = call.args.join(' ');
        if (message.includes('🔄') || message.includes('📝') || message.includes('✅') || message.includes('❌')) {
          cy.log(`${i}: ${message}`);
        }
      });
    });
  });
}); 