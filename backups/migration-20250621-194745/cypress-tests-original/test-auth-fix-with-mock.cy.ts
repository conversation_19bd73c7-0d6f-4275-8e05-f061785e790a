describe('Test Auth Fix com Mock Data', () => {
  it('deve manter usuário autenticado com dados mock e não redirecionar', () => {
    // Configurar dados mock ANTES de visitar a página
    cy.visit('http://localhost:5773', {
      onBeforeLoad: (win) => {
        const mockAuthData = {
          state: {
            user: {
              id: "gMcFplcYxxMGzoHcmmZlNngSl0u1",
              email: "<EMAIL>",
              firstName: "<PERSON>",
              lastName: "<PERSON><PERSON><PERSON>",
              username: "felipe-tavar<PERSON>",
              displayName: "<PERSON>",
              avatarUrl: "",
              bio: "",
              location: "",
              phoneNumber: "",
              emailVerified: true,
              isActive: true,
              isVerified: true,
              role: "user",
              status: "active",
              preferences: {
                theme: "light",
                language: "pt",
                notifications: {
                  email: true,
                  push: true,
                  trades: true,
                  messages: true
                }
              },
              stats: {
                pinsCount: 0,
                boardsCount: 0,
                followersCount: 0,
                followingCount: 0,
                checkInsCount: 0,
                tradesCompletedCount: 0,
                likesReceivedCount: 0
              }
            },
            adminUser: {
              id: "gMcFplcYxxMGzoHcmmZlNngSl0u1",
              email: "<EMAIL>",
              firstName: "Felipe",
              lastName: "Tavares",
              username: "felipe-tavares",
              displayName: "Felipe Tavares",
              role: "user",
              status: "active",
              emailVerified: true
            },
            isAuthenticated: true,
            permissions: {
              canAccessAdmin: false,
              canManageUsers: false,
              canManageTradingPoints: false,
              canModerate: false,
              isAmbassador: false
            }
          },
          version: 0
        };

        win.localStorage.setItem('auth-storage', JSON.stringify(mockAuthData));
        console.log('✅ Mock auth data configured before page load');
      }
    });
    
    // Aguardar carregamento
    cy.wait(4000);
    
    // Verificar se não foi redirecionado para login
    cy.url().should('not.include', '/login');
    
    // Se estiver logado, deve estar numa página protegida
    cy.url().should('match', /\/(home|explore|my-pins|dashboard|collection|$)/);
    
    // Testar navegação para rota protegida específica
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(2000);
    
    // Verificar que não foi redirecionado para login
    cy.url().should('include', '/my-pins');
    cy.url().should('not.include', '/login');
    
    // Testar outra rota protegida
    cy.visit('http://localhost:5773/explore');
    cy.wait(2000);
    
    // Verificar que permaneceu autenticado
    cy.url().should('include', '/explore');
    cy.url().should('not.include', '/login');
    
    cy.log('✅ Teste com mock data passou - usuário permaneceu autenticado');
  });

  it('deve verificar que dados mock foram carregados corretamente', () => {
    cy.visit('http://localhost:5773', {
      onBeforeLoad: (win) => {
        const mockAuthData = {
          state: {
            user: {
              id: "gMcFplcYxxMGzoHcmmZlNngSl0u1",
              email: "<EMAIL>",
              displayName: "Felipe Tavares"
            },
            isAuthenticated: true
          },
          version: 0
        };
        win.localStorage.setItem('auth-storage', JSON.stringify(mockAuthData));
      }
    });
    
    cy.wait(3000);
    
    cy.window().then((win) => {
      const authStorage = win.localStorage.getItem('auth-storage');
      expect(authStorage).to.exist;
      
      if (authStorage) {
        const parsedAuth = JSON.parse(authStorage);
        expect(parsedAuth.state.isAuthenticated).to.be.true;
        expect(parsedAuth.state.user.email).to.equal('<EMAIL>');
        
        cy.log(`✅ User: ${parsedAuth.state.user.displayName}`);
        cy.log(`✅ Authenticated: ${parsedAuth.state.isAuthenticated}`);
      }
    });
  });
}); 