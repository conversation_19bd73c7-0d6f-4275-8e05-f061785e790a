describe('Edit Pin Name Test', () => {
  beforeEach(() => {
    // Visitar a página inicial
    cy.visit('http://localhost:5773');
    cy.wait(2000);
  });

  it('should edit pin name and save changes', () => {
    cy.log('🧪 Iniciando teste de edição do nome do pin');

    // Navegar para uma página com pins (ex: My Pins)
    cy.get('body').then(($body) => {
      // Procurar por links de navegação
      const myPinsLink = $body.find('a[href*="/my-pins"], a[href*="/pins"], button').filter((i, el) => {
        const text = Cypress.$(el).text().toLowerCase();
        return text.includes('my pins') || text.includes('pins') || text.includes('collection');
      }).first();

      if (myPinsLink.length > 0) {
        cy.wrap(myPinsLink).click();
        cy.wait(3000);
      } else {
        // Se não encontrar, tentar navegar diretamente
        cy.visit('http://localhost:5773/my-pins');
        cy.wait(3000);
      }
    });

    // Procurar por um pin para editar
    cy.get('body').then(($body) => {
      const pinCards = $body.find('[data-testid*="pin"], .pin-card, img[alt*="pin"], img[src*="pin"]').first();
      
      if (pinCards.length > 0) {
        cy.log('✅ Pin encontrado, clicando para abrir detalhes');
        cy.wrap(pinCards).click();
        cy.wait(2000);

        // Procurar pelo botão de editar (três pontos ou edit)
        cy.get('body').then(($modalBody) => {
          // Procurar pelo menu de três pontos
          const moreButton = $modalBody.find('button').filter((i, el) => {
            const ariaLabel = Cypress.$(el).attr('aria-label') || '';
            const title = Cypress.$(el).attr('title') || '';
            return ariaLabel.toLowerCase().includes('more') || 
                   title.toLowerCase().includes('more') ||
                   Cypress.$(el).find('svg').length > 0;
          }).first();

          if (moreButton.length > 0) {
            cy.log('✅ Botão de menu encontrado');
            cy.wrap(moreButton).click();
            cy.wait(1000);

            // Procurar pela opção "Edit pin"
            cy.get('body').then(($menuBody) => {
              const editButton = $menuBody.find('button').filter((i, el) => {
                const text = Cypress.$(el).text().toLowerCase();
                return text.includes('edit pin') || text.includes('edit');
              }).first();

              if (editButton.length > 0) {
                cy.log('✅ Botão Edit Pin encontrado');
                cy.wrap(editButton).click();
                cy.wait(2000);

                // Agora testar a edição do nome
                cy.testPinNameEdit();
              } else {
                cy.log('❌ Botão Edit Pin não encontrado');
                cy.get('body').screenshot('edit-button-not-found');
              }
            });
          } else {
            cy.log('❌ Botão de menu não encontrado');
            cy.get('body').screenshot('more-button-not-found');
          }
        });
      } else {
        cy.log('❌ Nenhum pin encontrado na página');
        cy.get('body').screenshot('no-pins-found');
      }
    });
  });
});

// Comando customizado para testar a edição do nome
Cypress.Commands.add('testPinNameEdit', () => {
  cy.log('🔄 Testando edição do campo nome');

  // Aguardar modal de edição abrir
  cy.get('body').then(($body) => {
    const modal = $body.find('[role="dialog"], .modal, div[class*="modal"]').last();
    
    if (modal.length > 0) {
      cy.log('✅ Modal de edição aberto');

      // Procurar pelo campo "Pin Name"
      const nameInput = modal.find('input').filter((i, el) => {
        const label = Cypress.$(el).closest('div').find('label').text().toLowerCase();
        const placeholder = Cypress.$(el).attr('placeholder')?.toLowerCase() || '';
        return label.includes('pin name') || label.includes('name') || 
               placeholder.includes('pin name') || placeholder.includes('name');
      }).first();

      if (nameInput.length > 0) {
        cy.log('✅ Campo Pin Name encontrado');
        
        // Obter valor atual
        const currentValue = nameInput.val();
        cy.log(`📝 Valor atual: ${currentValue}`);

        // Definir novo valor
        const newName = `Pin Editado ${Date.now()}`;
        cy.log(`📝 Novo valor: ${newName}`);

        // Limpar e digitar novo nome
        cy.wrap(nameInput).clear().type(newName);
        cy.wait(1000);

        // Verificar se o valor foi inserido
        cy.wrap(nameInput).should('have.value', newName);
        cy.log('✅ Novo nome inserido no campo');

        // Procurar pelo botão Save/Salvar
        const saveButton = modal.find('button').filter((i, el) => {
          const text = Cypress.$(el).text().toLowerCase();
          return text.includes('save') || text.includes('salvar') || text.includes('update');
        }).first();

        if (saveButton.length > 0) {
          cy.log('✅ Botão Save encontrado');
          cy.wrap(saveButton).click();
          cy.wait(3000);

          // Verificar se houve toast de sucesso
          cy.get('body').then(($toastBody) => {
            const toast = $toastBody.find('div').filter((i, el) => {
              const text = Cypress.$(el).text().toLowerCase();
              return text.includes('success') || text.includes('updated') || text.includes('saved');
            });

            if (toast.length > 0) {
              cy.log('✅ Toast de sucesso encontrado');
            } else {
              cy.log('⚠️ Toast de sucesso não encontrado');
            }
          });

          // Aguardar modal fechar e verificar se o nome foi atualizado
          cy.wait(2000);
          
          // Verificar se o nome foi atualizado na interface
          cy.get('body').then(($updatedBody) => {
            const updatedText = $updatedBody.text();
            if (updatedText.includes(newName)) {
              cy.log('✅ Nome atualizado com sucesso na interface!');
            } else {
              cy.log('❌ Nome NÃO foi atualizado na interface');
              cy.log(`🔍 Procurando por: ${newName}`);
              cy.log(`📄 Texto da página: ${updatedText.substring(0, 500)}...`);
            }
          });

        } else {
          cy.log('❌ Botão Save não encontrado');
          cy.get('body').screenshot('save-button-not-found');
        }
      } else {
        cy.log('❌ Campo Pin Name não encontrado');
        cy.get('body').screenshot('name-input-not-found');
      }
    } else {
      cy.log('❌ Modal de edição não encontrado');
      cy.get('body').screenshot('edit-modal-not-found');
    }
  });
});

declare global {
  namespace Cypress {
    interface Chainable {
      testPinNameEdit(): Chainable<void>;
    }
  }
} 