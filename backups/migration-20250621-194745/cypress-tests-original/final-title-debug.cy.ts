describe('Final Title Debug - Complete Flow Analysis', () => {
  it('should trace the complete title update flow', () => {
    // Interceptar TODAS as requisições possíveis
    cy.intercept('**', (req) => {
      if (req.url.includes('pin') || req.url.includes('update') || req.url.includes('graphql')) {
        cy.log(`📡 REQUEST: ${req.method} ${req.url}`);
        if (req.body) {
          cy.log(`📤 BODY: ${JSON.stringify(req.body, null, 2)}`);
        }
      }
    }).as('allRequests');

    cy.visit('http://localhost:5773');
    cy.wait(3000);

    // Capturar TODOS os logs do console
    cy.window().then((win) => {
      const logs: string[] = [];
      const originalLog = win.console.log;
      const originalError = win.console.error;
      
      win.console.log = (...args) => {
        const message = args.join(' ');
        logs.push(`LOG: ${message}`);
        originalLog.apply(win.console, args);
      };
      
      win.console.error = (...args) => {
        const message = args.join(' ');
        logs.push(`ERROR: ${message}`);
        originalError.apply(win.console, args);
      };

      // Salvar logs no window para acesso posterior
      (win as any).testLogs = logs;
    });

    // Navegar para My Pins
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);

    // Encontrar e clicar no primeiro pin
    cy.get('img').first().click();
    cy.wait(2000);

    // Abrir menu More Options
    cy.get('button[title*="More"]').click();
    cy.wait(1000);

    // Clicar em Edit Pin
    cy.contains('button', 'Edit pin').click();
    cy.wait(2000);

    // Encontrar o campo Pin Name especificamente
    cy.get('label').contains('Pin Name').parent().find('input').then(($input) => {
      const originalValue = $input.val();
      const newValue = `TESTE FINAL ${Date.now()}`;
      
      cy.log(`📝 Valor original: "${originalValue}"`);
      cy.log(`📝 Novo valor: "${newValue}"`);

      // Limpar e digitar novo valor
      cy.wrap($input).clear().type(newValue);
      
      // Verificar se o valor foi definido
      cy.wrap($input).should('have.value', newValue);
      
      // Disparar eventos para garantir que o React detecte a mudança
      cy.wrap($input).trigger('input');
      cy.wrap($input).trigger('change');
      cy.wrap($input).trigger('blur');
      
      cy.wait(1000);

      // Clicar no botão Save
      cy.contains('button', 'Save').click();
      
      cy.wait(5000);

      // Analisar todos os logs capturados
      cy.window().then((win) => {
        const logs = (win as any).testLogs || [];
        
        cy.log('📋 ANÁLISE COMPLETA DOS LOGS:');
        cy.log(`Total de logs: ${logs.length}`);
        
        // Filtrar logs relevantes
        const relevantLogs = logs.filter((log: string) => 
          log.includes('🔄') || log.includes('📝') || log.includes('✅') || 
          log.includes('❌') || log.includes('🔍') || log.includes('📤') ||
          log.includes('EditPinModal') || log.includes('handleSaveEdit') ||
          log.includes('PinDetailModal') || log.includes('PinsDataConnectService') ||
          log.includes('title') || log.includes('name') || log.includes('TITLE')
        );

        cy.log(`Logs relevantes: ${relevantLogs.length}`);
        
        relevantLogs.forEach((log, i) => {
          cy.log(`${i + 1}: ${log}`);
        });

        // Verificar se há logs específicos sobre o title
        const titleLogs = logs.filter((log: string) => 
          log.toLowerCase().includes('title') || log.toLowerCase().includes('name')
        );
        
        if (titleLogs.length > 0) {
          cy.log('🔍 LOGS ESPECÍFICOS SOBRE TITLE/NAME:');
          titleLogs.forEach((log, i) => {
            cy.log(`${i + 1}: ${log}`);
          });
        } else {
          cy.log('❌ NENHUM LOG SOBRE TITLE/NAME ENCONTRADO!');
        }
      });
    });
  });
}); 