/// <reference types="cypress" />

describe('Board Actions Menu', () => {
  beforeEach(() => {
    // Ignorar erros de aplicação que não afetam o teste
    cy.on('uncaught:exception', (err, runnable) => {
      if (err.message.includes('Failed to fetch dynamically imported module') ||
          err.message.includes('Loading chunk') ||
          err.message.includes('ChunkLoadError') ||
          err.message.includes('operation "GetBoardPins" not found') ||
          err.message.includes('Failed to fetch board pins')) {
        return false;
      }
      return true;
    });

    // Visitar a página de boards do usuário
    cy.visit('http://localhost:5773/my-pins');
    
    // Aguardar o carregamento da aplicação
    cy.wait(3000);
  });

  it('should show 3-dots menu on board hover and test edit/delete options', () => {
    // Aguardar a página carregar completamente
    cy.get('body').should('be.visible');
    
    // Procurar por cards de board
    cy.get('[class*="grid"]').should('exist');
    
    // Tentar encontrar um card de board
    cy.get('div').contains('board', { matchCase: false }).should('exist').then(() => {
      cy.log('✅ Found board cards');
      
      // Procurar pelo primeiro card de board
      cy.get('[class*="bg-white"][class*="rounded"]').first().within(() => {
        // Fazer hover no card para mostrar o menu
        cy.get('[class*="group"]').trigger('mouseover');
        
        // Procurar pelo botão de 3 pontinhos
        cy.get('button[title*="options"], button[title*="Board options"]', { timeout: 5000 })
          .should('be.visible')
          .click();
        
        // Verificar se o menu dropdown apareceu
        cy.get('[class*="absolute"][class*="bg-white"]').should('be.visible').within(() => {
          // Verificar se as opções Edit e Delete estão presentes
          cy.contains('Edit').should('be.visible');
          cy.contains('Delete').should('be.visible');
          
          cy.log('✅ Menu de ações encontrado com opções Edit e Delete');
        });
      });
    });
  });

  it('should open edit modal when clicking Edit option', () => {
    // Aguardar a página carregar
    cy.get('body').should('be.visible');
    
    // Procurar por um card de board e abrir o menu
    cy.get('[class*="bg-white"][class*="rounded"]').first().within(() => {
      cy.get('[class*="group"]').trigger('mouseover');
      
      cy.get('button[title*="options"], button[title*="Board options"]', { timeout: 5000 })
        .should('be.visible')
        .click();
      
      // Clicar na opção Edit
      cy.get('[class*="absolute"][class*="bg-white"]').within(() => {
        cy.contains('Edit').click();
      });
    });
    
    // Verificar se o modal de edição abriu
    cy.get('[class*="modal"], [class*="fixed"][class*="inset-0"]', { timeout: 5000 })
      .should('be.visible')
      .within(() => {
        cy.contains('Edit Board').should('be.visible');
        cy.log('✅ Modal de edição aberto com sucesso');
      });
  });

  it('should open delete confirmation modal when clicking Delete option', () => {
    // Aguardar a página carregar
    cy.get('body').should('be.visible');
    
    // Procurar por um card de board e abrir o menu
    cy.get('[class*="bg-white"][class*="rounded"]').first().within(() => {
      cy.get('[class*="group"]').trigger('mouseover');
      
      cy.get('button[title*="options"], button[title*="Board options"]', { timeout: 5000 })
        .should('be.visible')
        .click();
      
      // Clicar na opção Delete
      cy.get('[class*="absolute"][class*="bg-white"]').within(() => {
        cy.contains('Delete').click();
      });
    });
    
    // Verificar se o modal de confirmação de delete abriu
    cy.get('[class*="modal"], [class*="fixed"][class*="inset-0"]', { timeout: 5000 })
      .should('be.visible')
      .within(() => {
        cy.contains('Delete Board').should('be.visible');
        cy.contains('This action cannot be undone').should('be.visible');
        cy.contains('pins in this board').should('be.visible');
        
        // Verificar se os botões Cancel e Delete Board estão presentes
        cy.contains('Cancel').should('be.visible');
        cy.contains('Delete Board').should('be.visible');
        
        cy.log('✅ Modal de confirmação de delete aberto com mensagem correta');
      });
  });

  it('should close delete modal when clicking Cancel', () => {
    // Aguardar a página carregar
    cy.get('body').should('be.visible');
    
    // Abrir o modal de delete
    cy.get('[class*="bg-white"][class*="rounded"]').first().within(() => {
      cy.get('[class*="group"]').trigger('mouseover');
      
      cy.get('button[title*="options"], button[title*="Board options"]', { timeout: 5000 })
        .should('be.visible')
        .click();
      
      cy.get('[class*="absolute"][class*="bg-white"]').within(() => {
        cy.contains('Delete').click();
      });
    });
    
    // Clicar em Cancel no modal de confirmação
    cy.get('[class*="modal"], [class*="fixed"][class*="inset-0"]', { timeout: 5000 })
      .should('be.visible')
      .within(() => {
        cy.contains('Cancel').click();
      });
    
    // Verificar se o modal foi fechado
    cy.get('[class*="modal"], [class*="fixed"][class*="inset-0"]').should('not.exist');
    
    cy.log('✅ Modal de delete fechado ao clicar em Cancel');
  });

  it('should capture screenshots for debugging', () => {
    cy.get('body').should('be.visible');
    cy.screenshot('board-actions-menu-page');
    
    // Tentar encontrar e capturar o menu de ações
    cy.get('[class*="bg-white"][class*="rounded"]').first().within(() => {
      cy.get('[class*="group"]').trigger('mouseover');
      cy.wait(1000);
      cy.screenshot('board-actions-menu-hover');
      
      cy.get('button[title*="options"], button[title*="Board options"]', { timeout: 5000 })
        .should('be.visible')
        .click();
      
      cy.wait(1000);
      cy.screenshot('board-actions-menu-open');
    });
  });
}); 