describe('Signup Validation Test', () => {
  const testUser = {
    firstName: 'Test',
    lastName: 'User',
    email: `test-${Date.now()}@pinpal.com`,
    username: `testuser${Date.now()}`,
    password: 'Test123!@#',
    invalidEmail: 'invalid-email',
    existingEmail: '<EMAIL>',
    invalidUsername: 'ab', // Too short
    reservedUsername: 'admin'
  };

  beforeEach(() => {
    cy.clearLocalStorage();
    cy.clearCookies();
  });

  it('should load signup page and validate form fields', () => {
    cy.log('🧪 Starting comprehensive signup validation test...');

    // Monitor console for CORS errors
    const corsErrors: string[] = [];
    
    cy.window().then((win) => {
      const originalConsoleError = win.console.error;
      
      win.console.error = (...args) => {
        const message = args.join(' ');
        if (message.includes('CORS') || 
            message.includes('googleMapsConfig') || 
            message.includes('blocked') ||
            (message.includes('400') && message.includes('identitytoolkit.googleapis.com'))) {
          corsErrors.push(message);
          cy.log(`❌ CORS error detected: ${message}`);
        }
        originalConsoleError.apply(win.console, args);
      };
    });

    // 1. Visit signup page
    cy.visit('/signup');
    cy.wait(2000);

    // 2. Verify page loaded
    cy.get('body').should('be.visible');
    cy.log('✅ Signup page loaded');

    // 3. Verify form structure
    cy.get('body').then(($body) => {
      const hasSignupIndicators = $body.text().includes('Sign Up') || 
                                 $body.text().includes('Create your account') ||
                                 $body.text().includes('Criar Conta');
      
      expect(hasSignupIndicators).to.be.true;
      cy.log('✅ Confirmed this is the signup page');
    });

    // 4. Test First Name field
    cy.get('input[name="firstName"]').should('exist');
    cy.get('input[name="firstName"]').clear().type(testUser.firstName);
    cy.log('✅ First name field works');

    // 5. Test Last Name field
    cy.get('input[name="lastName"]').should('exist');
    cy.get('input[name="lastName"]').clear().type(testUser.lastName);
    cy.log('✅ Last name field works');

    // 6. Test Email Validation
    cy.log('🔍 Testing email validation...');
    cy.get('input[name="email"]').should('exist');
    
    // Test invalid email format
    cy.get('input[name="email"]').clear().type(testUser.invalidEmail);
    cy.wait(1000);
    cy.get('body').then(($body) => {
      const hasErrorMessage = $body.text().includes('valid email') || 
                             $body.text().includes('invalid') ||
                             $body.text().includes('format');
      if (hasErrorMessage) {
        cy.log('✅ Email format validation working');
      } else {
        cy.log('⚠️ Email format validation may be async');
      }
    });

    // Test valid email
    cy.get('input[name="email"]').clear().type(testUser.email);
    cy.wait(2000); // Wait for async validation
    cy.log('✅ Valid email entered');

    // 7. Test Username Validation
    cy.log('🔍 Testing username validation...');
    cy.get('input[name="username"]').should('exist');
    
    // Test invalid username (too short)
    cy.get('input[name="username"]').clear().type(testUser.invalidUsername);
    cy.wait(1000);
    cy.get('body').then(($body) => {
      const hasErrorMessage = $body.text().includes('3 characters') || 
                             $body.text().includes('too short') ||
                             $body.text().includes('invalid');
      if (hasErrorMessage) {
        cy.log('✅ Username length validation working');
      } else {
        cy.log('⚠️ Username validation may be async');
      }
    });

    // Test valid username
    cy.get('input[name="username"]').clear().type(testUser.username);
    cy.wait(2000); // Wait for availability check
    cy.log('✅ Valid username entered');

    // 8. Check for username auto-suggestion
    cy.get('body').then(($body) => {
      const hasUsernamePreview = $body.text().includes('pinpal.com/') || 
                                $body.text().includes('profile URL');
      if (hasUsernamePreview) {
        cy.log('✅ Username preview/URL feature detected');
      }
    });

    // 9. Test Password field
    cy.get('input[name="password"]').should('exist');
    cy.get('input[name="password"]').clear().type(testUser.password);
    cy.log('✅ Password field works');

    // 10. Check password visibility toggle
    cy.get('button').then(($buttons) => {
      const hasPasswordToggle = Array.from($buttons).some(btn => 
        btn.title?.includes('password') || 
        btn.getAttribute('title')?.includes('Show') ||
        btn.getAttribute('title')?.includes('Hide')
      );
      if (hasPasswordToggle) {
        cy.log('✅ Password visibility toggle detected');
      }
    });

    // 11. Verify submit button
    cy.get('button[type="submit"]').should('exist').and('contain.text', 'Sign up');
    cy.log('✅ Submit button found');

    // 12. Verify no CORS errors
    cy.then(() => {
      if (corsErrors.length === 0) {
        cy.log('✅ No CORS errors detected during validation testing');
      } else {
        cy.log('❌ CORS errors detected:');
        corsErrors.forEach(error => cy.log(`  - ${error}`));
        throw new Error(`CORS errors detected: ${corsErrors.length} errors`);
      }
    });

    // 13. Take screenshot
    cy.screenshot('signup-form-with-validations');
    
    cy.log('🎉 Signup validation test completed successfully!');
  });

  it('should test email validation states', () => {
    cy.log('🔍 Testing email validation states...');
    
    cy.visit('/signup');
    cy.wait(2000);

    const emailField = cy.get('input[name="email"]');
    
    // Test empty state
    emailField.should('exist');
    cy.log('✅ Email field exists');

    // Test invalid format
    emailField.clear().type('invalid');
    cy.wait(1000);
    
    // Test valid format
    emailField.clear().type(testUser.email);
    cy.wait(2000); // Wait for validation

    // Look for validation indicators (icons, messages)
    cy.get('body').then(($body) => {
      const hasValidationFeedback = $body.find('svg').length > 0 || // Icons
                                   $body.text().includes('Checking') ||
                                   $body.text().includes('available') ||
                                   $body.text().includes('valid');
      if (hasValidationFeedback) {
        cy.log('✅ Email validation feedback detected');
      }
    });
  });

  it('should test username validation states', () => {
    cy.log('🔍 Testing username validation states...');
    
    cy.visit('/signup');
    cy.wait(2000);

    // Fill name fields first (for auto-suggestion)
    cy.get('input[name="firstName"]').type(testUser.firstName);
    cy.get('input[name="lastName"]').type(testUser.lastName);
    cy.wait(1000);

    // Check if auto-suggestion happened
    cy.get('input[name="username"]').should('exist').then(($usernameField) => {
      const value = $usernameField.val();
      if (value && value.toString().length > 0) {
        cy.log('✅ Username auto-suggestion working');
      }
    });

    // Test manual username entry
    cy.get('input[name="username"]').clear().type(testUser.username);
    cy.wait(2000); // Wait for availability check

    // Look for availability feedback
    cy.get('body').then(($body) => {
      const hasAvailabilityFeedback = $body.text().includes('available') ||
                                     $body.text().includes('taken') ||
                                     $body.text().includes('Checking') ||
                                     $body.find('svg').length > 0; // Status icons
      if (hasAvailabilityFeedback) {
        cy.log('✅ Username availability feedback detected');
      }
    });
  });

  it('should verify fetch is not overridden', () => {
    cy.visit('/');
    cy.wait(1000);

    cy.window().then((win) => {
      // Check that fetch is the native implementation
      const fetchString = win.fetch.toString();
      const hasCustomOverride = fetchString.includes('corsProblematicUrls') || 
                               fetchString.includes('BLOCKED: Google tile server') ||
                               fetchString.includes('identitytoolkit.googleapis.com');
      
      expect(hasCustomOverride).to.be.false;
      cy.log('✅ Native fetch implementation confirmed - no CORS overrides active');
    });
  });

  it('should test form submission prevention with invalid data', () => {
    cy.log('🔍 Testing form validation prevents submission...');
    
    cy.visit('/signup');
    cy.wait(2000);

    // Try to submit empty form
    cy.get('button[type="submit"]').click();
    cy.wait(1000);

    // Check if still on signup page (validation prevented submission)
    cy.url().should('include', '/signup');
    cy.log('✅ Form validation prevents submission of empty form');

    // Fill minimal invalid data
    cy.get('input[name="firstName"]').type('A');
    cy.get('input[name="lastName"]').type('B'); 
    cy.get('input[name="email"]').type('invalid');
    cy.get('input[name="username"]').type('ab'); // Too short
    cy.get('input[name="password"]').type('123'); // Too simple

    // Try to submit invalid form
    cy.get('button[type="submit"]').click();
    cy.wait(2000);

    // Should still be on signup page
    cy.url().should('include', '/signup');
    cy.log('✅ Form validation prevents submission of invalid data');
  });
}); 