describe('Debug Pin Edit', () => {
  it('should debug pin edit functionality', () => {
    cy.visit('http://localhost:5773');
    cy.wait(3000);

    // Capturar erros do console
    cy.window().then((win) => {
      win.console.error = cy.stub().as('consoleError');
    });

    // Tentar navegar para uma página com pins
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);

    // Verificar se há pins na página
    cy.get('body').then(($body) => {
      cy.log('📄 Conteúdo da página:');
      cy.log($body.text().substring(0, 500));

      // Procurar por qualquer elemento clicável que possa ser um pin
      const clickableElements = $body.find('img, div, button, a').filter((i, el) => {
        const $el = Cypress.$(el);
        const text = $el.text().toLowerCase();
        const alt = $el.attr('alt') || '';
        const src = $el.attr('src') || '';
        const className = $el.attr('class') || '';
        
        return text.includes('pin') || 
               alt.includes('pin') || 
               src.includes('pin') ||
               className.includes('pin') ||
               className.includes('card');
      });

      cy.log(`🔍 Elementos encontrados: ${clickableElements.length}`);

      if (clickableElements.length > 0) {
        // Clicar no primeiro elemento
        cy.wrap(clickableElements.first()).click();
        cy.wait(2000);

        // Verificar se um modal abriu
        cy.get('body').then(($modalBody) => {
          const modals = $modalBody.find('[role="dialog"], .modal, div[class*="modal"]');
          cy.log(`📱 Modais encontrados: ${modals.length}`);

          if (modals.length > 0) {
            // Procurar por botões no modal
            const buttons = modals.find('button');
            cy.log(`🔘 Botões no modal: ${buttons.length}`);

            buttons.each((i, btn) => {
              const text = Cypress.$(btn).text();
              const title = Cypress.$(btn).attr('title') || '';
              cy.log(`Botão ${i}: "${text}" (title: "${title}")`);
            });

            // Procurar especificamente pelo botão de menu (três pontos)
            const moreButton = buttons.filter((i, el) => {
              const title = Cypress.$(el).attr('title') || '';
              return title.toLowerCase().includes('more');
            }).first();

            if (moreButton.length > 0) {
              cy.log('✅ Botão More Options encontrado');
              cy.wrap(moreButton).click();
              cy.wait(1000);

              // Verificar menu dropdown
              cy.get('body').then(($menuBody) => {
                const menuButtons = $menuBody.find('button').filter((i, el) => {
                  const text = Cypress.$(el).text().toLowerCase();
                  return text.includes('edit');
                });

                cy.log(`📝 Botões de edit encontrados: ${menuButtons.length}`);

                if (menuButtons.length > 0) {
                  cy.wrap(menuButtons.first()).click();
                  cy.wait(2000);

                  // Verificar modal de edição
                  cy.get('body').then(($editBody) => {
                    const editModal = $editBody.find('[role="dialog"], .modal, div[class*="modal"]').last();
                    
                    if (editModal.length > 0) {
                      cy.log('✅ Modal de edição aberto');

                      // Listar todos os inputs
                      const inputs = editModal.find('input');
                      cy.log(`📝 Inputs encontrados: ${inputs.length}`);

                      inputs.each((i, input) => {
                        const $input = Cypress.$(input);
                        const label = $input.closest('div').find('label').text();
                        const placeholder = $input.attr('placeholder') || '';
                        const value = $input.val();
                        cy.log(`Input ${i}: label="${label}", placeholder="${placeholder}", value="${value}"`);
                      });

                      // Procurar especificamente pelo campo nome
                      const nameInput = inputs.filter((i, el) => {
                        const $el = Cypress.$(el);
                        const label = $el.closest('div').find('label').text().toLowerCase();
                        return label.includes('pin name') || label.includes('name');
                      }).first();

                      if (nameInput.length > 0) {
                        cy.log('✅ Campo nome encontrado');
                        
                        const originalValue = nameInput.val();
                        const newValue = `Teste Debug ${Date.now()}`;
                        
                        cy.log(`📝 Valor original: "${originalValue}"`);
                        cy.log(`📝 Novo valor: "${newValue}"`);

                        // Interceptar requisições
                        cy.intercept('PUT', '**/pins/**').as('updatePin');
                        cy.intercept('POST', '**/pins/**').as('createPin');

                        // Editar o campo
                        cy.wrap(nameInput).clear().type(newValue);
                        cy.wait(1000);

                        // Verificar se o valor foi inserido
                        cy.wrap(nameInput).should('have.value', newValue);

                        // Procurar botão save
                        const saveButton = editModal.find('button').filter((i, el) => {
                          const text = Cypress.$(el).text().toLowerCase();
                          return text.includes('save') || text.includes('update');
                        }).first();

                        if (saveButton.length > 0) {
                          cy.log('✅ Botão Save encontrado');
                          cy.wrap(saveButton).click();

                          // Aguardar requisição
                          cy.wait('@updatePin', { timeout: 10000 }).then((interception) => {
                            if (interception) {
                              cy.log('📡 Requisição interceptada:');
                              cy.log(`URL: ${interception.request.url}`);
                              cy.log(`Method: ${interception.request.method}`);
                              cy.log(`Body: ${JSON.stringify(interception.request.body, null, 2)}`);
                              
                              if (interception.response) {
                                cy.log(`Status: ${interception.response.statusCode}`);
                                cy.log(`Response: ${JSON.stringify(interception.response.body, null, 2)}`);
                              }
                            }
                          }).catch(() => {
                            cy.log('⚠️ Nenhuma requisição de update interceptada');
                          });

                          cy.wait(3000);

                          // Verificar se houve erros no console
                          cy.get('@consoleError').then((stub) => {
                            if (stub.callCount > 0) {
                              cy.log('❌ Erros no console:');
                              stub.getCalls().forEach((call, i) => {
                                cy.log(`Erro ${i}: ${call.args.join(' ')}`);
                              });
                            } else {
                              cy.log('✅ Nenhum erro no console');
                            }
                          });

                        } else {
                          cy.log('❌ Botão Save não encontrado');
                        }
                      } else {
                        cy.log('❌ Campo nome não encontrado');
                      }
                    } else {
                      cy.log('❌ Modal de edição não encontrado');
                    }
                  });
                } else {
                  cy.log('❌ Botão Edit não encontrado no menu');
                }
              });
            } else {
              cy.log('❌ Botão More Options não encontrado');
            }
          } else {
            cy.log('❌ Nenhum modal encontrado');
          }
        });
      } else {
        cy.log('❌ Nenhum elemento clicável encontrado');
      }
    });
  });
}); 