import express from 'express';
import { getPool } from '../config/database.js';

const router = express.Router();

// Get trade opportunities
router.get('/opportunities', async (req, res) => {
  try {
    const { userId, categories, limit = 20 } = req.query;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    const pool = await getPool();
    
    let query = `
      SELECT 
        p.id,
        p.title,
        p.image_url,
        p.description,
        p.origin,
        p.release_year,
        p.original_price,
        p.pin_number,
        p.tradable,
        p.likes_count,
        p.views_count,
        p.user_id,
        p.created_at,
        u.first_name,
        u.last_name,
        u.username,
        u.avatar_url,
        COALESCE(comment_count.count, 0) as comments_count
      FROM pin p
      JOIN "user" u ON p.user_id = u.id
      LEFT JOIN (
        SELECT pin_id, COUNT(*) as count 
        FROM pin_comments 
        GROUP BY pin_id
      ) comment_count ON p.id = comment_count.pin_id
      WHERE p.tradable = true 
      AND p.is_public = true 
      AND p.user_id != $1
    `;
    
    const params = [userId];
    let paramIndex = 2;
    
    // Add category filter if provided
    if (categories) {
      const categoryList = categories.split(',');
      query += ` AND p.origin = ANY($${paramIndex})`;
      params.push(categoryList);
      paramIndex++;
    }
    
    // Order by engagement and recency
    query += ` ORDER BY (p.likes_count + COALESCE(comment_count.count, 0)) DESC, p.created_at DESC`;
    query += ` LIMIT $${paramIndex}`;
    params.push(limit);
    
    const result = await pool.query(query, params);
    
    const opportunities = result.rows.map(row => ({
      id: row.id,
      name: row.title || 'Untitled Pin',
      title: row.title || 'Untitled Pin',
      image: row.image_url || '',
      imageUrl: row.image_url || '',
      description: row.description || '',
      origin: row.origin,
      series: row.origin,
      releaseYear: row.release_year,
      year: row.release_year,
      originalPrice: row.original_price,
      pinNumber: row.pin_number,
      tradable: row.tradable,
      likes: parseInt(row.likes_count) || 0,
      likesCount: parseInt(row.likes_count) || 0,
      viewsCount: parseInt(row.views_count) || 0,
      comments: parseInt(row.comments_count) || 0,
      commentsCount: parseInt(row.comments_count) || 0,
      userId: row.user_id,
      createdAt: row.created_at,
      owner: {
        id: row.user_id,
        name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.username || 'Unknown',
        username: row.username,
        avatar: row.avatar_url
      },
      tradeScore: (parseInt(row.likes_count) || 0) + (parseInt(row.comments_count) || 0) * 2
    }));
    
    console.log(`✅ Found ${opportunities.length} trade opportunities for user ${userId}`);
    res.json(opportunities);
    
  } catch (error) {
    console.error('Error getting trade opportunities:', error);
    res.status(500).json({ error: 'Failed to get trade opportunities' });
  }
});

export default router; 