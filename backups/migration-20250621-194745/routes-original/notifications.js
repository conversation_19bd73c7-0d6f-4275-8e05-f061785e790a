import express from 'express';
import { getPool } from '../config/database.js';
import { sendNotificationToUser } from '../config/websocket.js';
import webPush from 'web-push';

const router = express.Router();

// Configure web-push (you should set these in your environment variables)
if (process.env.VAPID_PUBLIC_KEY && process.env.VAPID_PRIVATE_KEY) {
  webPush.setVapidDetails(
    'mailto:<EMAIL>',
    process.env.VAPID_PUBLIC_KEY,
    process.env.VAPID_PRIVATE_KEY
  );
} else {
  console.log('⚠️  VAPID keys not configured - push notifications will be disabled');
}

// Get user notifications
router.get('/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 20, offset = 0, unreadOnly = false } = req.query;
    
    const pool = await getPool();
    
    let query = `
      SELECT 
        n.*,
        u.first_name as from_user_first_name,
        u.last_name as from_user_last_name,
        u.username as from_user_username,
        u.avatar_url as from_user_avatar_url
      FROM notifications n
      LEFT JOIN "user" u ON n.from_user_id = u.id
      WHERE n.user_id = $1
    `;
    
    const params = [userId];
    
    if (unreadOnly === 'true') {
      query += ' AND n.is_read = false';
    }
    
    query += ' ORDER BY n.created_at DESC LIMIT $2 OFFSET $3';
    params.push(limit, offset);
    
    const result = await pool.query(query, params);
    
    res.json(result.rows);
    
  } catch (error) {
    console.error('Error getting notifications:', error);
    res.status(500).json({ error: 'Failed to get notifications' });
  }
});

// Create notification
router.post('/', async (req, res) => {
  try {
    const {
      userId,
      type,
      fromUserId,
      title,
      content,
      pinId,
      commentId,
      boardId,
      actionUrl,
      metadata = {}
    } = req.body;
    
    if (!userId || !type || !title || !content) {
      return res.status(400).json({ error: 'Required fields: userId, type, title, content' });
    }
    
    const pool = await getPool();
    
    const result = await pool.query(`
      INSERT INTO notifications (
        user_id, type, from_user_id, title, content, 
        pin_id, comment_id, board_id, action_url, metadata, 
        created_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP)
      RETURNING *
    `, [userId, type, fromUserId, title, content, pinId, commentId, boardId, actionUrl, JSON.stringify(metadata)]);
    
    const notification = result.rows[0];
    
    // Send via WebSocket if user is online
    sendNotificationToUser(userId, notification);
    
    // Send push notification if user has subscriptions and VAPID is configured
    if (process.env.VAPID_PUBLIC_KEY && process.env.VAPID_PRIVATE_KEY) {
      try {
        const subscriptions = await pool.query(
          'SELECT endpoint, p256dh, auth FROM push_subscriptions WHERE user_id = $1 AND is_active = true',
          [userId]
        );
        
        const pushPromises = subscriptions.rows.map(sub => {
          return webPush.sendNotification({
            endpoint: sub.endpoint,
            keys: {
              p256dh: sub.p256dh,
              auth: sub.auth
            }
          }, JSON.stringify({
            title,
            body: content,
            icon: '/icons/notification-icon.png',
            badge: '/icons/badge-icon.png',
            data: {
              url: actionUrl,
              notificationId: notification.id
            }
          }));
        });
        
        await Promise.allSettled(pushPromises);
      } catch (pushError) {
        console.error('Error sending push notifications:', pushError);
      }
    }
    
    res.status(201).json(notification);
    
  } catch (error) {
    console.error('Error creating notification:', error);
    res.status(500).json({ error: 'Failed to create notification' });
  }
});

// Mark notification as read
router.put('/:id/read', async (req, res) => {
  try {
    const { id } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(
      'UPDATE notifications SET is_read = true, read_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *',
      [id]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Notification not found' });
    }
    
    res.json(result.rows[0]);
    
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({ error: 'Failed to mark notification as read' });
  }
});

// Mark all notifications as read for user
router.put('/mark-all-read/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const pool = await getPool();
    
    await pool.query(
      'UPDATE notifications SET is_read = true, read_at = CURRENT_TIMESTAMP WHERE user_id = $1 AND is_read = false',
      [userId]
    );
    
    res.json({ success: true, message: 'All notifications marked as read' });
    
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    res.status(500).json({ error: 'Failed to mark notifications as read' });
  }
});

// Get unread count
router.get('/:userId/unread-count', async (req, res) => {
  try {
    const { userId } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(
      'SELECT COUNT(*) as count FROM notifications WHERE user_id = $1 AND is_read = false',
      [userId]
    );
    
    res.json({ count: parseInt(result.rows[0].count) });
    
  } catch (error) {
    console.error('Error getting unread count:', error);
    res.status(500).json({ error: 'Failed to get unread count' });
  }
});

// Delete notifications in bulk
router.delete('/bulk', async (req, res) => {
  try {
    const { notificationIds } = req.body;
    
    if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
      return res.status(400).json({ error: 'notificationIds array is required' });
    }
    
    const pool = await getPool();
    
    const result = await pool.query(
      `DELETE FROM notificationss WHERE id = ANY($1) RETURNING id`,
      [notificationIds]
    );
    
    res.json({
      success: true,
      deletedCount: result.rowCount,
      deletedIds: result.rows.map(row => row.id)
    });
    
  } catch (error) {
    console.error('Error deleting notifications:', error);
    res.status(500).json({ error: 'Failed to delete notifications' });
  }
});

// Delete single notification
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const pool = await getPool();
    
    const result = await pool.query('DELETE FROM notificationss WHERE id = $1', [id]);
    
    if (result.rowCount === 0) {
      return res.status(404).json({ error: 'Notification not found' });
    }
    
    res.json({ success: true, message: 'Notification deleted' });
    
  } catch (error) {
    console.error('Error deleting notification:', error);
    res.status(500).json({ error: 'Failed to delete notification' });
  }
});

// Delete all notifications for user
router.delete('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const pool = await getPool();
    
    const result = await pool.query('DELETE FROM notificationss WHERE user_id = $1', [userId]);
    
    res.json({
      success: true,
      deletedCount: result.rowCount,
      message: 'All notifications deleted'
    });
    
  } catch (error) {
    console.error('Error deleting user notifications:', error);
    res.status(500).json({ error: 'Failed to delete notifications' });
  }
});

// Subscribe to push notifications
router.post('/subscribe', async (req, res) => {
  try {
    const { userId, subscription } = req.body;
    
    if (!userId || !subscription) {
      return res.status(400).json({ error: 'userId and subscription are required' });
    }
    
    const { endpoint, keys } = subscription;
    
    if (!endpoint || !keys || !keys.p256dh || !keys.auth) {
      return res.status(400).json({ error: 'Invalid subscription format' });
    }
    
    const pool = await getPool();
    
    // Upsert subscription
    await pool.query(`
      INSERT INTO push_subscriptions (user_id, endpoint, p256dh, auth, created_at, is_active)
      VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, true)
      ON CONFLICT (user_id, endpoint) 
      DO UPDATE SET p256dh = $3, auth = $4, is_active = true, updated_at = CURRENT_TIMESTAMP
    `, [userId, endpoint, keys.p256dh, keys.auth]);
    
    res.json({ success: true, message: 'Subscription saved' });
    
  } catch (error) {
    console.error('Error saving push subscription:', error);
    res.status(500).json({ error: 'Failed to save subscription' });
  }
});

// Unsubscribe from push notifications
router.post('/unsubscribe', async (req, res) => {
  try {
    const { userId, endpoint } = req.body;
    
    if (!userId || !endpoint) {
      return res.status(400).json({ error: 'userId and endpoint are required' });
    }
    
    const pool = await getPool();
    
    await pool.query(
      'UPDATE push_subscriptions SET is_active = false WHERE user_id = $1 AND endpoint = $2',
      [userId, endpoint]
    );
    
    res.json({ success: true, message: 'Unsubscribed successfully' });
    
  } catch (error) {
    console.error('Error unsubscribing:', error);
    res.status(500).json({ error: 'Failed to unsubscribe' });
  }
});

export default router; 