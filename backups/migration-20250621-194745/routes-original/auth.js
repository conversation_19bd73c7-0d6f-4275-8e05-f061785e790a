import express from 'express';
import { getPool } from '../config/database.js';

const router = express.Router();

// Token verification endpoint
router.post('/verify-token', async (req, res) => {
  try {
    const { token, userId } = req.body;
    
    if (!token || !userId) {
      return res.status(400).json({ error: 'Token and userId are required' });
    }
    
    // Add your token verification logic here
    // This is a placeholder implementation
    res.json({ 
      valid: true, 
      userId,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error verifying token:', error);
    res.status(500).json({ error: 'Failed to verify token' });
  }
});

// Logout endpoint
router.post('/logout', async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (userId) {
      // Add logout logic here (clear sessions, etc.)
      console.log(`User ${userId} logged out`);
    }
    
    res.json({ success: true, message: 'Logged out successfully' });
    
  } catch (error) {
    console.error('Error during logout:', error);
    res.status(500).json({ error: 'Failed to logout' });
  }
});

// Session validation
router.get('/session/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    // Add session validation logic here
    res.json({
      valid: true,
      userId,
      lastActivity: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error validating session:', error);
    res.status(500).json({ error: 'Failed to validate session' });
  }
});

// Refresh token endpoint
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken, userId } = req.body;
    
    // Add refresh token logic here
    res.json({
      accessToken: 'new-access-token',
      refreshToken: 'new-refresh-token',
      expiresIn: 3600
    });
    
  } catch (error) {
    console.error('Error refreshing token:', error);
    res.status(500).json({ error: 'Failed to refresh token' });
  }
});

// Get current user info
router.get('/me/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const pool = await getPool();
    
    const result = await pool.query(
      'SELECT id, first_name, last_name, email, username, created_at FROM "user" WHERE id = $1',
      [userId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json(result.rows[0]);
    
  } catch (error) {
    console.error('Error getting user info:', error);
    res.status(500).json({ error: 'Failed to get user info' });
  }
});

// Check username availability
router.get('/username/check', async (req, res) => {
  try {
    const { username } = req.query;
    
    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }
    
    if (username.length < 3) {
      return res.status(400).json({ 
        error: 'Username must be at least 3 characters long',
        available: false 
      });
    }
    
    const pool = await getPool();
    
    // Check PostgreSQL
    const pgResult = await pool.query(
      'SELECT username FROM "user" WHERE LOWER(username) = LOWER($1)',
      [username]
    );
    
    if (pgResult.rows.length > 0) {
      return res.json({ 
        available: false, 
        message: 'Username is already taken' 
      });
    }
    
    // Check Firestore as well
    try {
      const { adminFirestore } = await import('../src/services/firebaseAdmin.js');
      const firestoreQuery = await adminFirestore.collection('users')
        .where('username', '==', username.toLowerCase())
        .get();
      
      if (!firestoreQuery.empty) {
        return res.json({ 
          available: false, 
          message: 'Username is already taken' 
        });
      }
    } catch (firestoreError) {
      console.warn('⚠️ Could not check Firestore for username:', firestoreError);
    }
    
    res.json({ 
      available: true, 
      message: 'Username is available' 
    });
    
  } catch (error) {
    console.error('Error checking username availability:', error);
    res.status(500).json({ error: 'Failed to check username availability' });
  }
});

export default router; 