import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ExplorePage } from '@/modules/explore/ExplorePage';
import { exploreService } from '@/services/exploreService';
import { pinsService } from '@/services/pinsService';
import { useAuthStore } from '@/store/authStore';

// Mock services
vi.mock('@/services/exploreService');
vi.mock('@/services/pinsService');
vi.mock('@/store/authStore');

// Mock PinDetailModal
vi.mock('@/components/ui/PinDetailModal', () => ({
  PinDetailModal: ({ isOpen, pin, onClose, onLike, onSave }: any) => (
    isOpen ? (
      <div data-testid="pin-detail-modal">
        <h2>{pin?.name}</h2>
        <button onClick={() => onLike(pin?.id)}>Like</button>
        <button onClick={() => onSave(pin?.id)}>Save</button>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null
  )
}));

// Mock ExploreFilters
vi.mock('@/modules/explore/components/ExploreFilters', () => ({
  ExploreFilters: ({ isOpen, onClose, onApplyFilters }: any) => (
    isOpen ? (
      <div data-testid="explore-filters">
        <button onClick={() => onApplyFilters({ rarity: ['rare'] })}>Apply Filters</button>
        <button onClick={onClose}>Close Filters</button>
      </div>
    ) : null
  )
}));

// Mock other components
vi.mock('@/components/ui/LoadingSpinner', () => ({
  LoadingSpinner: () => <div data-testid="loading-spinner">Loading...</div>
}));

vi.mock('@/components/ui/StandardInput', () => ({
  SearchInput: ({ placeholder, value, onChange, onFocus, onBlur, leftIcon }: any) => (
    <input
      data-testid="search-input"
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      onFocus={onFocus}
      onBlur={onBlur}
    />
  )
}));

const mockPins = [
  {
    id: 'pin-1',
    name: 'Test Pin 1',
    image: 'https://example.com/pin1.jpg',
    description: 'Test description 1',
    rarity: 'rare' as const,
    year: 2023,
    series: 'Disney',
    likes: 5,
    comments: 2,
    isLiked: false,
    isSaved: false,
    owner: {
      id: 'owner-1',
      name: 'Pin Owner 1',
      username: 'owner1',
      avatar: 'https://example.com/avatar1.jpg'
    },
    createdAt: new Date('2023-12-01T10:00:00Z')
  },
  {
    id: 'pin-2',
    name: 'Test Pin 2',
    image: 'https://example.com/pin2.jpg',
    description: 'Test description 2',
    rarity: 'common' as const,
    year: 2024,
    series: 'Marvel',
    likes: 10,
    comments: 5,
    isLiked: true,
    isSaved: true,
    owner: {
      id: 'owner-2',
      name: 'Pin Owner 2',
      username: 'owner2',
      avatar: 'https://example.com/avatar2.jpg'
    },
    createdAt: new Date('2023-12-02T10:00:00Z')
  }
];

const renderExplorePage = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter>
        <ExplorePage />
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('ExplorePage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock auth store
    (useAuthStore as any).mockReturnValue({
      user: { id: 'user-123', username: 'testuser' }
    });

    // Mock exploreService
    (exploreService.getPins as any).mockResolvedValue(mockPins);
    
    // Mock pinsService
    (pinsService.updateLikes as any).mockResolvedValue({});
    (pinsService.toggleSaved as any).mockResolvedValue({});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initial Rendering', () => {
    it('should render the page title and search bar', async () => {
      renderExplorePage();

      expect(screen.getByText('Explorar')).toBeInTheDocument();
      expect(screen.getByTestId('search-input')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search pins, users or tags...')).toBeInTheDocument();
    });

    it('should render filter button', () => {
      renderExplorePage();

      expect(screen.getByText('Filtros')).toBeInTheDocument();
    });

    it('should render quick filter tags', () => {
      renderExplorePage();

      expect(screen.getByText('Todos')).toBeInTheDocument();
      expect(screen.getByText('Disney')).toBeInTheDocument();
      expect(screen.getByText('Marvel')).toBeInTheDocument();
      expect(screen.getByText('Raros')).toBeInTheDocument();
    });
  });

  describe('Pin Loading', () => {
    it('should load and display pins on mount', async () => {
      renderExplorePage();

      await waitFor(() => {
        expect(exploreService.getPins).toHaveBeenCalledWith({
          page: 0,
          limit: 30,
          sortBy: 'trending'
        });
      });

      await waitFor(() => {
        expect(screen.getByAltText('Test Pin 1')).toBeInTheDocument();
        expect(screen.getByAltText('Test Pin 2')).toBeInTheDocument();
      });
    });

    it('should show loading spinner while loading', async () => {
      // Make the service return a promise that doesn't resolve immediately
      (exploreService.getPins as any).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockPins), 100))
      );

      renderExplorePage();

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });
    });

    it('should handle empty results', async () => {
      (exploreService.getPins as any).mockResolvedValue([]);

      renderExplorePage();

      await waitFor(() => {
        expect(exploreService.getPins).toHaveBeenCalled();
      });

      // Should not show any pins
      expect(screen.queryByAltText('Test Pin 1')).not.toBeInTheDocument();
    });

    it('should handle API errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      (exploreService.getPins as any).mockRejectedValue(new Error('API Error'));

      renderExplorePage();

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Error loading pins'),
          expect.any(Error)
        );
      });

      consoleSpy.mockRestore();
    });
  });

  describe('Pin Interactions', () => {
    beforeEach(async () => {
      renderExplorePage();
      await waitFor(() => {
        expect(screen.getByAltText('Test Pin 1')).toBeInTheDocument();
      });
    });

    it('should open pin detail modal when pin is clicked', async () => {
      const pin1 = screen.getByAltText('Test Pin 1');
      fireEvent.click(pin1);

      await waitFor(() => {
        expect(screen.getByTestId('pin-detail-modal')).toBeInTheDocument();
        expect(screen.getByText('Test Pin 1')).toBeInTheDocument();
      });
    });

    it('should close pin detail modal when close button is clicked', async () => {
      const pin1 = screen.getByAltText('Test Pin 1');
      fireEvent.click(pin1);

      await waitFor(() => {
        expect(screen.getByTestId('pin-detail-modal')).toBeInTheDocument();
      });

      const closeButton = screen.getByText('Close');
      fireEvent.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByTestId('pin-detail-modal')).not.toBeInTheDocument();
      });
    });

    it('should handle like action from modal', async () => {
      const pin1 = screen.getByAltText('Test Pin 1');
      fireEvent.click(pin1);

      await waitFor(() => {
        expect(screen.getByTestId('pin-detail-modal')).toBeInTheDocument();
      });

      const likeButton = screen.getByText('Like');
      fireEvent.click(likeButton);

      await waitFor(() => {
        expect(pinsService.updateLikes).toHaveBeenCalledWith('pin-1', 6, true);
      });
    });

    it('should handle save action from modal', async () => {
      const pin1 = screen.getByAltText('Test Pin 1');
      fireEvent.click(pin1);

      await waitFor(() => {
        expect(screen.getByTestId('pin-detail-modal')).toBeInTheDocument();
      });

      const saveButton = screen.getByText('Save');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(pinsService.toggleSaved).toHaveBeenCalledWith('pin-1', true);
      });
    });

    it('should revert optimistic updates on API error', async () => {
      (pinsService.updateLikes as any).mockRejectedValue(new Error('API Error'));
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const pin1 = screen.getByAltText('Test Pin 1');
      fireEvent.click(pin1);

      await waitFor(() => {
        expect(screen.getByTestId('pin-detail-modal')).toBeInTheDocument();
      });

      const likeButton = screen.getByText('Like');
      fireEvent.click(likeButton);

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Error persisting like'),
          expect.any(Error)
        );
      });

      consoleSpy.mockRestore();
    });
  });

  describe('Search Functionality', () => {
    beforeEach(async () => {
      renderExplorePage();
      await waitFor(() => {
        expect(screen.getByAltText('Test Pin 1')).toBeInTheDocument();
      });
    });

    it('should update search term when typing in search input', () => {
      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, { target: { value: 'Disney' } });

      expect(searchInput).toHaveValue('Disney');
    });

    it('should reload pins when search term changes', async () => {
      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, { target: { value: 'Disney' } });

      await waitFor(() => {
        // Should call getPins again with page 0 (reset pagination)
        expect(exploreService.getPins).toHaveBeenCalledTimes(2);
      });
    });

    it('should show search results info when searching', async () => {
      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, { target: { value: 'Disney' } });

      await waitFor(() => {
        expect(screen.getByText(/Resultados para/)).toBeInTheDocument();
        expect(screen.getByText('Disney')).toBeInTheDocument();
      });
    });
  });

  describe('Filters', () => {
    beforeEach(async () => {
      renderExplorePage();
      await waitFor(() => {
        expect(screen.getByAltText('Test Pin 1')).toBeInTheDocument();
      });
    });

    it('should open filters modal when filter button is clicked', () => {
      const filterButton = screen.getByText('Filtros');
      fireEvent.click(filterButton);

      expect(screen.getByTestId('explore-filters')).toBeInTheDocument();
    });

    it('should close filters modal when close button is clicked', () => {
      const filterButton = screen.getByText('Filtros');
      fireEvent.click(filterButton);

      expect(screen.getByTestId('explore-filters')).toBeInTheDocument();

      const closeButton = screen.getByText('Close Filters');
      fireEvent.click(closeButton);

      expect(screen.queryByTestId('explore-filters')).not.toBeInTheDocument();
    });

    it('should apply filters and reload pins', async () => {
      const filterButton = screen.getByText('Filtros');
      fireEvent.click(filterButton);

      const applyButton = screen.getByText('Apply Filters');
      fireEvent.click(applyButton);

      await waitFor(() => {
        // Should reset pagination and reload pins
        expect(exploreService.getPins).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Infinite Scroll', () => {
    it('should load more pins when scrolling to bottom', async () => {
      // Mock IntersectionObserver
      const mockIntersectionObserver = vi.fn();
      mockIntersectionObserver.mockReturnValue({
        observe: () => null,
        unobserve: () => null,
        disconnect: () => null
      });
      window.IntersectionObserver = mockIntersectionObserver;

      renderExplorePage();

      await waitFor(() => {
        expect(screen.getByAltText('Test Pin 1')).toBeInTheDocument();
      });

      // Simulate intersection (last element coming into view)
      const lastElement = screen.getByAltText('Test Pin 2').closest('div');
      if (lastElement) {
        // Trigger intersection manually
        const callback = mockIntersectionObserver.mock.calls[0][0];
        callback([{ isIntersecting: true, target: lastElement }]);
      }

      await waitFor(() => {
        expect(exploreService.getPins).toHaveBeenCalledWith({
          page: 1,
          limit: 30,
          sortBy: 'trending'
        });
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle missing pin data gracefully', async () => {
      const incompletePins = [
        {
          id: 'incomplete-pin',
          name: undefined,
          image: undefined
        }
      ];

      (exploreService.getPins as any).mockResolvedValue(incompletePins);

      renderExplorePage();

      await waitFor(() => {
        expect(exploreService.getPins).toHaveBeenCalled();
      });

      // Should not crash, even with incomplete data
      expect(screen.getByText('Explorar')).toBeInTheDocument();
    });
  });
}); 