import React from 'react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders } from '../utils/testHelpers'
import { mockAuthStore } from '../mocks/authStore'

// Component imports
import { EditBoardModal } from '../../modules/pins/components/EditBoardModal'

// Mock services
vi.mock('../../services/storageService', () => ({
  storageService: {
    validateImageFile: vi.fn(),
    uploadBoardCover: vi.fn(),
    deleteFile: vi.fn(),
  },
}))

// Mock auth store
vi.mock('../../store/authStore', () => ({
  useAuthStore: vi.fn(() => mockAuthStore),
}))

describe('EditBoardModal', () => {
  const mockBoard = {
    id: 'board-123',
    name: 'Mickey Collection',
    description: 'My favorite Mickey pins',
    coverImage: 'https://example.com/image.jpg',
    isPrivate: false,
  }

  const mockOnClose = vi.fn()
  const mockOnUpdateBoard = vi.fn()

  const defaultProps = {
    board: mockBoard,
    onClose: mockOnClose,
    onUpdateBoard: mockOnUpdateBoard,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Rendering & Initial State', () => {
    it('renders edit board modal with correct title', async () => {
      renderWithProviders(<EditBoardModal {...defaultProps} />)
      
      expect(screen.getByText('Edit Board')).toBeInTheDocument()
      expect(screen.getByRole('dialog')).toBeInTheDocument()
    })

    it('pre-fills form fields with board data', async () => {
      renderWithProviders(<EditBoardModal {...defaultProps} />)
      
      expect(screen.getByLabelText('Board Name')).toHaveValue(mockBoard.name)
      expect(screen.getByLabelText('Description (optional)')).toHaveValue(mockBoard.description)
      expect(screen.getByDisplayValue('public')).toBeChecked()
      expect(screen.getByDisplayValue('private')).not.toBeChecked()
    })

    it('shows correct privacy setting for private board', async () => {
      const privateBoardProps = {
        ...defaultProps,
        board: { ...mockBoard, isPrivate: true }
      }
      
      renderWithProviders(<EditBoardModal {...privateBoardProps} />)
      
      expect(screen.getByDisplayValue('private')).toBeChecked()
      expect(screen.getByDisplayValue('public')).not.toBeChecked()
    })
  })

  describe('Form Field Interactions', () => {
    it('allows editing board name', async () => {
      const user = userEvent.setup()
      renderWithProviders(<EditBoardModal {...defaultProps} />)
      
      const nameInput = screen.getByLabelText('Board Name')
      await user.clear(nameInput)
      await user.type(nameInput, 'Updated Collection Name')
      
      expect(nameInput).toHaveValue('Updated Collection Name')
    })

    it('allows editing description', async () => {
      const user = userEvent.setup()
      renderWithProviders(<EditBoardModal {...defaultProps} />)
      
      const descriptionInput = screen.getByLabelText('Description (optional)')
      await user.clear(descriptionInput)
      await user.type(descriptionInput, 'Updated description text')
      
      expect(descriptionInput).toHaveValue('Updated description text')
    })

    it('allows changing privacy setting', async () => {
      const user = userEvent.setup()
      renderWithProviders(<EditBoardModal {...defaultProps} />)
      
      const privateRadio = screen.getByDisplayValue('private')
      await user.click(privateRadio)
      
      expect(privateRadio).toBeChecked()
      expect(screen.getByDisplayValue('public')).not.toBeChecked()
    })
  })

  describe('Form Submission & Update', () => {
    it('calls onUpdateBoard with correct data when form is submitted', async () => {
      const user = userEvent.setup()
      renderWithProviders(<EditBoardModal {...defaultProps} />)
      
      // Edit fields
      const nameInput = screen.getByLabelText('Board Name')
      await user.clear(nameInput)
      await user.type(nameInput, 'New Board Name')
      
      const descriptionInput = screen.getByLabelText('Description (optional)')
      await user.clear(descriptionInput)
      await user.type(descriptionInput, 'New description')
      
      await user.click(screen.getByDisplayValue('private'))
      
      // Submit
      await user.click(screen.getByRole('button', { name: 'Update Board' }))
      
      await waitFor(() => {
        expect(mockOnUpdateBoard).toHaveBeenCalledWith('board-123', {
          name: 'New Board Name',
          description: 'New description',
          coverImageUrl: mockBoard.coverImage,
          isPrivate: true,
        })
      })
    })

    it('trims whitespace from name and description', async () => {
      const user = userEvent.setup()
      renderWithProviders(<EditBoardModal {...defaultProps} />)
      
      const nameInput = screen.getByLabelText('Board Name')
      await user.clear(nameInput)
      await user.type(nameInput, '  Spaced Name  ')
      
      const descriptionInput = screen.getByLabelText('Description (optional)')
      await user.clear(descriptionInput)
      await user.type(descriptionInput, '  Spaced description  ')
      
      await user.click(screen.getByRole('button', { name: 'Update Board' }))
      
      await waitFor(() => {
        expect(mockOnUpdateBoard).toHaveBeenCalledWith('board-123', {
          name: 'Spaced Name',
          description: 'Spaced description',
          coverImageUrl: mockBoard.coverImage,
          isPrivate: false,
        })
      })
    })

    it('calls onClose after successful update', async () => {
      const user = userEvent.setup()
      mockOnUpdateBoard.mockResolvedValue(undefined)
      
      renderWithProviders(<EditBoardModal {...defaultProps} />)
      
      await user.click(screen.getByRole('button', { name: 'Update Board' }))
      
      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalled()
      })
    })
  })

  describe('Modal Actions', () => {
    it('calls onClose when cancel button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProviders(<EditBoardModal {...defaultProps} />)
      
      await user.click(screen.getByRole('button', { name: 'Cancel' }))
      
      expect(mockOnClose).toHaveBeenCalled()
    })

    it('validates required name field', async () => {
      const user = userEvent.setup()
      renderWithProviders(<EditBoardModal {...defaultProps} />)
      
      const nameInput = screen.getByLabelText('Board Name')
      await user.clear(nameInput)
      
      const submitButton = screen.getByRole('button', { name: 'Update Board' })
      expect(submitButton).toBeDisabled()
    })
  })
}) 