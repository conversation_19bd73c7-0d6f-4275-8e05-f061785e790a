import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { renderWithProviders } from '../utils/testHelpers';
import { DraggableBoardGrid } from '../../modules/pins/components/DraggableBoardGrid';

// Mock das dependências de drag and drop
vi.mock('@dnd-kit/core', () => ({
  DndContext: ({ children, onDragEnd }: any) => (
    <div data-testid="dnd-context" data-drag-handler={onDragEnd?.toString()}>
      {children}
    </div>
  ),
  useSensor: vi.fn(),
  useSensors: vi.fn(),
  PointerSensor: vi.fn(),
  KeyboardSensor: vi.fn(),
  DragOverlay: ({ children }: any) => (
    <div data-testid="drag-overlay">{children}</div>
  ),
  closestCenter: vi.fn(),
}));

vi.mock('@dnd-kit/sortable', () => ({
  SortableContext: ({ children }: any) => (
    <div data-testid="sortable-context">{children}</div>
  ),
  arrayMove: vi.fn((array, oldIndex, newIndex) => {
    const result = [...array];
    result.splice(newIndex, 0, result.splice(oldIndex, 1)[0]);
    return result;
  }),
  verticalListSortingStrategy: 'vertical',
  rectSortingStrategy: 'rect', // Adicionado o export que estava faltando
  sortableKeyboardCoordinates: vi.fn(),
  useSortable: vi.fn(() => ({
    attributes: {},
    listeners: {},
    setNodeRef: vi.fn(),
    transform: null,
    transition: null,
    isDragging: false,
  })),
}));

// Mock do BoardCard
vi.mock('../../components/ui/BoardCard', () => ({
  BoardCard: ({ board, onClick, onEdit, onDelete, onShare, showPrivacyIcon, showActionsMenu, isOwner }: any) => (
    <div 
      data-testid={`board-card-${board.id}`} 
      data-board-name={board.name}
      data-show-privacy-icon={showPrivacyIcon}
      data-show-actions-menu={showActionsMenu}
      data-is-owner={isOwner}
      onClick={() => onClick?.(board.id)}
    >
      <h3>{board.name}</h3>
      <span data-testid={`pin-count-${board.id}`}>{board.pinCount} pins</span>
      {board.isPrivate && <span data-testid={`private-${board.id}`}>Private</span>}
      <button 
        onClick={(e) => {
          e.stopPropagation();
          onDelete?.(board.id);
        }}
        data-testid={`delete-button-${board.id}`}
      >
        Delete
      </button>
      <button 
        onClick={(e) => {
          e.stopPropagation();
          onEdit?.(board.id);
        }}
        data-testid={`edit-button-${board.id}`}
      >
        Edit
      </button>
      <button 
        onClick={(e) => {
          e.stopPropagation();
          onShare?.(board.id);
        }}
        data-testid={`share-button-${board.id}`}
      >
        Share
      </button>
    </div>
  ),
}));

// Mock dos componentes de design system
vi.mock('../../components/ui/design-system/patterns/CardActions', () => ({
  CardActionButton: ({ onClick, title, icon: Icon }: any) => (
    <button onClick={onClick} title={title} data-testid="card-action-button">
      {Icon && <Icon />}
    </button>
  ),
  CardActionsContainer: ({ children, position }: any) => (
    <div data-testid="card-actions-container" data-position={position}>
      {children}
    </div>
  ),
}));

describe('DraggableBoardGrid', () => {
  const mockBoards = [
    {
      id: 'board1',
      name: 'Travel Photos',
      description: 'Collection of travel memories',
      pinCount: 15,
      isPrivate: false,
      coverImageUrl: '/covers/travel.jpg',
      createdAt: '2024-01-01',
      lastUpdated: '2024-01-15',
    },
    {
      id: 'board2',
      name: 'Recipe Collection',
      description: 'Favorite recipes',
      pinCount: 8,
      isPrivate: true,
      coverImageUrl: null,
      createdAt: '2024-01-05',
      lastUpdated: '2024-01-20',
    },
    {
      id: 'board3',
      name: 'Art & Design',
      description: 'Design inspiration',
      pinCount: 23,
      isPrivate: false,
      coverImageUrl: '/covers/art.jpg',
      createdAt: '2024-01-10',
      lastUpdated: '2024-01-25',
    },
  ];

  const defaultProps = {
    boards: mockBoards,
    onBoardClick: vi.fn(),
    onBoardEdit: vi.fn(),
    onBoardDelete: vi.fn(),
    onBoardShare: vi.fn(),
    onReorder: vi.fn(),
    isOwner: true,
    isDragDisabled: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Renderização Básica', () => {
    it('deve renderizar todos os boards corretamente', () => {
      renderWithProviders(<DraggableBoardGrid {...defaultProps} />);

      expect(screen.getByTestId('board-card-board1')).toBeInTheDocument();
      expect(screen.getByTestId('board-card-board2')).toBeInTheDocument();
      expect(screen.getByTestId('board-card-board3')).toBeInTheDocument();

      expect(screen.getByText('Travel Photos')).toBeInTheDocument();
      expect(screen.getByText('Recipe Collection')).toBeInTheDocument();
      expect(screen.getByText('Art & Design')).toBeInTheDocument();
    });

    it('deve exibir contagem de pins corretamente', () => {
      renderWithProviders(<DraggableBoardGrid {...defaultProps} />);

      expect(screen.getByTestId('pin-count-board1')).toHaveTextContent('15 pins');
      expect(screen.getByTestId('pin-count-board2')).toHaveTextContent('8 pins');
      expect(screen.getByTestId('pin-count-board3')).toHaveTextContent('23 pins');
    });

    it('deve mostrar indicador de privacidade para boards privados', () => {
      renderWithProviders(<DraggableBoardGrid {...defaultProps} />);

      expect(screen.queryByTestId('private-board1')).not.toBeInTheDocument();
      expect(screen.getByTestId('private-board2')).toBeInTheDocument();
      expect(screen.queryByTestId('private-board3')).not.toBeInTheDocument();
    });

    it('deve renderizar contextos de drag and drop', () => {
      renderWithProviders(<DraggableBoardGrid {...defaultProps} />);

      expect(screen.getByTestId('dnd-context')).toBeInTheDocument();
      expect(screen.getByTestId('sortable-context')).toBeInTheDocument();
      expect(screen.getByTestId('drag-overlay')).toBeInTheDocument();
    });
  });

  describe('Configurações de Props', () => {
    it('deve renderizar com isOwner false', () => {
      const ownerProps = {
        ...defaultProps,
        isOwner: false,
      };

      renderWithProviders(<DraggableBoardGrid {...ownerProps} />);

      expect(screen.getByText('Travel Photos')).toBeInTheDocument();
      expect(screen.getByText('Recipe Collection')).toBeInTheDocument();
      expect(screen.getByText('Art & Design')).toBeInTheDocument();
    });

    it('deve renderizar boards corretamente', () => {
      renderWithProviders(<DraggableBoardGrid {...defaultProps} />);

      expect(screen.getByText('Travel Photos')).toBeInTheDocument();
      expect(screen.getByText('Recipe Collection')).toBeInTheDocument();
      expect(screen.getByText('Art & Design')).toBeInTheDocument();
    });
  });

  describe('Funcionalidade Drag and Drop', () => {
    it('deve mostrar drag handles quando drag está habilitado', () => {
      const dragEnabledProps = {
        ...defaultProps,
        isDragDisabled: false,
      };

      renderWithProviders(<DraggableBoardGrid {...dragEnabledProps} />);

      // Verifica se os drag handles estão presentes (2 elementos por board: container + button)
      const dragHandles = screen.getAllByTitle('Drag to reorder');
      expect(dragHandles).toHaveLength(6); // Dois para cada board (container + button)
    });

    it('deve ocultar drag handles quando drag está desabilitado', () => {
      const dragDisabledProps = {
        ...defaultProps,
        isDragDisabled: true,
      };

      renderWithProviders(<DraggableBoardGrid {...dragDisabledProps} />);

      // Verifica se os drag handles não estão presentes
      const dragHandles = screen.queryAllByTitle('Drag to reorder');
      expect(dragHandles).toHaveLength(0);
    });
  });

  describe('Ações dos Boards', () => {
    it('deve chamar onDeleteBoard quando deletar', async () => {
      renderWithProviders(<DraggableBoardGrid {...defaultProps} />);

      const deleteButton = screen.getByTestId('delete-button-board1');
      await userEvent.click(deleteButton);

      expect(defaultProps.onBoardDelete).toHaveBeenCalledWith('board1');
    });

    it('deve chamar onEditBoard quando editar', async () => {
      renderWithProviders(<DraggableBoardGrid {...defaultProps} />);

      const editButton = screen.getByTestId('edit-button-board2');
      await userEvent.click(editButton);

      expect(defaultProps.onBoardEdit).toHaveBeenCalledWith('board2');
    });

    it('deve chamar onShareBoard quando compartilhar', async () => {
      renderWithProviders(<DraggableBoardGrid {...defaultProps} />);

      const shareButton = screen.getByTestId('share-button-board3');
      await userEvent.click(shareButton);

      expect(defaultProps.onBoardShare).toHaveBeenCalledWith('board3');
    });
  });

  describe('Estados Especiais', () => {
    it('deve renderizar estado vazio quando não há boards', () => {
      const emptyProps = {
        ...defaultProps,
        boards: [],
      };

      renderWithProviders(<DraggableBoardGrid {...emptyProps} />);

      expect(screen.getByText('Nenhum board encontrado.')).toBeInTheDocument();
    });

    it('deve renderizar com boards de diferentes tipos', () => {
      renderWithProviders(<DraggableBoardGrid {...defaultProps} />);

      // Verifica que boards públicos e privados são renderizados
      expect(screen.getByText('Travel Photos')).toBeInTheDocument();
      expect(screen.getByText('Recipe Collection')).toBeInTheDocument();
      expect(screen.getByTestId('private-board2')).toBeInTheDocument();
    });
  });
}); 