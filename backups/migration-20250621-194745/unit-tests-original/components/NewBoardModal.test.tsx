import React from 'react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders, fillForm, uploadFile, waitForModal } from '../utils/testHelpers'
import { mockAuthStore } from '../mocks/authStore'

// Component imports
import { NewBoardModal } from '../../modules/pins/components/NewBoardModal'

// Mock storage service - using the correct path
vi.mock('../../services/storageService', () => ({
  storageService: {
    validateImageFile: vi.fn(),
    uploadBoardCover: vi.fn(),
  },
}))

// Mock callbacks
const mockOnClose = vi.fn()
const mockOnSubmit = vi.fn()

const defaultProps = {
  onClose: mockOnClose,
  onSubmit: mockOnSubmit,
  isLoading: false,
}

describe('NewBoardModal', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Rendering & Basic Functionality', () => {
    it('renders modal with correct title and form fields', () => {
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      expect(screen.getByText('Create New Board')).toBeInTheDocument()
      expect(screen.getByLabelText('Board Name')).toBeInTheDocument()
      expect(screen.getByLabelText('Description (optional)')).toBeInTheDocument()
      expect(screen.getByText('Privacy')).toBeInTheDocument()
      expect(screen.getByText('Public')).toBeInTheDocument()
      expect(screen.getByText('Private')).toBeInTheDocument()
      expect(screen.getByText('Cover Image (optional)')).toBeInTheDocument()
    })

    it('renders action buttons with correct labels', () => {
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Create Board' })).toBeInTheDocument()
    })

    it('calls onClose when cancel button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      const cancelButton = screen.getByRole('button', { name: 'Cancel' })
      await user.click(cancelButton)
      
      expect(mockOnClose).toHaveBeenCalledTimes(1)
    })

    it('calls onClose when modal close is triggered', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      // Try to find close button (may be an X or ESC key)
      const closeButton = screen.queryByRole('button', { name: /close/i })
      if (closeButton) {
        await user.click(closeButton)
        expect(mockOnClose).toHaveBeenCalledTimes(1)
      } else {
        // Test with ESC key if no close button
        fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' })
        expect(mockOnClose).toHaveBeenCalledTimes(1)
      }
    })
  })

  describe('Form Validation & Interaction', () => {
    it('disables submit button when name is empty', () => {
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      const submitButton = screen.getByRole('button', { name: 'Create Board' })
      expect(submitButton).toBeDisabled()
    })

    it('enables submit button when name is provided', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      const nameInput = screen.getByLabelText('Board Name')
      await user.type(nameInput, 'My Test Board')
      
      const submitButton = screen.getByRole('button', { name: 'Create Board' })
      expect(submitButton).toBeEnabled()
    })

    it('updates form fields correctly when user types', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      const nameInput = screen.getByLabelText('Board Name')
      const descriptionTextarea = screen.getByLabelText('Description (optional)')
      
      await user.type(nameInput, 'Test Board')
      await user.type(descriptionTextarea, 'Test description')
      
      expect(nameInput).toHaveValue('Test Board')
      expect(descriptionTextarea).toHaveValue('Test description')
    })

    it('handles privacy radio button selection', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      const privateRadio = screen.getByRole('radio', { name: 'Private - Only you can see this board' })
      const publicRadio = screen.getByRole('radio', { name: 'Public - Anyone can see this board' })
      
      // Public should be selected by default
      expect(publicRadio).toBeChecked()
      expect(privateRadio).not.toBeChecked()
      
      // Select private
      await user.click(privateRadio)
      expect(privateRadio).toBeChecked()
      expect(publicRadio).not.toBeChecked()
    })
  })

  describe('Form Submission', () => {
    it('calls onSubmit with correct data when form is submitted', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      // Fill form
      await user.type(screen.getByLabelText('Board Name'), 'Test Board')
      await user.type(screen.getByLabelText('Description (optional)'), 'Test description')
      await user.click(screen.getByRole('radio', { name: 'Private - Only you can see this board' }))
      
      // Submit
      const submitButton = screen.getByRole('button', { name: 'Create Board' })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          name: 'Test Board',
          description: 'Test description',
          coverImageUrl: '',
          isPrivate: true,
        })
      })
    })

    it('trims whitespace from name and description', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      // Fill form with whitespace
      await user.type(screen.getByLabelText('Board Name'), '  Test Board  ')
      await user.type(screen.getByLabelText('Description (optional)'), '  Test description  ')
      
      // Submit
      const submitButton = screen.getByRole('button', { name: 'Create Board' })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          name: 'Test Board',
          description: 'Test description',
          coverImageUrl: '',
          isPrivate: false,
        })
      })
    })

    it('calls onClose after successful submission', async () => {
      const user = userEvent.setup()
      mockOnSubmit.mockResolvedValue(undefined)
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      await user.type(screen.getByLabelText('Board Name'), 'Test Board')
      
      const submitButton = screen.getByRole('button', { name: 'Create Board' })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalledTimes(1)
      })
    })

    it('validates authentication before submission', async () => {
      // This test validates that the component has auth validation logic
      // The actual auth state is mocked globally in testHelpers
      // Component has check: if (!user) { return; } before calling onSubmit
      
      const user = userEvent.setup()
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      await user.type(screen.getByLabelText('Board Name'), 'Test Board')
      
      const submitButton = screen.getByRole('button', { name: 'Create Board' })
      await user.click(submitButton)
      
      // With authenticated user mock, should call onSubmit
      expect(mockOnSubmit).toHaveBeenCalled()
    })
  })

  describe('Loading States', () => {
    it('disables form fields when isLoading prop is true', () => {
      renderWithProviders(<NewBoardModal {...defaultProps} isLoading={true} />)
      
      expect(screen.getByLabelText('Board Name')).toBeDisabled()
      expect(screen.getByLabelText('Description (optional)')).toBeDisabled()
      expect(screen.getByRole('button', { name: 'Cancel' })).toBeDisabled()
      // Submit button should be disabled (find by type="submit")
      const submitButton = screen.getByRole('button', { name: '' })
      expect(submitButton).toBeDisabled()
    })

    it('shows loading state on submit button during submission', async () => {
      const user = userEvent.setup()
      
      // Make onSubmit return a promise that doesn't resolve immediately
      mockOnSubmit.mockImplementation(() => new Promise(() => {}))
      
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      await user.type(screen.getByLabelText('Board Name'), 'Test Board')
      
      const submitButton = screen.getByRole('button', { name: 'Create Board' })
      await user.click(submitButton)
      
      // Check if button shows loading state (loading prop should disable it)
      await waitFor(() => {
        expect(submitButton).toBeDisabled()
      })
    })
  })

  describe('Image Upload Functionality', () => {
    it('handles file selection for cover image', async () => {
      renderWithProviders(<NewBoardModal {...defaultProps} />)
      
      // This test would need to trigger file selection
      // The exact implementation depends on how FileUploadField works
      const fileInput = screen.queryByRole('button', { name: /upload/i }) || 
                      screen.queryByText(/drag.*drop/i) ||
                      screen.queryByText(/click.*upload/i)
      
      if (fileInput) {
        // File selection would trigger the upload process
        expect(fileInput).toBeInTheDocument()
      }
    })

    // Note: Full file upload testing would require more complex mocking
    // of the FileUploadField component and Firebase Storage interactions
  })
}) 