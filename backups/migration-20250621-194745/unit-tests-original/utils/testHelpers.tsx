import React, { ReactElement } from 'react'
import { render, RenderOptions, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi, expect } from 'vitest'
import { mockAuthStore } from '../mocks/authStore'

// Mock do React Router
export const mockNavigate = vi.fn()
export const mockLocation = {
  pathname: '/my-pins',
  search: '',
  hash: '',
  state: null,
  key: 'default',
}

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => mockLocation,
    useParams: () => ({}),
  }
})

// Mock do Auth Store
vi.mock('../../store/authStore', () => ({
  useAuthStore: () => mockAuthStore,
}))

// Custom render com providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[]
}

export function renderWithProviders(
  ui: ReactElement,
  {
    initialEntries = ['/'],
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  // Criar um novo QueryClient para cada teste para evitar cache entre testes
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          {children}
        </BrowserRouter>
      </QueryClientProvider>
    )
  }

  return {
    user: userEvent.setup(),
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  }
}

// Helper para esperar por loading states
export const waitForLoadingToFinish = () =>
  waitFor(() => {
    expect(screen.queryByText(/loading/i)).not.toBeInTheDocument()
    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument()
  })

// Helper para preencher formulários
export const fillForm = async (user: ReturnType<typeof userEvent.setup>, fields: Record<string, string>) => {
  for (const [fieldName, value] of Object.entries(fields)) {
    const field = screen.getByLabelText(new RegExp(fieldName, 'i')) || 
                  screen.getByPlaceholderText(new RegExp(fieldName, 'i')) ||
                  screen.getByRole('textbox', { name: new RegExp(fieldName, 'i') })
    
    await user.clear(field)
    await user.type(field, value)
  }
}

// Helper para drag and drop
export const dragAndDrop = async (
  dragElement: HTMLElement,
  dropElement: HTMLElement
) => {
  fireEvent.dragStart(dragElement)
  fireEvent.dragEnter(dropElement)
  fireEvent.dragOver(dropElement)
  fireEvent.drop(dropElement)
  fireEvent.dragEnd(dragElement)
}

// Helper para simular upload de arquivo
export const uploadFile = async (
  user: ReturnType<typeof userEvent.setup>,
  input: HTMLElement,
  file: File
) => {
  await user.upload(input, file)
}

// Helper para criar arquivo mock
export const createMockFile = (
  name = 'test.jpg',
  type = 'image/jpeg',
  size = 1024
): File => {
  return new File(['mock file content'], name, { type, lastModified: Date.now() })
}

// Helper para simular clique em modal backdrop
export const clickModalBackdrop = () => {
  const backdrop = screen.getByTestId('modal-backdrop') || 
                   document.querySelector('[data-testid="modal-backdrop"]') ||
                   document.querySelector('.modal-backdrop')
  
  if (backdrop) {
    fireEvent.click(backdrop)
  }
}

// Helper para aguardar modal abrir/fechar
export const waitForModal = async (shouldBeOpen = true) => {
  if (shouldBeOpen) {
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument()
    })
  } else {
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    })
  }
}

// Helper para simular scroll
export const simulateScroll = (element: HTMLElement, scrollTop: number) => {
  Object.defineProperty(element, 'scrollTop', {
    writable: true,
    value: scrollTop,
  })
  fireEvent.scroll(element)
}

// Helper para aguardar debounce
export const waitForDebounce = (ms = 300) => 
  new Promise(resolve => setTimeout(resolve, ms))

// Helper para mock de IntersectionObserver específico
export const mockIntersectionObserver = (isIntersecting = true) => {
  const mockObserver = vi.fn().mockImplementation((callback) => ({
    observe: vi.fn().mockImplementation((element) => {
      // Simula entrada/saída da viewport
      callback([{ target: element, isIntersecting }])
    }),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))
  
  global.IntersectionObserver = mockObserver
  return mockObserver
}

// Helper para aguardar animações CSS
export const waitForAnimation = (ms = 300) =>
  new Promise(resolve => setTimeout(resolve, ms))

// Helper para verificar acessibilidade básica
export const checkBasicA11y = async (element: HTMLElement) => {
  // Verifica se elementos interativos têm labels
  const buttons = element.querySelectorAll('button')
  buttons.forEach(button => {
    expect(button).toHaveAccessibleName()
  })
  
  // Verifica se inputs têm labels
  const inputs = element.querySelectorAll('input')
  inputs.forEach(input => {
    if (input.type !== 'hidden') {
      expect(input).toHaveAccessibleName()
    }
  })
}

// Re-export das principais funções para conveniência
export { screen, fireEvent, waitFor, userEvent }
export * from '@testing-library/react' 