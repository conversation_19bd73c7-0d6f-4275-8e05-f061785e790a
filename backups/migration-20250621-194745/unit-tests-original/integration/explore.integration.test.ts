import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';

/**
 * Integration Tests for Explore System
 * 
 * These tests verify the complete flow of the explore functionality,
 * from API endpoints to frontend interactions.
 */

describe('Explore System Integration', () => {
  const API_BASE = 'http://localhost:3001';
  const TEST_USER_ID = 'gMcFplcYxxMGzoHcmmZlNngSl0u1';
  
  beforeAll(async () => {
    // Ensure server is running
    try {
      const response = await fetch(`${API_BASE}/api/pins?limit=1`);
      if (!response.ok) {
        throw new Error('Server not running');
      }
    } catch (error) {
      console.error('⚠️ Server not running. Start with: POSTGRES_PORT=5433 node server.js');
      throw error;
    }
  });

  describe('Pins API Endpoints', () => {
    it('should fetch pins with pagination', async () => {
      const response = await fetch(`${API_BASE}/api/pins?limit=2&offset=0`);
      
      expect(response.ok).toBe(true);
      expect(response.status).toBe(200);
      
      const pins = await response.json();
      expect(Array.isArray(pins)).toBe(true);
      expect(pins.length).toBeLessThanOrEqual(2);
      
      if (pins.length > 0) {
        const pin = pins[0];
        expect(pin).toHaveProperty('id');
        expect(pin).toHaveProperty('name');
        expect(pin).toHaveProperty('image');
        expect(pin).toHaveProperty('likes');
        expect(pin).toHaveProperty('comments');
        expect(pin).toHaveProperty('isLiked');
        expect(pin).toHaveProperty('isSaved');
        expect(pin).toHaveProperty('owner');
      }
    });

    it('should fetch pins with user-specific states', async () => {
      const response = await fetch(`${API_BASE}/api/pins?limit=1&userId=${TEST_USER_ID}`);
      
      expect(response.ok).toBe(true);
      
      const pins = await response.json();
      if (pins.length > 0) {
        const pin = pins[0];
        expect(typeof pin.isLiked).toBe('boolean');
        expect(typeof pin.isSaved).toBe('boolean');
      }
    });

    it('should handle different sort orders', async () => {
      const newestResponse = await fetch(`${API_BASE}/api/pins?limit=5&sortBy=newest`);
      const popularResponse = await fetch(`${API_BASE}/api/pins?limit=5&sortBy=popular`);
      
      expect(newestResponse.ok).toBe(true);
      expect(popularResponse.ok).toBe(true);
      
      const newestPins = await newestResponse.json();
      const popularPins = await popularResponse.json();
      
      expect(Array.isArray(newestPins)).toBe(true);
      expect(Array.isArray(popularPins)).toBe(true);
    });
  });

  describe('Like Functionality', () => {
    let testPinId: string;

    beforeEach(async () => {
      // Get a pin to test with
      const response = await fetch(`${API_BASE}/api/pins?limit=1`);
      const pins = await response.json();
      if (pins.length > 0) {
        testPinId = pins[0].id;
      }
    });

    it('should like a pin successfully', async () => {
      if (!testPinId) {
        console.warn('No pins available for testing');
        return;
      }

      const response = await fetch(`${API_BASE}/api/pins/${testPinId}/like`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: TEST_USER_ID,
          isLiked: true
        })
      });

      expect(response.ok).toBe(true);
      
      const result = await response.json();
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('isLiked', true);
      expect(result).toHaveProperty('likesCount');
      expect(typeof result.likesCount).toBe('number');
    });

    it('should unlike a pin successfully', async () => {
      if (!testPinId) {
        console.warn('No pins available for testing');
        return;
      }

      // First like it
      await fetch(`${API_BASE}/api/pins/${testPinId}/like`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: TEST_USER_ID,
          isLiked: true
        })
      });

      // Then unlike it
      const response = await fetch(`${API_BASE}/api/pins/${testPinId}/like`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: TEST_USER_ID,
          isLiked: false
        })
      });

      expect(response.ok).toBe(true);
      
      const result = await response.json();
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('isLiked', false);
    });

    it('should persist like state', async () => {
      if (!testPinId) {
        console.warn('No pins available for testing');
        return;
      }

      // Like the pin
      await fetch(`${API_BASE}/api/pins/${testPinId}/like`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: TEST_USER_ID,
          isLiked: true
        })
      });

      // Verify the state is persisted
      const response = await fetch(`${API_BASE}/api/pins?limit=50&userId=${TEST_USER_ID}`);
      const pins = await response.json();
      const likedPin = pins.find((p: any) => p.id === testPinId);
      
      expect(likedPin).toBeDefined();
      expect(likedPin.isLiked).toBe(true);
    });
  });

  describe('Save Functionality', () => {
    let testPinId: string;

    beforeEach(async () => {
      // Get a pin to test with
      const response = await fetch(`${API_BASE}/api/pins?limit=1`);
      const pins = await response.json();
      if (pins.length > 0) {
        testPinId = pins[0].id;
      }
    });

    it('should save a pin successfully', async () => {
      if (!testPinId) {
        console.warn('No pins available for testing');
        return;
      }

      const response = await fetch(`${API_BASE}/api/pins/${testPinId}/save`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: TEST_USER_ID,
          isSaved: true
        })
      });

      expect(response.ok).toBe(true);
      
      const result = await response.json();
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('isSaved', true);
    });

    it('should unsave a pin successfully', async () => {
      if (!testPinId) {
        console.warn('No pins available for testing');
        return;
      }

      // First save it
      await fetch(`${API_BASE}/api/pins/${testPinId}/save`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: TEST_USER_ID,
          isSaved: true
        })
      });

      // Then unsave it
      const response = await fetch(`${API_BASE}/api/pins/${testPinId}/save`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: TEST_USER_ID,
          isSaved: false
        })
      });

      expect(response.ok).toBe(true);
      
      const result = await response.json();
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('isSaved', false);
    });

    it('should persist save state', async () => {
      if (!testPinId) {
        console.warn('No pins available for testing');
        return;
      }

      // Save the pin
      await fetch(`${API_BASE}/api/pins/${testPinId}/save`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: TEST_USER_ID,
          isSaved: true
        })
      });

      // Verify the state is persisted
      const response = await fetch(`${API_BASE}/api/pins?limit=50&userId=${TEST_USER_ID}`);
      const pins = await response.json();
      const savedPin = pins.find((p: any) => p.id === testPinId);
      
      expect(savedPin).toBeDefined();
      expect(savedPin.isSaved).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid pin ID for like', async () => {
      const response = await fetch(`${API_BASE}/api/pins/invalid-id/like`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: TEST_USER_ID,
          isLiked: true
        })
      });

      // Should handle gracefully (might be 404 or 500 depending on implementation)
      expect([404, 500].includes(response.status)).toBe(true);
    });

    it('should handle missing request body', async () => {
      const response = await fetch(`${API_BASE}/api/pins?limit=1`);
      const pins = await response.json();
      
      if (pins.length > 0) {
        const testPinId = pins[0].id;
        
        const likeResponse = await fetch(`${API_BASE}/api/pins/${testPinId}/like`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}) // Missing required fields
        });

        expect(likeResponse.status).toBeGreaterThanOrEqual(400);
      }
    });
  });

  describe('Data Consistency', () => {
    it('should maintain consistent like counts', async () => {
      const response = await fetch(`${API_BASE}/api/pins?limit=1`);
      const pins = await response.json();
      
      if (pins.length > 0) {
        const testPinId = pins[0].id;
        const initialLikes = pins[0].likes || 0;

        // Like the pin
        const likeResponse = await fetch(`${API_BASE}/api/pins/${testPinId}/like`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId: TEST_USER_ID,
            isLiked: true
          })
        });

        const likeResult = await likeResponse.json();
        expect(likeResult.likesCount).toBeGreaterThan(initialLikes);

        // Verify the count in the pins list
        const updatedResponse = await fetch(`${API_BASE}/api/pins?limit=50`);
        const updatedPins = await updatedResponse.json();
        const updatedPin = updatedPins.find((p: any) => p.id === testPinId);
        
        expect(updatedPin.likes).toBe(likeResult.likesCount);
      }
    });
  });

  describe('Performance', () => {
    it('should respond to pins API within reasonable time', async () => {
      const startTime = Date.now();
      
      const response = await fetch(`${API_BASE}/api/pins?limit=30`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.ok).toBe(true);
      expect(responseTime).toBeLessThan(2000); // Should respond within 2 seconds
    });

    it('should handle large pagination efficiently', async () => {
      const startTime = Date.now();
      
      const response = await fetch(`${API_BASE}/api/pins?limit=100&offset=0`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.ok).toBe(true);
      expect(responseTime).toBeLessThan(3000); // Should handle large requests within 3 seconds
    });
  });
}); 