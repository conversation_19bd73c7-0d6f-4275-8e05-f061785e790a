import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useNotifications } from '../../hooks/useNotifications';
import { notificationService, Notification } from '../../services/notificationService';

// Mock the notification service
vi.mock('../../services/notificationService', () => ({
  notificationService: {
    getUserNotifications: vi.fn(),
    getUnreadCount: vi.fn(),
    markAsRead: vi.fn(),
    markAllAsRead: vi.fn(),
    deleteNotification: vi.fn(),
    deleteAllNotifications: vi.fn(),
    deleteBulkNotifications: vi.fn(),
    addListener: vi.fn(),
    requestPermission: vi.fn(),
  }
}));

const mockNotificationService = notificationService as any;

describe('useNotifications', () => {
  const mockUserId = 'user123';
  const mockNotifications: Notification[] = [
    {
      id: '1',
      type: 'like',
      title: 'New like',
      content: 'Someone liked your pin',
      fromUser: { id: '2', name: '<PERSON>', username: 'john' },
      isRead: false,
      timestamp: '2025-01-01T00:00:00Z'
    },
    {
      id: '2',
      type: 'comment',
      title: 'New comment',
      content: 'Someone commented on your pin',
      fromUser: { id: '3', name: 'Jane', username: 'jane', avatar: '/avatar.jpg' },
      isRead: true,
      timestamp: '2025-01-01T01:00:00Z'
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock implementations
    mockNotificationService.getUserNotifications.mockResolvedValue(mockNotifications);
    mockNotificationService.getUnreadCount.mockResolvedValue(1);
    mockNotificationService.markAsRead.mockResolvedValue(true);
    mockNotificationService.markAllAsRead.mockResolvedValue(2);
    mockNotificationService.deleteNotification.mockResolvedValue(true);
    mockNotificationService.deleteAllNotifications.mockResolvedValue(2);
    mockNotificationService.deleteBulkNotifications.mockResolvedValue(2);
    mockNotificationService.requestPermission.mockResolvedValue(true);
    
    // Mock addListener to return an unsubscribe function
    mockNotificationService.addListener.mockReturnValue(() => {});
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useNotifications(mockUserId));

      expect(result.current.notifications).toEqual([]);
      expect(result.current.unreadCount).toBe(0);
      expect(result.current.loading).toBe(true);
      expect(result.current.error).toBeNull();
    });

    it('should load notifications on mount', async () => {
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(mockNotificationService.getUserNotifications).toHaveBeenCalledWith(
        mockUserId,
        {}
      );
      expect(mockNotificationService.getUnreadCount).toHaveBeenCalledWith(mockUserId);
      expect(result.current.notifications).toEqual(mockNotifications);
      expect(result.current.unreadCount).toBe(1);
    });

    it('should handle loading error gracefully', async () => {
      const errorMessage = 'Failed to load notifications';
      mockNotificationService.getUserNotifications.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.error).toBe('Failed to load notifications');
      expect(result.current.notifications).toEqual([]);
    });

    it('should not load notifications if no userId provided', () => {
      renderHook(() => useNotifications(''));

      expect(mockNotificationService.getUserNotifications).not.toHaveBeenCalled();
      expect(mockNotificationService.getUnreadCount).not.toHaveBeenCalled();
    });
  });

  describe('loadMore', () => {
    it('should load more notifications with correct offset', async () => {
      const { result } = renderHook(() => useNotifications(mockUserId));

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Mock additional notifications
      const additionalNotifications: Notification[] = [
        {
          id: '3',
          type: 'follow',
          title: 'New follower',
          content: 'Someone started following you',
          fromUser: { id: '4', name: 'Bob', username: 'bob' },
          isRead: false,
          timestamp: '2025-01-01T02:00:00Z'
        }
      ];
      
      mockNotificationService.getUserNotifications.mockResolvedValueOnce(additionalNotifications);

      await act(async () => {
        await result.current.loadMore(2);
      });

      expect(mockNotificationService.getUserNotifications).toHaveBeenCalledWith(
        mockUserId,
        { limit: 30, offset: 2 }
      );
      expect(result.current.notifications).toHaveLength(3);
    });

    it('should handle load more error gracefully', async () => {
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      mockNotificationService.getUserNotifications.mockRejectedValueOnce(
        new Error('Load more failed')
      );

      await act(async () => {
        const result_loadMore = await result.current.loadMore(2);
        expect(result_loadMore).toEqual([]);
      });
    });
  });

  describe('markAsRead', () => {
    it('should mark notification as read and update state', async () => {
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.markAsRead('1');
      });

      expect(mockNotificationService.markAsRead).toHaveBeenCalledWith('1', mockUserId);
      
      // Check that the notification is marked as read in local state
      const notification = result.current.notifications.find(n => n.id === '1');
      expect(notification?.isRead).toBe(true);
      expect(result.current.unreadCount).toBe(0);
    });

    it('should handle mark as read error gracefully', async () => {
      mockNotificationService.markAsRead.mockResolvedValueOnce(false);
      
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.markAsRead('1');
      });

      // State should not change if marking failed
      const notification = result.current.notifications.find(n => n.id === '1');
      expect(notification?.isRead).toBe(false);
    });
  });

  describe('markAllAsRead', () => {
    it('should mark all notifications as read', async () => {
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.markAllAsRead();
      });

      expect(mockNotificationService.markAllAsRead).toHaveBeenCalledWith(mockUserId);
      expect(result.current.unreadCount).toBe(0);
      
      // All notifications should be marked as read
      result.current.notifications.forEach(notification => {
        expect(notification.isRead).toBe(true);
      });
    });

    it('should handle mark all as read error gracefully', async () => {
      mockNotificationService.markAllAsRead.mockResolvedValueOnce(0);
      
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const originalUnreadCount = result.current.unreadCount;

      await act(async () => {
        await result.current.markAllAsRead();
      });

      // State should not change if marking failed
      expect(result.current.unreadCount).toBe(originalUnreadCount);
    });
  });

  describe('deleteNotification', () => {
    it('should delete notification and update state', async () => {
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.deleteNotification('1');
      });

      expect(mockNotificationService.deleteNotification).toHaveBeenCalledWith('1', mockUserId);
      expect(result.current.notifications).toHaveLength(1);
      expect(result.current.notifications.find(n => n.id === '1')).toBeUndefined();
    });

    it('should handle delete notification error gracefully', async () => {
      mockNotificationService.deleteNotification.mockResolvedValueOnce(false);
      
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const originalLength = result.current.notifications.length;

      await act(async () => {
        await result.current.deleteNotification('1');
      });

      // State should not change if deletion failed
      expect(result.current.notifications).toHaveLength(originalLength);
    });
  });

  describe('deleteAllNotifications', () => {
    it('should delete all notifications', async () => {
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.deleteAllNotifications();
      });

      expect(mockNotificationService.deleteAllNotifications).toHaveBeenCalledWith(mockUserId);
      expect(result.current.notifications).toEqual([]);
      expect(result.current.unreadCount).toBe(0);
    });
  });

  describe('deleteBulkNotifications', () => {
    it('should delete selected notifications', async () => {
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.deleteBulkNotifications(['1', '2']);
      });

      expect(mockNotificationService.deleteBulkNotifications).toHaveBeenCalledWith(
        ['1', '2'],
        mockUserId
      );
      expect(result.current.notifications).toEqual([]);
    });
  });

  describe('refreshNotifications', () => {
    it('should refresh notifications and unread count', async () => {
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Clear previous calls
      vi.clearAllMocks();

      await act(async () => {
        result.current.refreshNotifications();
      });

      expect(mockNotificationService.getUserNotifications).toHaveBeenCalledWith(
        mockUserId,
        {}
      );
      expect(mockNotificationService.getUnreadCount).toHaveBeenCalledWith(mockUserId);
    });
  });

  describe('loadNotifications with options', () => {
    it('should load notifications with custom options', async () => {
      const { result } = renderHook(() => useNotifications(mockUserId));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const options = { limit: 10, offset: 5, type: 'like', unreadOnly: true };

      await act(async () => {
        await result.current.loadNotifications(options);
      });

      expect(mockNotificationService.getUserNotifications).toHaveBeenCalledWith(
        mockUserId,
        options
      );
    });
  });

  describe('real-time updates', () => {
    it('should setup listener on mount and cleanup on unmount', () => {
      const unsubscribeMock = vi.fn();
      mockNotificationService.addListener.mockReturnValue(unsubscribeMock);

      const { unmount } = renderHook(() => useNotifications(mockUserId));

      expect(mockNotificationService.addListener).toHaveBeenCalled();
      expect(mockNotificationService.requestPermission).toHaveBeenCalled();

      unmount();

      expect(unsubscribeMock).toHaveBeenCalled();
    });
  });
}); 