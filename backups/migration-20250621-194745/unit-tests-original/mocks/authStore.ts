import { vi } from 'vitest'
import { mockUser } from './handlers'

// Mock do store de autenticação
export const mockAuthStore = {
  user: mockUser,
  isAuthenticated: true,
  isLoading: false,
  error: null,
  login: vi.fn().mockResolvedValue(mockUser),
  logout: vi.fn().mockResolvedValue(undefined),
  register: vi.fn().mockResolvedValue(mockUser),
  updateProfile: vi.fn().mockResolvedValue(mockUser),
  clearError: vi.fn(),
  checkAuth: vi.fn().mockResolvedValue(mockUser),
}

// Mock para estado não autenticado
export const mockUnauthenticatedStore = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  login: vi.fn(),
  logout: vi.fn(),
  register: vi.fn(),
  updateProfile: vi.fn(),
  clearError: vi.fn(),
  checkAuth: vi.fn(),
}

// Mock para estado de loading
export const mockLoadingAuthStore = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
  login: vi.fn(),
  logout: vi.fn(),
  register: vi.fn(),
  updateProfile: vi.fn(),
  clearError: vi.fn(),
  checkAuth: vi.fn(),
}

// Mock para estado de erro
export const mockErrorAuthStore = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: 'Authentication failed',
  login: vi.fn().mockRejectedValue(new Error('Authentication failed')),
  logout: vi.fn(),
  register: vi.fn().mockRejectedValue(new Error('Registration failed')),
  updateProfile: vi.fn(),
  clearError: vi.fn(),
  checkAuth: vi.fn().mockRejectedValue(new Error('Auth check failed')),
} 