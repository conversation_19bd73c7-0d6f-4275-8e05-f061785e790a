import { http, HttpResponse } from 'msw'

// Mock data
export const mockUser = {
  id: 'user1',
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  avatar: 'https://example.com/avatar.jpg',
  createdAt: '2024-01-01T00:00:00.000Z',
}

export const mockBoard = {
  id: 'board1',
  name: 'My Board 1',
  description: 'Test board description',
  isPrivate: false,
  userId: 'user1',
  pinCount: 5,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-15T00:00:00.000Z',
  coverImage: 'https://example.com/board1.jpg',
}

export const mockBoards = [
  {
    id: 'board1',
    name: 'My Board 1',
    description: 'First test board',
    isPrivate: false,
    userId: 'user1',
    pinCount: 5,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-15T00:00:00.000Z',
    coverImage: 'https://example.com/board1.jpg',
  },
  {
    id: 'board2',
    name: 'My Board 2',
    description: 'Second test board',
    isPrivate: true,
    userId: 'user1',
    pinCount: 3,
    createdAt: '2024-01-02T00:00:00.000Z',
    updatedAt: '2024-01-10T00:00:00.000Z',
    coverImage: 'https://example.com/board2.jpg',
  },
]

export const mockPin = {
  id: 'pin-1',
  title: 'Test Pin',
  description: 'Test pin description',
  imageUrl: 'https://example.com/test-image.jpg',
  sourceUrl: 'https://example.com/source',
  userId: 'user-1',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  tags: ['test', 'mock'],
  boards: [mockBoard],
  user: mockUser,
  _count: { comments: 3, likes: 10 },
}

export const mockComment = {
  id: 'comment-1',
  content: 'Test comment',
  pinId: 'pin-1',
  userId: 'user-1',
  user: mockUser,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
}

// Mock notification data
export const mockNotifications = [
  {
    id: '1',
    type: 'like',
    title: 'New like',
    content: 'Someone liked your pin',
    fromUser: { id: '2', name: 'John', username: 'john' },
    isRead: false,
    timestamp: '2025-01-01T00:00:00Z'
  },
  {
    id: '2',
    type: 'comment',
    title: 'New comment',
    content: 'Someone commented on your pin',
    fromUser: { id: '3', name: 'Jane', username: 'jane', avatar: '/avatar.jpg' },
    isRead: true,
    timestamp: '2025-01-01T01:00:00Z'
  }
]

export const mockNotification = {
  id: '123',
  type: 'like',
  title: 'Test',
  content: 'Test notification',
  fromUser: { id: '2', name: 'John', username: 'john' },
  isRead: false,
  timestamp: '2025-01-01T00:00:00Z'
}

// API Handlers
export const handlers = [
  // Auth endpoints
  http.get('/api/auth/me', () => {
    return HttpResponse.json(mockUser)
  }),

  http.post('/api/auth/login', () => {
    return HttpResponse.json({ user: mockUser, token: 'mock-token' })
  }),

  http.post('/api/auth/logout', () => {
    return HttpResponse.json({ success: true })
  }),

  // MyPinsPage specific endpoints - WITH FULL URL including protocol
  http.get('http://localhost:3001/api/boards/user/:userId', ({ params }) => {
    console.log('🎯 Mock handler intercepted getUserBoards request for userId:', params.userId);
    return HttpResponse.json(mockBoards);
  }),

  http.post('http://localhost:3001/api/boards', async ({ request }) => {
    const body = await request.json() as any
    return HttpResponse.json({
      ...mockBoard,
      id: 'new-board-id',
      name: body.name,
      description: body.description,
    })
  }),

  http.put('http://localhost:3001/api/boards/:boardId', async ({ params, request }) => {
    const body = await request.json() as any
    return HttpResponse.json({
      ...mockBoard,
      id: params.boardId,
      ...body,
    })
  }),

  http.delete('http://localhost:3001/api/boards/:boardId', () => {
    return HttpResponse.json({ success: true })
  }),

  http.put('http://localhost:3001/api/boards/user/:userId/order', () => {
    return HttpResponse.json({ success: true })
  }),

  // Notification endpoints
  http.get('http://localhost:3001/api/notifications/:userId', ({ params, request }) => {
    console.log('🎯 Mock handler intercepted getUserNotifications request for userId:', params.userId);
    const url = new URL(request.url);
    const limit = url.searchParams.get('limit');
    const offset = url.searchParams.get('offset');
    const type = url.searchParams.get('type');
    const unreadOnly = url.searchParams.get('unread_only');
    
    let filteredNotifications = [...mockNotifications];
    
    if (type) {
      filteredNotifications = filteredNotifications.filter(n => n.type === type);
    }
    
    if (unreadOnly === 'true') {
      filteredNotifications = filteredNotifications.filter(n => !n.isRead);
    }
    
    return HttpResponse.json(filteredNotifications);
  }),

  http.get('http://localhost:3001/api/notifications/:userId/unread-count', ({ params }) => {
    console.log('🎯 Mock handler intercepted getUnreadCount request for userId:', params.userId);
    const unreadCount = mockNotifications.filter(n => !n.isRead).length;
    return HttpResponse.json({ count: unreadCount });
  }),

  http.put('http://localhost:3001/api/notifications/:notificationId/read', ({ params }) => {
    console.log('🎯 Mock handler intercepted markAsRead request for notificationId:', params.notificationId);
    return HttpResponse.json({ success: true });
  }),

  http.put('http://localhost:3001/api/notifications/mark-all-read/:userId', ({ params }) => {
    console.log('🎯 Mock handler intercepted markAllAsRead request for userId:', params.userId);
    const unreadCount = mockNotifications.filter(n => !n.isRead).length;
    return HttpResponse.json({ updatedCount: unreadCount });
  }),

  http.post('http://localhost:3001/api/notifications', async ({ request }) => {
    console.log('🎯 Mock handler intercepted createNotification request');
    const body = await request.json() as any;
    return HttpResponse.json({
      ...mockNotification,
      id: 'new-notification-id',
      ...body
    });
  }),

  http.delete('http://localhost:3001/api/notifications/:notificationId', ({ params }) => {
    console.log('🎯 Mock handler intercepted deleteNotification request for notificationId:', params.notificationId);
    return HttpResponse.json({ success: true });
  }),

  http.delete('http://localhost:3001/api/notifications/user/:userId', ({ params }) => {
    console.log('🎯 Mock handler intercepted deleteAllNotifications request for userId:', params.userId);
    const deletedCount = mockNotifications.length;
    return HttpResponse.json({ deletedCount });
  }),

  http.delete('http://localhost:3001/api/notifications/bulk', async ({ request }) => {
    console.log('🎯 Mock handler intercepted deleteBulkNotifications request');
    const body = await request.json() as any;
    const deletedCount = body.notificationIds?.length || 0;
    return HttpResponse.json({ deletedCount });
  }),

  // Error scenarios
  http.get('http://localhost:3001/api/boards/user/error-user', () => {
    return HttpResponse.json({ error: 'Internal server error' }, { status: 500 });
  }),

  // Fallback handlers for other URLs  
  http.get('/api/boards/:boardId', ({ params }) => {
    return HttpResponse.json(mockBoard)
  }),

  http.get('/api/pins/:pinId', ({ params }) => {
    return HttpResponse.json(mockPin)
  }),

  http.get('/api/users/:userId', ({ params }) => {
    return HttpResponse.json(mockUser)
  }),

  // 404 handlers
  http.get('*', ({ request }) => {
    console.log('🟡 Unhandled request:', request.url);
    return HttpResponse.json({ error: 'Not found' }, { status: 404 });
  }),
] 