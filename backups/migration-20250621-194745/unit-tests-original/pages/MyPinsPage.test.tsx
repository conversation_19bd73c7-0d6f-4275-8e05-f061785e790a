import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from '../utils/testHelpers';
import MyPinsPage from '../../modules/pins/MyPinsPage';

// Mock data and functions using vi.hoisted
const mockData = vi.hoisted(() => {
  const mockDeleteBoard = vi.fn();
  const mockUpdateBoardOrder = vi.fn();
  const mockUseUserBoards = vi.fn();
  const mockUseCreateBoard = vi.fn();
  const mockUseUpdateBoard = vi.fn();
  const mockUseAuthStore = vi.fn();
  
  const mockAuthenticatedUser = {
    user: { 
      id: 'user1', 
      name: 'Test User',
      email: '<EMAIL>'
    },
    isAuthenticated: true,
    isLoading: false,
    error: null,
  };

  return {
    mockDeleteBoard,
    mockUpdateBoardOrder,
    mockUseUserBoards,
    mockUseCreateBoard,
    mockUseUpdateBoard,
    mockUseAuthStore,
    mockAuthenticatedUser
  };
});

// Mock all dependencies
vi.mock('../../hooks/api/useApiQueries', () => ({
  useUserBoards: mockData.mockUseUserBoards,
  useCreateBoard: mockData.mockUseCreateBoard,
  useUpdateBoard: mockData.mockUseUpdateBoard,
}));

vi.mock('../../store/authStore', () => ({
  useAuthStore: mockData.mockUseAuthStore,
}));

vi.mock('../../services/api/boardsService', () => ({
  deleteBoard: mockData.mockDeleteBoard,
  updateBoardOrder: mockData.mockUpdateBoardOrder,
}));

// Mock toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('MyPinsPage', () => {
  const mockBoards = [
    {
      id: 'board1',
      name: 'My First Board',
      description: 'Test board',
      pinCount: 5,
      isPrivate: false,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-15',
    },
    {
      id: 'board2',
      name: 'Private Board', 
      description: 'Private test board',
      pinCount: 3,
      isPrivate: true,
      createdAt: '2024-01-02',
      updatedAt: '2024-01-10',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup mutation mocks
    mockData.mockUseCreateBoard.mockReturnValue({
      mutateAsync: vi.fn().mockResolvedValue({}),
      isLoading: false,
      error: null,
    });
    
    mockData.mockUseUpdateBoard.mockReturnValue({
      mutateAsync: vi.fn().mockResolvedValue({}),
      isLoading: false,
      error: null,
    });
  });

  describe('Estados Básicos', () => {
    it('deve exibir loading spinner quando carregando', () => {
      mockData.mockUseAuthStore.mockReturnValue(mockData.mockAuthenticatedUser);
      mockData.mockUseUserBoards.mockReturnValue({
        data: [],
        isLoading: true,
        error: null,
      });

      renderWithProviders(<MyPinsPage />);
      
      // O componente renderiza um spinner visual, não texto "loading"
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    it('deve exibir estado vazio quando usuário não tem boards', () => {
      mockData.mockUseAuthStore.mockReturnValue(mockData.mockAuthenticatedUser);
      mockData.mockUseUserBoards.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      renderWithProviders(<MyPinsPage />);
      
      // O componente mostra "Create your first board" não "no boards"
      expect(screen.getByText('Create your first board')).toBeInTheDocument();
    });

    it('deve carregar e exibir boards quando usuário autenticado', async () => {
      mockData.mockUseAuthStore.mockReturnValue(mockData.mockAuthenticatedUser);
      mockData.mockUseUserBoards.mockReturnValue({
        data: mockBoards,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<MyPinsPage />);
      
      await waitFor(() => {
        // O componente renderiza os nomes dos boards quando tem dados
        expect(screen.getByText('My First Board')).toBeInTheDocument();
        expect(screen.getByText('Private Board')).toBeInTheDocument();
      });
    });
  });

  describe('Interface Básica', () => {
    beforeEach(() => {
      mockData.mockUseAuthStore.mockReturnValue(mockData.mockAuthenticatedUser);
      mockData.mockUseUserBoards.mockReturnValue({
        data: mockBoards,
        isLoading: false,
        error: null,
      });
    });

    it('deve mostrar o título da página', () => {
      renderWithProviders(<MyPinsPage />);
      
      expect(screen.getByText('My Pinboards')).toBeInTheDocument();
    });

    it('deve mostrar botão de criar board', () => {
      renderWithProviders(<MyPinsPage />);
      
      expect(screen.getByRole('button', { name: /new board/i })).toBeInTheDocument();
    });

    it('deve mostrar campo de busca', () => {
      renderWithProviders(<MyPinsPage />);
      
      expect(screen.getByPlaceholderText(/search boards/i)).toBeInTheDocument();
    });

    it('deve exibir boards carregados', async () => {
      renderWithProviders(<MyPinsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('My First Board')).toBeInTheDocument();
        expect(screen.getByText('Private Board')).toBeInTheDocument();
      });
    });
  });

  describe('Funcionalidade de Busca', () => {
    beforeEach(() => {
      mockData.mockUseAuthStore.mockReturnValue(mockData.mockAuthenticatedUser);
      mockData.mockUseUserBoards.mockReturnValue({
        data: mockBoards,
        isLoading: false,
        error: null,
      });
    });

    it('deve permitir digitar no campo de busca', async () => {
      renderWithProviders(<MyPinsPage />);
      
      const searchInput = screen.getByPlaceholderText(/search boards/i);
      await userEvent.type(searchInput, 'First');
      
      expect(searchInput).toHaveValue('First');
    });

    it('deve limpar busca quando input for limpo', async () => {
      renderWithProviders(<MyPinsPage />);
      
      const searchInput = screen.getByPlaceholderText(/search boards/i);
      await userEvent.type(searchInput, 'test');
      await userEvent.clear(searchInput);
      
      expect(searchInput).toHaveValue('');
    });
  });
}); 