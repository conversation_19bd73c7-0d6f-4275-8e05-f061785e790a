import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from '../utils/testHelpers';
import MyPinsPage from '../../modules/pins/MyPinsPage';

// Mock data and functions using vi.hoisted
const mockData = vi.hoisted(() => {
  const mockUseUserBoards = vi.fn();
  const mockUseCreateBoard = vi.fn();
  const mockUseUpdateBoard = vi.fn();
  const mockUseAuthStore = vi.fn();
  const mockDeleteBoard = vi.fn();
  
  const mockAuthenticatedUser = {
    user: { 
      id: 'user1', 
      name: 'Test User',
      email: '<EMAIL>'
    },
    isAuthenticated: true,
    isLoading: false,
    error: null,
  };

  return {
    mockUseUserBoards,
    mockUseCreateBoard,
    mockUseUpdateBoard,
    mockUseAuthStore,
    mockDeleteBoard,
    mockAuthenticatedUser
  };
});

// Mock all dependencies
vi.mock('../../hooks/api/useApiQueries', () => ({
  useUserBoards: mockData.mockUseUserBoards,
  useCreateBoard: mockData.mockUseCreateBoard,
  useUpdateBoard: mockData.mockUseUpdateBoard,
}));

vi.mock('../../store/authStore', () => ({
  useAuthStore: mockData.mockUseAuthStore,
}));

vi.mock('../../services/api/boardsService', () => ({
  deleteBoard: mockData.mockDeleteBoard,
  updateBoardOrder: vi.fn(),
}));

// Mock toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('MyPinsPage', () => {
  const mockBoards = [
    {
      id: 'board1',
      name: 'My First Board',
      description: 'Test board',
      pinCount: 5,
      isPrivate: false,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-15',
    },
    {
      id: 'board2',
      name: 'Private Board', 
      description: 'Private test board',
      pinCount: 3,
      isPrivate: true,
      createdAt: '2024-01-02',
      updatedAt: '2024-01-10',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup padrão - usuário autenticado
    mockData.mockUseAuthStore.mockReturnValue(mockData.mockAuthenticatedUser);
    
    // Setup mutation mocks
    mockData.mockUseCreateBoard.mockReturnValue({
      mutateAsync: vi.fn().mockResolvedValue({}),
      isLoading: false,
      error: null,
    });
    
    mockData.mockUseUpdateBoard.mockReturnValue({
      mutateAsync: vi.fn().mockResolvedValue({}),
      isLoading: false,
      error: null,
    });
  });

  describe('Estados Básicos', () => {
    it('deve carregar e exibir boards quando usuário autenticado', async () => {
      mockData.mockUseUserBoards.mockReturnValue({
        data: mockBoards,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<MyPinsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('My First Board')).toBeInTheDocument();
        expect(screen.getByText('Private Board')).toBeInTheDocument();
      });
    });

    it('deve exibir loading quando carregando', () => {
      mockData.mockUseUserBoards.mockReturnValue({
        data: [],
        isLoading: true,
        error: null,
      });

      renderWithProviders(<MyPinsPage />);
      
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    it('deve exibir estado vazio quando não há boards', () => {
      mockData.mockUseUserBoards.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      renderWithProviders(<MyPinsPage />);
      
      expect(screen.getByText('Create your first board')).toBeInTheDocument();
    });

    it('deve exibir erro quando falha ao carregar', () => {
      mockData.mockUseUserBoards.mockReturnValue({
        data: [],
        isLoading: false,
        error: new Error('Failed to load boards'),
      });

      renderWithProviders(<MyPinsPage />);
      
      // Quando há erro, geralmente o componente ainda renderiza o main
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });

  describe('Funcionalidade de Busca', () => {
    beforeEach(() => {
      mockData.mockUseUserBoards.mockReturnValue({
        data: mockBoards,
        isLoading: false,
        error: null,
      });
    });

    it('deve permitir digitar no campo de busca', async () => {
      renderWithProviders(<MyPinsPage />);
      
      const searchInput = screen.getByPlaceholderText(/search boards/i);
      await userEvent.type(searchInput, 'First');
      
      expect(searchInput).toHaveValue('First');
    });

    it('deve limpar busca quando input for limpo', async () => {
      renderWithProviders(<MyPinsPage />);
      
      const searchInput = screen.getByPlaceholderText(/search boards/i);
      await userEvent.type(searchInput, 'test');
      await userEvent.clear(searchInput);
      
      expect(searchInput).toHaveValue('');
    });
  });

  describe('Interface', () => {
    beforeEach(() => {
      mockData.mockUseUserBoards.mockReturnValue({
        data: mockBoards,
        isLoading: false,
        error: null,
      });
    });

    it('deve mostrar botão de criar board', () => {
      renderWithProviders(<MyPinsPage />);
      
      expect(screen.getByRole('button', { name: /new board/i })).toBeInTheDocument();
    });

    it('deve mostrar título da página', () => {
      renderWithProviders(<MyPinsPage />);
      
      expect(screen.getByText('My Pinboards')).toBeInTheDocument();
    });
  });
}); 