import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from '../utils/testHelpers';
import { NotificationsPage } from '../../pages/NotificationsPage';

// Mock data and functions using vi.hoisted
const mockData = vi.hoisted(() => {
  const mockUseNotifications = vi.fn();
  const mockUseAuthStore = vi.fn();
  const mockUseToast = vi.fn();
  const mockNavigate = vi.fn();
  
  const mockAuthenticatedUser = {
    user: { 
      id: 'user1', 
      name: 'Test User',
      email: '<EMAIL>'
    },
    isAuthenticated: true,
    isLoading: false,
    error: null,
  };

  const mockNotifications = [
    {
      id: '1',
      type: 'like',
      title: 'New like',
      content: '<PERSON> liked your pin',
      fromUser: { 
        id: '2', 
        name: '<PERSON>', 
        username: 'john', 
        avatar: '/avatar1.jpg' 
      },
      isRead: false,
      timestamp: '2025-01-01T00:00:00Z',
      pinId: 'pin1'
    },
    {
      id: '2',
      type: 'comment',
      title: 'New comment',
      content: '<PERSON> commented on your pin',
      fromUser: { 
        id: '3', 
        name: 'Jane Smith', 
        username: 'jane', 
        avatar: '/avatar2.jpg' 
      },
      isRead: true,
      timestamp: '2025-01-01T01:00:00Z',
      pinId: 'pin2'
    },
    {
      id: '3',
      type: 'follow',
      title: 'New follower',
      content: 'Bob started following you',
      fromUser: { 
        id: '4', 
        name: 'Bob Wilson', 
        username: 'bob', 
        avatar: '/avatar3.jpg' 
      },
      isRead: false,
      timestamp: '2025-01-01T02:00:00Z'
    }
  ];

  return {
    mockUseNotifications,
    mockUseAuthStore,
    mockUseToast,
    mockNavigate,
    mockAuthenticatedUser,
    mockNotifications
  };
});

// Mock all dependencies
vi.mock('../../hooks/useNotifications', () => ({
  useNotifications: mockData.mockUseNotifications,
}));

vi.mock('../../store/authStore', () => ({
  useAuthStore: mockData.mockUseAuthStore,
}));

vi.mock('../../hooks/useToast', () => ({
  useToast: mockData.mockUseToast,
}));

vi.mock('react-router-dom', () => ({
  useNavigate: () => mockData.mockNavigate,
}));

describe('NotificationsPage', () => {
  const mockNotificationActions = {
    markAsRead: vi.fn(),
    markAllAsRead: vi.fn(),
    deleteNotification: vi.fn(),
    deleteAllNotifications: vi.fn(),
    deleteBulkNotifications: vi.fn(),
    refreshNotifications: vi.fn(),
    loadMore: vi.fn()
  };

  const mockToast = {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mocks
    mockData.mockUseAuthStore.mockReturnValue(mockData.mockAuthenticatedUser);
    mockData.mockUseToast.mockReturnValue(mockToast);
    
    mockData.mockUseNotifications.mockReturnValue({
      notifications: mockData.mockNotifications,
      unreadCount: 2,
      loading: false,
      error: null,
      ...mockNotificationActions
    });
  });

  describe('Estados Básicos', () => {
    it('deve renderizar a página de notificações', () => {
      renderWithProviders(<NotificationsPage />);
      
      expect(screen.getByText('Notifications')).toBeInTheDocument();
    });

    it('deve exibir estado vazio quando não há notificações', () => {
      mockData.mockUseNotifications.mockReturnValue({
        notifications: [],
        unreadCount: 0,
        loading: false,
        error: null,
        ...mockNotificationActions
      });

      renderWithProviders(<NotificationsPage />);
      
      expect(screen.getByText('No notifications yet')).toBeInTheDocument();
    });
  });

  describe('Exibição de Notificações', () => {
    it('deve exibir lista de notificações', () => {
      renderWithProviders(<NotificationsPage />);
      
      expect(screen.getByText('John liked your pin')).toBeInTheDocument();
      expect(screen.getByText('Jane commented on your pin')).toBeInTheDocument();
      expect(screen.getByText('Bob started following you')).toBeInTheDocument();
    });

    it('deve exibir nomes dos usuários', () => {
      renderWithProviders(<NotificationsPage />);
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Bob Wilson')).toBeInTheDocument();
    });

    it('deve marcar notificação como lida ao clicar', async () => {
      const user = userEvent.setup();
      renderWithProviders(<NotificationsPage />);
      
      const firstNotificationContent = screen.getByText('John liked your pin');
      await user.click(firstNotificationContent);
      
      expect(mockNotificationActions.markAsRead).toHaveBeenCalledWith('1');
    });
  });

  describe('Filtros', () => {
    it('deve exibir filtros de notificação', () => {
      renderWithProviders(<NotificationsPage />);
      
      expect(screen.getByText('All')).toBeInTheDocument();
      expect(screen.getByText('Likes')).toBeInTheDocument();
      expect(screen.getByText('Comments')).toBeInTheDocument();
      expect(screen.getByText('Follows')).toBeInTheDocument();
    });

    it('deve filtrar notificações por tipo', async () => {
      const user = userEvent.setup();
      renderWithProviders(<NotificationsPage />);
      
      await user.click(screen.getByText('Likes'));
      
      expect(screen.getByText('John liked your pin')).toBeInTheDocument();
      expect(screen.queryByText('Jane commented on your pin')).not.toBeInTheDocument();
      expect(screen.queryByText('Bob started following you')).not.toBeInTheDocument();
    });
  });

  describe('Busca', () => {
    it('deve exibir campo de busca', () => {
      renderWithProviders(<NotificationsPage />);
      
      expect(screen.getByPlaceholderText(/search notifications/i)).toBeInTheDocument();
    });
  });

  describe('Auto-marcação como Lida', () => {
    it('deve marcar todas como lidas quando página carrega', async () => {
      renderWithProviders(<NotificationsPage />);
      
      await waitFor(() => {
        expect(mockNotificationActions.markAllAsRead).toHaveBeenCalled();
      });
    });
  });
}); 