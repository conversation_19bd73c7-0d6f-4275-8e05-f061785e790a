#!/bin/bash

# Script para restaurar backup da migração
# ATENÇÃO: Este script sobrescreverá os arquivos atuais!

echo "⚠️ RESTAURANDO BACKUP DA MIGRAÇÃO"
echo "=================================="
echo "ATENÇÃO: Este script irá sobrescrever os arquivos atuais!"
echo ""
read -p "Tem certeza que deseja continuar? (digite 'SIM' para confirmar): " confirm

if [ "$confirm" != "SIM" ]; then
    echo "❌ Operação cancelada"
    exit 1
fi

BACKUP_DIR="$(dirname "$0")"

echo "📦 Restaurando código fonte..."

# Restaurar rotas
if [ -d "$BACKUP_DIR/routes-original" ]; then
    rm -rf routes/
    cp -r "$BACKUP_DIR/routes-original/" routes/
    echo "   ✅ Rotas restauradas"
fi

# Restaurar serviços
if [ -d "$BACKUP_DIR/src-services-original" ]; then
    rm -rf src/services/
    cp -r "$BACKUP_DIR/src-services-original/" src/services/
    echo "   ✅ Serviços restaurados"
fi

# Restaurar server.js
if [ -f "$BACKUP_DIR/server-original.js" ]; then
    cp "$BACKUP_DIR/server-original.js" server.js
    echo "   ✅ Server.js restaurado"
fi

# Restaurar configurações
if [ -d "$BACKUP_DIR/config-original" ]; then
    rm -rf config/
    cp -r "$BACKUP_DIR/config-original/" config/
    echo "   ✅ Configurações restauradas"
fi

echo ""
echo "🗄️ Para restaurar o banco de dados, execute:"
echo "   PGPASSWORD=pinpal123 psql -h localhost -p 5433 -U postgres -d postgres < $BACKUP_DIR/database-backup.sql"
echo ""
echo "✅ Restauração do código concluída!"
echo "💡 Lembre-se de reinstalar dependências: npm install"
