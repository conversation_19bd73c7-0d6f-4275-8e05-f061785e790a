
-- SCRIPT DE RESTAURAÇÃO DA TABELA ORDERS
-- Gerado em: 2025-06-26T22:56:57.389Z
-- Backup: backups/orders-backup-2025-06-26T22-56-57-340Z.json

-- Para restaurar a tabela orders (SE NECESSÁRIO):
-- 1. <PERSON>criar tabela:
CREATE TABLE IF NOT EXISTS orders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id varchar NOT NULL,
  pin_id uuid,
  amount integer NOT NULL,
  shipping_address_id uuid,
  stripe_payment_intent_id varchar,
  status varchar DEFAULT 'pending',
  created_at timestamp DEFAULT NOW(),
  updated_at timestamp DEFAULT NOW()
);

-- 2. Restaurar dados do backup JSON usando:
-- node scripts/restore-orders-from-backup.js backups/orders-backup-2025-06-26T22-56-57-340Z.json
    