/* Tailwind CSS para Storybook */\n@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* Background escuro para o Storybook */\nbody {\n  background-color: #000000 !important;\n  color: white !important;\n}\n\n.sb-show-main {\n  background-color: #000000 !important;\n}\n\n/* Aplicar tema dark por padrão */\n.dark {\n  background-color: #000000;\n  color: white;\n}\n\n/* Classes Tailwind essenciais */\n.bg-black { background-color: #000000 !important; }\n.bg-gray-800 { background-color: #1f2937 !important; }\n.text-white { color: #ffffff !important; }\n.p-4 { padding: 1rem !important; }\n.rounded-md { border-radius: 0.375rem !important; } 