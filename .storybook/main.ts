import { mergeConfig } from 'vite';
import type { StorybookConfig } from '@storybook/react-vite';

const config: StorybookConfig = {
  "stories": [
    "../src/**/*.mdx",
    "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"
  ],
  "addons": [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-onboarding",
    "@storybook/addon-interactions",
    "@storybook/addon-a11y",
    "@storybook/addon-viewport",
    "@storybook/addon-controls",
    "@storybook/addon-storysource",
  ],
  "framework": {
    "name": "@storybook/react-vite",
    "options": {}
  },
  docs: {
    autodocs: "tag",
    defaultName: 'Documentation',
  },
  staticDirs: ['../public'],
  core: {
    builder: '@storybook/builder-vite',
    disableTelemetry: true,
  },
  typescript: {
    check: false,
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      shouldExtractLiteralValuesFromEnum: true,
      propFilter: (prop) => (prop.parent ? !/node_modules/.test(prop.parent.fileName) : true),
      shouldExtractValuesFromUnion: true,
      shouldRemoveUndefinedFromOptional: true,
    },
  },
  viteFinal: async (config) => {
    return mergeConfig(config, {
      server: {
        watch: {
          usePolling: false,
          interval: 100,
        },
        hmr: {
          overlay: true,
        },
      },
      resolve: {
        alias: {
          '@': require('path').resolve(__dirname, '../src'),
          'react-router-dom': require.resolve('react-router-dom'),
          'react': require.resolve('react'),
        },
      },
      optimizeDeps: {
        include: [
          '@storybook/addon-essentials',
          '@storybook/addon-controls',
          '@storybook/addon-viewport',
        ],
      },
      cacheDir: 'node_modules/.vite/storybook',
    });
  },
};
export default config;