import express from 'express';
import { getPool } from '../../../config/database.js';
import stripeService from '../../../services/stripeService.js';
import { sendNotificationToUser } from '../../../config/websocket.js';
import emailService from '../../../services/emailService.js';

const router = express.Router();

/**
 * GET /api/marketplace/withdrawals/seller/:userId/balance
 * Get seller balance (available and pending)
 */
router.get('/seller/:userId/balance', async (req, res) => {
  try {
    const { userId } = req.params;
    const pool = await getPool();

    // Calculate total earnings from completed orders
    const earningsQuery = `
      SELECT 
        COALESCE(SUM(amount), 0) as total_earnings,
        COALESCE(SUM(CASE WHEN status = 'delivered' AND delivered_at <= NOW() - INTERVAL '7 days' THEN amount ELSE 0 END), 0) as available_balance,
        COALESCE(SUM(CASE WHEN status IN ('pending', 'processing', 'shipped') OR (status = 'delivered' AND delivered_at > NOW() - INTERVAL '7 days') THEN amount ELSE 0 END), 0) as pending_balance
      FROM pin_transactions 
      WHERE seller_id = $1 AND status != 'cancelled'
    `;

    // Calculate total withdrawals
    const withdrawalsQuery = `
      SELECT COALESCE(SUM(amount), 0) as total_withdrawn
      FROM seller_withdrawals 
      WHERE seller_id = $1 AND status IN ('completed', 'processing')
    `;

    const [earningsResult, withdrawalsResult] = await Promise.all([
      pool.query(earningsQuery, [userId]),
      pool.query(withdrawalsQuery, [userId])
    ]);

    const earnings = earningsResult.rows[0];
    const withdrawals = withdrawalsResult.rows[0];

    // Calculate platform fee (5%)
    const platformFeeRate = 0.05;
    const totalEarningsAfterFees = earnings.total_earnings * (1 - platformFeeRate);
    const availableAfterFees = earnings.available_balance * (1 - platformFeeRate);
    const pendingAfterFees = earnings.pending_balance * (1 - platformFeeRate);

    const balance = {
      total_earnings: totalEarningsAfterFees,
      available_balance: Math.max(0, availableAfterFees - withdrawals.total_withdrawn),
      pending_balance: pendingAfterFees,
      total_withdrawn: withdrawals.total_withdrawn,
      platform_fee_rate: platformFeeRate
    };

    res.json({
      success: true,
      balance
    });

  } catch (error) {
    console.error('Error fetching seller balance:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch seller balance'
    });
  }
});

/**
 * POST /api/marketplace/withdrawals/seller/:userId/request
 * Request a withdrawal
 */
router.post('/seller/:userId/request', async (req, res) => {
  console.log('🔍 Withdrawal request received:', {
    userId: req.params.userId,
    body: req.body,
    timestamp: new Date().toISOString()
  });
  
  try {
    const { userId } = req.params;
    const { amount, payment_method = 'stripe' } = req.body;
    const pool = await getPool();

    console.log('✅ Pool obtained, validating amount:', amount);

    // Validate amount
    if (!amount || amount <= 0) {
      console.log('❌ Invalid amount:', amount);
      return res.status(400).json({
        success: false,
        message: 'Invalid withdrawal amount'
      });
    }

    // Check minimum withdrawal ($10)
    const minimumWithdrawal = 10.00;
    if (amount < minimumWithdrawal) {
      return res.status(400).json({
        success: false,
        message: `Minimum withdrawal amount is $${minimumWithdrawal}`
      });
    }

    // Get current balance
    const balanceQuery = `
      SELECT 
        COALESCE(SUM(CASE WHEN status = 'delivered' AND delivered_at <= NOW() - INTERVAL '7 days' THEN amount ELSE 0 END), 0) * 0.95 as available_balance
      FROM pin_transactions 
      WHERE seller_id = $1 AND status != 'cancelled'
    `;

    const withdrawnQuery = `
      SELECT COALESCE(SUM(amount), 0) as total_withdrawn
      FROM seller_withdrawals 
      WHERE seller_id = $1 AND status IN ('completed', 'processing')
    `;

    const [balanceResult, withdrawnResult] = await Promise.all([
      pool.query(balanceQuery, [userId]),
      pool.query(withdrawnQuery, [userId])
    ]);

    const availableBalance = balanceResult.rows[0].available_balance - withdrawnResult.rows[0].total_withdrawn;
    
    console.log('💰 Balance check:', {
      availableBalance,
      requestedAmount: amount,
      balanceFromDB: balanceResult.rows[0].available_balance,
      totalWithdrawn: withdrawnResult.rows[0].total_withdrawn
    });

    if (amount > availableBalance) {
      console.log('❌ Insufficient balance');
      return res.status(400).json({
        success: false,
        message: 'Insufficient available balance'
      });
    }

    // Create withdrawal request
    const withdrawalId = `wd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    let statusToSave = 'pending';
    let stripeTransferId = null;
    let processedAt = null;

    console.log('🆔 Created withdrawal ID:', withdrawalId);

    // Automático? se Stripe + AUTO_TRANSFER_ENABLED
    const autoTransfer = false; // Disabled for now - process.env.AUTO_TRANSFER_ENABLED === 'true' && payment_method === 'stripe';
    console.log('🤖 Auto transfer enabled:', autoTransfer);

    if (autoTransfer) {
      console.log('🔄 Attempting auto transfer...');
      try {
        const transfer = await stripeService.createTransferToSeller(userId, Math.round(amount * 100), { withdrawalId });
        stripeTransferId = transfer.id;
        statusToSave = 'completed';
        processedAt = new Date();
        console.log('✅ Auto transfer successful:', transfer.id);
      } catch (err) {
        console.error('❌ Auto transfer failed:', err);
        statusToSave = 'failed';
        processedAt = new Date();
      }
    }

    console.log('📝 Inserting withdrawal record...', {
      withdrawalId,
      userId,
      amount,
      payment_method,
      statusToSave,
      stripeTransferId,
      processedAt
    });

    const insertQuery = `
      INSERT INTO seller_withdrawals (
        id, seller_id, amount, payment_method, status, stripe_transfer_id,
        requested_at, processed_at, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), $7, NOW(), NOW())
      RETURNING *
    `;

    const result = await pool.query(insertQuery, [
      withdrawalId, userId, amount, payment_method, statusToSave, stripeTransferId, processedAt
    ]);

    console.log('✅ Withdrawal record created:', result.rows[0]);

    const withdrawalRecord = result.rows[0];

    // Notificações + e-mails se completado ou pendente
    if (statusToSave === 'completed') {
      // WS + email
      sendNotificationToUser(userId, {
        notificationType: 'payout_completed',
        amount,
        withdrawalId,
      });

      // email do seller
      const emailRes = await pool.query('SELECT email FROM "user" WHERE id=$1', [userId]);
      const email = emailRes.rows[0]?.email;
      if (email) {
        await emailService.sendMail({
          to: email,
          subject: 'Your PinPal payout was completed',
          html: `<p>Your withdrawal of <strong>$${amount}</strong> has been processed and will arrive shortly.</p>`
        });
      }
    } else {
      sendNotificationToUser(userId, {
        notificationType: 'payout_pending',
        amount,
        withdrawalId,
      });
    }

    res.json({
      success: true,
      withdrawal: withdrawalRecord,
      message: autoTransfer ? 'Withdrawal processed automatically' : 'Withdrawal request submitted successfully'
    });

  } catch (error) {
    console.error('Error creating withdrawal request:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create withdrawal request'
    });
  }
});

/**
 * GET /api/marketplace/withdrawals/seller/:userId/history
 * Get withdrawal history
 */
router.get('/seller/:userId/history', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 20, offset = 0, status } = req.query;
    const pool = await getPool();

    let query = `
      SELECT 
        id, amount, payment_method, status,
        requested_at, processed_at, created_at,
        failure_reason, stripe_transfer_id
      FROM seller_withdrawals 
      WHERE seller_id = $1
    `;
    
    const params = [userId];
    
    if (status && status !== 'all') {
      query += ` AND status = $${params.length + 1}`;
      params.push(status);
    }
    
    query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit, offset);

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM seller_withdrawals WHERE seller_id = $1`;
    const countParams = [userId];
    
    if (status && status !== 'all') {
      countQuery += ` AND status = $2`;
      countParams.push(status);
    }

    const [withdrawalsResult, countResult] = await Promise.all([
      pool.query(query, params),
      pool.query(countQuery, countParams)
    ]);

    res.json({
      success: true,
      withdrawals: withdrawalsResult.rows,
      pagination: {
        total: parseInt(countResult.rows[0].total),
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });

  } catch (error) {
    console.error('Error fetching withdrawal history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch withdrawal history'
    });
  }
});

/**
 * PUT /api/marketplace/withdrawals/:withdrawalId/status
 * Update withdrawal status (admin only)
 */
router.put('/:withdrawalId/status', async (req, res) => {
  try {
    const { withdrawalId } = req.params;
    const { status, failure_reason, stripe_transfer_id } = req.body;
    const pool = await getPool();

    const validStatuses = ['pending', 'processing', 'completed', 'failed'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    let updateQuery = `
      UPDATE seller_withdrawals 
      SET status = $1, updated_at = NOW()
    `;
    const params = [status];

    if (status === 'completed' || status === 'failed') {
      updateQuery += `, processed_at = NOW()`;
    }

    if (failure_reason) {
      updateQuery += `, failure_reason = $${params.length + 1}`;
      params.push(failure_reason);
    }

    if (stripe_transfer_id) {
      updateQuery += `, stripe_transfer_id = $${params.length + 1}`;
      params.push(stripe_transfer_id);
    }

    updateQuery += ` WHERE id = $${params.length + 1} RETURNING *`;
    params.push(withdrawalId);

    const result = await pool.query(updateQuery, params);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Withdrawal not found'
      });
    }

    // Após validar status
    if (status === 'processing') {
      // Buscar withdrawal
      const wdRes = await pool.query('SELECT * FROM seller_withdrawals WHERE id = $1', [withdrawalId]);
      if (wdRes.rows.length === 0) {
        return res.status(404).json({ success: false, message: 'Withdrawal not found' });
      }
      const withdrawal = wdRes.rows[0];
      const amountCents = Math.round(parseFloat(withdrawal.amount) * 100);

      try {
        const transfer = await stripeService.createTransferToSeller(withdrawal.seller_id, amountCents, { withdrawalId });
        stripe_transfer_id = transfer.id;
        status = 'completed';

        // Update db
        await pool.query(`
          UPDATE seller_withdrawals
          SET status = 'completed', processed_at = NOW(), stripe_transfer_id = $1, updated_at = NOW()
          WHERE id = $2
        `, [transfer.id, withdrawalId]);

        // Notify seller (WS + email)
        sendNotificationToUser(withdrawal.seller_id, {
          notificationType: 'payout_completed',
          amount: withdrawal.amount,
          withdrawalId,
        });

        // Buscar e-mail do seller
        const sellerEmailRes = await pool.query('SELECT email FROM "user" WHERE id = $1', [withdrawal.seller_id]);
        const sellerEmail = sellerEmailRes.rows[0]?.email;

        const emailHtml = `<p>Hello,</p><p>Your withdrawal of <strong>$${withdrawal.amount}</strong> has been processed successfully and is on its way to your Stripe account.</p><p>Transfer ID: ${transfer.id}</p><p>Thank you for selling on PinPal!</p>`;
        if (sellerEmail) {
          await emailService.sendMail({
            to: sellerEmail,
            subject: 'Your PinPal payout was completed',
            html: emailHtml,
          });
        }

        return res.json({ success: true, withdrawal: { ...withdrawal, status: 'completed', stripe_transfer_id: transfer.id } });
      } catch (err) {
        console.error('Error processing payout:', err);
        await pool.query(`UPDATE seller_withdrawals SET status = 'failed', failure_reason = $1, updated_at = NOW() WHERE id = $2`, [err.message, withdrawalId]);
        return res.status(500).json({ success: false, message: 'Stripe transfer failed', error: err.message });
      }
    }

    res.json({
      success: true,
      withdrawal: result.rows[0]
    });

  } catch (error) {
    console.error('Error updating withdrawal status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update withdrawal status'
    });
  }
});

export default router; 