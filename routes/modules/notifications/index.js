import express from 'express';
import managementRoutes from './management.js';
import readingRoutes from './reading.js';
import pushRoutes from './push.js';
import fcmV1Routes from './fcm-v1.js';

const router = express.Router();

// Mount sub-modules
router.use('/', managementRoutes);    // CRUD operations (get, create, delete)
router.use('/', readingRoutes);       // Read status and counters
router.use('/', pushRoutes);          // Push notification subscriptions
router.use('/fcm', fcmV1Routes);      // FCM REST API v1 routes

export default router; 