import { useEffect, useState } from 'react';
import { useAuthStore } from '@/store/authStore';
import { pushNotificationService } from '@/services/pushNotificationService';
import { toast } from 'react-hot-toast';

export const usePushNotifications = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [isRegistered, setIsRegistered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Automatically register FCM token when user is authenticated
  useEffect(() => {
    const registerFCMToken = async () => {
      if (!isAuthenticated || !user?.id || isRegistered) {
        return;
      }

      setIsLoading(true);
      try {
        console.log('🔔 Registering FCM token for user:', user.id);
        
        // Request permission and get FCM token
        const token = await pushNotificationService.requestPermission();
        
        if (token) {
          // Save token to backend
          const success = await pushNotificationService.saveToken(user.id, token);
          
          if (success) {
            console.log('✅ FCM token registered successfully');
            setIsRegistered(true);
            
            // Setup foreground message listener
            pushNotificationService.onMessage((payload) => {
              console.log('📱 Notification received:', payload);
              
              // Show toast notification for foreground messages
              toast.success(
                `${payload.notification?.title}: ${payload.notification?.body}`,
                {
                  duration: 5000,
                  icon: '🔔',
                }
              );
            });
          } else {
            console.warn('⚠️ Failed to save FCM token to backend');
          }
        } else {
          console.warn('⚠️ FCM token not available (permission denied or not supported)');
        }
      } catch (error) {
        console.warn('⚠️ FCM registration skipped:', error instanceof Error ? error.message : 'Unknown error');
        // Don't show error toast for FCM registration failures as it's not critical
        // This is normal if user denies notification permission
      } finally {
        setIsLoading(false);
      }
    };

    registerFCMToken();
  }, [isAuthenticated, user?.id, isRegistered]);

  // Manual registration function
  const registerToken = async () => {
    console.log('🔔 registerToken called, user:', user?.id);

    if (!user?.id) {
      console.log('🔔 User not authenticated');
      toast.error('User not authenticated');
      return false;
    }

    console.log('🔔 Setting loading to true');
    setIsLoading(true);

    try {
      console.log('🔔 Requesting permission...');
      const token = await pushNotificationService.requestPermission();
      console.log('🔔 Token received:', token ? 'YES' : 'NO');

      if (token) {
        console.log('🔔 Saving token to backend...');
        const success = await pushNotificationService.saveToken(user.id, token);
        console.log('🔔 Save token result:', success);

        if (success) {
          console.log('🔔 Success! Setting registered to true');
          setIsRegistered(true);
          toast.success('Push notifications enabled!');
          return true;
        } else {
          console.log('🔔 Failed to save token');
          toast.error('Failed to register for notifications');
          return false;
        }
      } else {
        console.log('🔔 No token received');
        toast.error('Notification permission denied');
        return false;
      }
    } catch (error) {
      console.error('🔔 Error registering FCM token:', error);
      toast.error('Failed to enable notifications');
      return false;
    } finally {
      console.log('🔔 Setting loading to false');
      setIsLoading(false);
    }
  };

  return {
    isRegistered,
    isLoading,
    registerToken,
  };
}; 