import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { getApp, getApps } from 'firebase/app';

export interface PushNotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: Record<string, any>;
}

export interface AdminNotificationRequest {
  type: 'all' | 'specific';
  userIds?: string[];
  title: string;
  body: string;
  icon?: string;
  actionUrl?: string;
  data?: Record<string, any>;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  title: string;
  body: string;
  icon?: string;
  category: 'announcement' | 'update' | 'promotion' | 'alert';
}

class PushNotificationService {
  private messaging;
  private vapidKey = import.meta.env.VITE_FIREBASE_VAPID_KEY;
  private baseUrl = 'http://localhost:3001/api';

  constructor() {
    try {
      // Get the existing Firebase app or use the first one
      const app = getApps().length > 0 ? getApp() : null;
      if (app) {
        this.messaging = getMessaging(app);
      } else {
        console.warn('Firebase app not initialized yet');
      }
    } catch (error) {
      console.error('Error initializing Firebase Messaging:', error);
    }
  }

  // Request permission and get FCM token
  async requestPermission(): Promise<string | null> {
    console.log('🔔 pushNotificationService.requestPermission called');

    try {
      if (!('Notification' in window)) {
        console.log('🔔 Browser does not support notifications');
        throw new Error('This browser does not support notifications');
      }

      console.log('🔔 Current permission:', Notification.permission);
      console.log('🔔 Requesting permission...');

      const permission = await Notification.requestPermission();
      console.log('🔔 Permission result:', permission);

      if (permission !== 'granted') {
        console.log('🔔 Permission denied');
        throw new Error('Notification permission denied');
      }

      if (!this.messaging) {
        console.log('🔔 Firebase Messaging not initialized');
        throw new Error('Firebase Messaging not initialized');
      }

      console.log('🔔 Firebase Messaging initialized, getting token...');

      // Prepare token options
      const tokenOptions: any = {};
      if (this.vapidKey) {
        tokenOptions.vapidKey = this.vapidKey;
        console.log('🔔 Using VAPID key for FCM token');
      } else {
        console.warn('🔔 No VAPID key configured - using default FCM registration');
      }

      const token = await getToken(this.messaging, tokenOptions);
      console.log('🔔 getToken result:', token ? 'SUCCESS' : 'FAILED');

      if (token) {
        console.log('🔔 FCM Token received:', token.substring(0, 20) + '...');
        return token;
      } else {
        console.log('🔔 No registration token available');
        throw new Error('No registration token available');
      }
    } catch (error) {
      console.error('🔔 Error getting FCM token:', error);
      return null;
    }
  }

  // Listen for foreground messages
  onMessage(callback: (payload: any) => void) {
    if (!this.messaging) return;

    return onMessage(this.messaging, (payload) => {
      console.log('Message received in foreground:', payload);
      callback(payload);
    });
  }

  // Save FCM token to backend
  async saveToken(userId: string, token: string): Promise<boolean> {
    console.log('🔔 saveToken called for user:', userId);

    try {
      const url = `${this.baseUrl}/admin/push-notifications/tokens`;
      console.log('🔔 Making request to:', url);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          token,
          platform: 'web',
          userAgent: navigator.userAgent
        }),
      });

      console.log('🔔 Response status:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('🔔 Error response:', errorText);
      }

      return response.ok;
    } catch (error) {
      console.error('🔔 Error saving FCM token:', error);
      return false;
    }
  }

  // Admin: Send notification to all users
  async sendToAll(notification: Omit<AdminNotificationRequest, 'type' | 'userIds'>): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/admin/push-notifications/send-all`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notification),
      });

      const result = await response.json();
      return response.ok ? result : { success: false, error: result.error };
    } catch (error) {
      console.error('Error sending notification to all:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // Admin: Send notification to specific users
  async sendToUsers(userIds: string[], notification: Omit<AdminNotificationRequest, 'type' | 'userIds'>): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/admin/push-notifications/send-specific`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userIds,
          ...notification
        }),
      });

      const result = await response.json();
      return response.ok ? result : { success: false, error: result.error };
    } catch (error) {
      console.error('Error sending notification to users:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // Admin: Get notification statistics
  async getStatistics(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/admin/push-notifications/stats`);
      if (!response.ok) throw new Error('Failed to fetch stats');
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching notification stats:', error);
      return null;
    }
  }

  // Admin: Get notification history
  async getHistory(limit: number = 50, offset: number = 0): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/admin/push-notifications/history?limit=${limit}&offset=${offset}`);
      if (!response.ok) throw new Error('Failed to fetch history');
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching notification history:', error);
      return [];
    }
  }

  // Admin: Get registered tokens count
  async getTokensCount(): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/admin/push-notifications/tokens/count`);
      if (!response.ok) throw new Error('Failed to fetch tokens count');
      
      const result = await response.json();
      return result.count || 0;
    } catch (error) {
      console.error('Error fetching tokens count:', error);
      return 0;
    }
  }

  // Get notification templates
  async getTemplates(): Promise<NotificationTemplate[]> {
    try {
      const response = await fetch(`${this.baseUrl}/admin/push-notifications/templates`);
      if (!response.ok) throw new Error('Failed to fetch templates');
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching templates:', error);
      return [];
    }
  }

  // Create notification template
  async createTemplate(template: Omit<NotificationTemplate, 'id'>): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/admin/push-notifications/templates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(template),
      });

      return response.ok;
    } catch (error) {
      console.error('Error creating template:', error);
      return false;
    }
  }

  // Test notification (send to current admin only)
  async testNotification(notification: PushNotificationPayload): Promise<any> {
    console.log('🔧 pushNotificationService.testNotification called with:', notification);
    console.log('🌐 Base URL:', this.baseUrl);
    
    try {
      const url = `${this.baseUrl}/admin/push-notifications/test`;
      console.log('📡 Making request to:', url);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notification),
      });

      console.log('📥 Response status:', response.status, response.statusText);
      
      const result = await response.json();
      console.log('📦 Response body:', result);
      
      if (response.ok) {
        console.log('✅ Request successful, returning result');
        return result;
      } else {
        console.log('❌ Request failed, returning error');
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('💥 Exception in testNotification:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}

export const pushNotificationService = new PushNotificationService(); 