:root {
  /* Base Colors - Black and White */
  --color-black: #000000;
  --color-white: #ffffff;
  
  /* Dark Mode Soft Colors - Cores mais suaves para dark mode */
  --color-dark-text-primary: #e2e8f0;     /* Cinza muito claro, mais suave que branco puro */
  --color-dark-text-secondary: #cbd5e1;   /* Cinza claro para textos secundários */
  --color-dark-text-muted: #94a3b8;       /* Cinza médio para textos menos importantes */
  --color-dark-border-soft: #334155;      /* Cinza escuro para bordas suaves */
  --color-dark-border-medium: #475569;    /* Cinza médio para bordas mais visíveis */
  --color-dark-input-bg: rgba(51, 65, 85, 0.3);  /* Fundo translúcido para inputs */
  --color-dark-input-border: rgba(71, 85, 105, 0.5); /* Borda suave para inputs */
  
  /* Primary Colors - Blue */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Gray Scale */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: var(--color-black); /* Centralized black */
  --color-gray-900: var(--color-black); /* Centralized black */
  
  /* Chat Dark Colors - All using centralized black */
  --color-chat-dark-900: var(--color-black);
  --color-chat-dark-800: var(--color-black);
  --color-chat-dark-700: var(--color-black);
  --color-chat-dark-600: #334155;
  
  /* Background Colors - Corrigido para suportar light mode */
  --color-bg-primary-light: var(--color-white);
  --color-bg-primary-dark: var(--color-black);
  --color-bg-secondary-light: #f8fafc;
  --color-bg-secondary-dark: var(--color-black);
  --color-bg-tertiary-light: #f1f5f9;
  --color-bg-tertiary-dark: var(--color-black);
  
  /* Text Colors - Corrigido para light mode */
  --color-text-primary-light: #1e293b;
  --color-text-primary-dark: var(--color-dark-text-primary);
  --color-text-secondary-light: #475569;
  --color-text-secondary-dark: var(--color-dark-text-secondary);
  --color-text-muted-light: #64748b;
  --color-text-muted-dark: var(--color-dark-text-muted);
  
  /* Title Colors - Corrigido para light mode */
  --color-title-primary: #1e293b;
  --color-title-secondary: #475569;
  --color-title-muted: #64748b;
  
  /* Border Colors */
  --color-border-light: var(--color-gray-200);
  --color-border-dark: var(--color-dark-border-soft);
  
  /* Success Colors */
  --color-success-light: #d1fae5;
  --color-success-main: #10b981;
  --color-success-dark: #047857;
  
  /* Error Colors */
  --color-error-light: #fee2e2;
  --color-error-main: #ef4444;
  --color-error-dark: #b91c1c;
  
  /* Warning Colors */
  --color-warning-light: #fef3c7;
  --color-warning-main: #f59e0b;
  --color-warning-dark: #b45309;
  
  /* Info Colors */
  --color-info-light: #dbeafe;
  --color-info-main: #3b82f6;
  --color-info-dark: #1d4ed8;
  
  /* Map Theme Colors */
  --color-map-water-dark: var(--color-black);
  --color-map-geometry-dark: var(--color-black);
  --color-map-text-stroke-dark: var(--color-black);
  --color-map-landscape-dark: #2d3748;
  --color-map-road-dark: #2d3748;
  --color-map-highway-dark: #4a5568;
  
  /* Scrollbar Colors */
  --color-scrollbar-track-light: #f1f5f9;
  --color-scrollbar-track-dark: var(--color-black);
  --color-scrollbar-thumb-light: #cbd5e1;
  --color-scrollbar-thumb-dark: #334155;
  --color-scrollbar-thumb-hover-light: #94a3b8;
  --color-scrollbar-thumb-hover-dark: #475569;
  
  /* Message Bubbles */
  --message-sent-bg: var(--color-primary-600);
  --message-sent-text: var(--color-white);
  --message-sent-border: transparent;
  
  --message-received-bg: var(--color-gray-200);
  --message-received-text: var(--color-gray-900);
  --message-received-border: transparent;
  
  --message-system-bg: var(--color-gray-100);
  --message-system-text: var(--color-gray-800);
  --message-system-border: var(--color-gray-300);
  
  --message-error-bg: var(--color-error-light);
  --message-error-text: var(--color-error-dark);
  --message-error-border: var(--color-error-main);
  
  --message-success-bg: var(--color-success-light);
  --message-success-text: var(--color-success-dark);
  --message-success-border: var(--color-success-main);
  
  --message-attachment-bg: rgba(0, 0, 0, 0.04);
  --message-attachment-text: var(--color-gray-700);
  --message-attachment-border: var(--color-gray-300);
  
  --message-timestamp-text: var(--color-gray-500);
  --message-typing-indicator: var(--color-gray-400);
  
  /* Border Radius */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 6px; 
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-pill: 9999px;
  --radius-full: 50%;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Typography */
  --font-family-body: 'Inter', system-ui, -apple-system, sans-serif;
  --font-family-heading: 'Inter', system-ui, -apple-system, sans-serif;
  --font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-md: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-xxl: 1.5rem;   /* 24px */
  
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}

/* Dark mode overrides - CORRIGIDO E COMPLETO */
.dark {
  /* ✅ ADICIONADO: Title colors para dark mode */
  --color-title-primary: var(--color-dark-text-primary);
  --color-title-secondary: var(--color-dark-text-secondary);
  --color-title-muted: var(--color-dark-text-muted);
  
  /* ✅ ADICIONADO: Background colors para dark mode */
  --color-bg-primary: var(--color-bg-primary-dark);
  --color-bg-secondary: var(--color-bg-secondary-dark);
  --color-bg-tertiary: var(--color-bg-tertiary-dark);
  
  /* ✅ ADICIONADO: Text colors para dark mode */
  --color-text-primary: var(--color-text-primary-dark);
  --color-text-secondary: var(--color-text-secondary-dark);
  --color-text-muted: var(--color-text-muted-dark);
  
  /* ✅ ADICIONADO: Border colors para dark mode */
  --color-border: var(--color-border-dark);
  
  /* Message bubbles dark mode */
  --message-received-bg: var(--color-gray-700);
  --message-received-text: var(--color-gray-100);
  --message-received-border: transparent;
  
  --message-system-bg: var(--color-gray-800);
  --message-system-text: var(--color-gray-200);
  --message-system-border: var(--color-gray-600);
  
  --message-error-bg: rgba(239, 68, 68, 0.2);
  --message-error-text: #fca5a5;
  --message-error-border: #ef4444;
  
  --message-success-bg: rgba(16, 185, 129, 0.2);
  --message-success-text: #6ee7b7;
  --message-success-border: #10b981;
  
  --message-attachment-bg: rgba(255, 255, 255, 0.05);
  --message-attachment-text: var(--color-gray-300);
  --message-attachment-border: var(--color-gray-600);
  
  --message-timestamp-text: var(--color-gray-400);
  --message-typing-indicator: var(--color-gray-300);
  
  /* Sombras mais escuras para dark mode */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
} 