/**
 * Tokens de design centralizados para componentes de autenticação
 * Garantem consistência visual e facilitam manutenção
 */

export const authTokens = {
  // Cores de fundo
  background: {
    page: 'bg-gray-50 dark:bg-gray-900',
    card: 'bg-white/95 dark:bg-gray-800/90',
    input: 'bg-white dark:bg-gray-800',
    button: {
      primary: 'bg-blue-600 hover:bg-blue-700',
      secondary: 'bg-white dark:bg-gray-700/50 hover:bg-gray-50 dark:hover:bg-gray-600/50'
    }
  },

  // Cores de texto - usando cores mais suaves no dark mode
  text: {
    primary: 'text-gray-900 dark:text-slate-200',
    secondary: 'text-gray-600 dark:text-slate-300',
    muted: 'text-gray-500 dark:text-slate-400',
    placeholder: 'placeholder-gray-500 dark:placeholder-slate-400',
    link: 'text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300'
  },

  // Bordas
  border: {
    default: 'border-gray-300 dark:border-gray-600',
    subtle: 'border-gray-300 dark:border-white/20',
    none: 'border-0 dark:border dark:border-white/10',
    focus: 'focus:border-blue-500 dark:focus:border-blue-400'
  },

  // Sombras
  shadow: {
    card: 'shadow-xl dark:shadow-2xl',
    button: 'shadow-sm'
  },

  // Bordas arredondadas
  radius: {
    card: 'rounded-2xl',
    input: 'rounded-lg',
    button: 'rounded-lg'
  },

  // Espaçamento
  spacing: {
    cardPadding: 'p-8',
    inputPadding: 'py-3 px-4',
    buttonPadding: 'py-3 px-4',
    labelMargin: 'mb-2'
  },

  // Estados de foco e interação
  focus: {
    ring: 'focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400',
    offset: 'focus:ring-offset-2 dark:focus:ring-offset-gray-800'
  },

  // Transições
  transition: {
    default: 'transition-all duration-200',
    colors: 'transition-colors'
  },

  // Efeitos especiais
  effects: {
    glassmorphism: 'backdrop-blur-sm',
    disabled: 'disabled:opacity-50 disabled:cursor-not-allowed'
  }
} as const;

// Utilitários para combinar tokens
export const combineAuthTokens = (...tokens: string[]) => tokens.join(' ');

// Classes específicas para inputs de autenticação
export const authInputClasses = combineAuthTokens(
  'block w-full',
  authTokens.background.input,
  authTokens.border.default,
  authTokens.radius.input,
  authTokens.spacing.inputPadding,
  authTokens.text.primary,
  authTokens.text.placeholder,
  authTokens.focus.ring,
  authTokens.border.focus,
  authTokens.transition.default
);

// Classes específicas para cards de autenticação
export const authCardClasses = combineAuthTokens(
  authTokens.background.card,
  authTokens.effects.glassmorphism,
  authTokens.radius.card,
  authTokens.shadow.card,
  authTokens.border.none,
  authTokens.spacing.cardPadding
);

// Classes específicas para cards de autenticação sem glassmorphism (para melhor clareza dos ícones)
export const authCardClassesSolid = combineAuthTokens(
  authTokens.background.card,
  authTokens.radius.card,
  authTokens.shadow.card,
  authTokens.border.none,
  authTokens.spacing.cardPadding
);

// Classes específicas para botões primários
export const authButtonPrimaryClasses = combineAuthTokens(
  'w-full flex justify-center',
  authTokens.spacing.buttonPadding,
  'border border-transparent',
  authTokens.radius.button,
  authTokens.shadow.button,
  'text-sm font-medium text-white',
  authTokens.background.button.primary,
  authTokens.focus.ring,
  authTokens.focus.offset,
  authTokens.effects.disabled,
  authTokens.transition.colors
);

// Classes específicas para botões secundários
export const authButtonSecondaryClasses = combineAuthTokens(
  'w-full inline-flex justify-center',
  authTokens.spacing.buttonPadding,
  'border',
  authTokens.border.subtle,
  authTokens.radius.button,
  authTokens.shadow.button,
  'text-sm font-medium',
  authTokens.text.muted,
  authTokens.background.button.secondary,
  authTokens.focus.ring,
  authTokens.focus.offset,
  authTokens.effects.disabled,
  authTokens.transition.colors
); 