import React, { useState, useEffect } from 'react';
import { BellIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/design-system/Button';
import { usePushNotifications } from '@/hooks/usePushNotifications';
import { useAuthStore } from '@/store/authStore';

export const NotificationPermissionBanner: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [permissionDenied, setPermissionDenied] = useState(false);
  const { registerToken, isLoading } = usePushNotifications();
  const { user, isAuthenticated } = useAuthStore();

  // Detect browser for specific instructions
  const getBrowserName = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.includes('chrome') && !userAgent.includes('edg')) return 'Chrome';
    if (userAgent.includes('edg')) return 'Edge';
    if (userAgent.includes('firefox')) return 'Firefox';
    if (userAgent.includes('safari') && !userAgent.includes('chrome')) return 'Safari';
    return 'Browser';
  };

  const browserName = getBrowserName();

  useEffect(() => {
    // Check notification permission status
    const checkPermission = () => {
      if ('Notification' in window) {
        const permission = Notification.permission;
        
        if (permission === 'denied') {
          setPermissionDenied(true);
          setIsVisible(true);
        } else if (permission === 'default') {
          setIsVisible(true);
        } else if (permission === 'granted') {
          setIsVisible(false);
          setPermissionDenied(false);
        }
      }
    };

    // Check permission after a delay to avoid showing immediately
    const timer = setTimeout(checkPermission, 2000);
    
    // Also check periodically in case user changes permission in browser settings
    const interval = setInterval(checkPermission, 5000);
    
    return () => {
      clearTimeout(timer);
      clearInterval(interval);
    };
  }, []);

  const handleEnableNotifications = async () => {
    console.log('🔔 Enable notifications button clicked');
    console.log('🔔 User authenticated:', isAuthenticated);
    console.log('🔔 User ID:', user?.id);

    if (!isAuthenticated || !user?.id) {
      console.log('🔔 User not authenticated, cannot enable notifications');
      return;
    }

    try {
      const success = await registerToken();
      console.log('🔔 Register token result:', success);
      if (success) {
        console.log('🔔 Success! Hiding banner');
        setIsVisible(false);
      } else {
        console.log('🔔 Failed to register token');
      }
    } catch (error) {
      console.error('🔔 Error in handleEnableNotifications:', error);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    // Remember user dismissed this banner
    localStorage.setItem('notification-banner-dismissed', 'true');
  };



  // Don't show if user previously dismissed
  useEffect(() => {
    const dismissed = localStorage.getItem('notification-banner-dismissed');
    if (dismissed === 'true') {
      setIsVisible(false);
    }
  }, []);

  if (!isVisible || !('Notification' in window) || !isAuthenticated) {
    return null;
  }

  return (
    <div className="fixed top-16 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
      <div className="bg-blue-600 text-white rounded-lg shadow-lg p-4 border border-blue-500">
        <div className="flex items-start space-x-3">
          <BellIcon className="h-6 w-6 text-blue-200 flex-shrink-0 mt-0.5" />
          
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
              Enable Push Notifications
            </h3>
            
            <p className="text-sm text-blue-100 mt-1">
              {permissionDenied 
                ? "Notifications are blocked. To enable them:"
                : "Stay updated with the latest pins, trades, and community activities!"
              }
            </p>
            
            {permissionDenied && (
              <div className="text-xs text-blue-100 mt-2 p-2 bg-blue-700/30 rounded border border-blue-500/30">
                <div className="font-semibold mb-1">For {browserName}:</div>
                {browserName === 'Chrome' && (
                  <div>1. Click the site info icon on the left side of the URL<br/>2. Set Notifications to "Allow"<br/>3. Refresh this page</div>
                )}
                {browserName === 'Edge' && (
                  <div>1. Click the site info icon on the left side of the URL<br/>2. Set Notifications to "Allow"<br/>3. Refresh this page</div>
                )}
                {browserName === 'Firefox' && (
                  <div>1. Click the shield icon in the address bar<br/>2. Go to Permissions → Notifications<br/>3. Select "Allow"<br/>4. Refresh this page</div>
                )}
                {browserName === 'Safari' && (
                  <div>1. Go to Safari menu → Settings for this Website<br/>2. Set Notifications to "Allow"<br/>3. Refresh this page</div>
                )}
                {!['Chrome', 'Edge', 'Firefox', 'Safari'].includes(browserName) && (
                  <div>1. Look for a site info or permissions icon on the left side of the URL<br/>2. Set Notifications to "Allow"<br/>3. Refresh this page</div>
                )}
              </div>
            )}
            
                          <div className="flex items-center space-x-2 mt-3">
              {!permissionDenied && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleEnableNotifications}
                  disabled={isLoading}
                  className="text-gray-900 dark:text-white border-white/30 hover:bg-white/10"
                >
                  {isLoading ? 'Enabling...' : 'Enable'}
                </Button>
              )}
              

              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="text-gray-900 dark:text-white border-white/30 hover:bg-white/10"
              >
                {permissionDenied ? 'Got it' : 'Maybe later'}
              </Button>
            </div>
          </div>
          
          <button
            onClick={handleDismiss}
            className="text-blue-200 hover:text-gray-900 dark:text-white transition-colors p-1"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}; 