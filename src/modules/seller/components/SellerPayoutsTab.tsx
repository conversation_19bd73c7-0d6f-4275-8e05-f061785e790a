import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  CurrencyDollarIcon, 
  BanknotesIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import { Button } from '../../../components/ui/design-system/Button';
import { Input } from '../../../components/ui/design-system/Input';
import { LoadingSpinner } from '../../../components/ui/LoadingSpinner';
import { Modal } from '../../../components/ui/design-system/Modal';
import { useToast } from '../../../hooks/useToast';
import { useAuthStore } from '../../../store/authStore';
import marketplaceService from '../../../services/marketplaceService';
import { formatDistanceToNow } from 'date-fns';
import { websocketService } from '../../../services/websocketService';

interface WithdrawalRequest {
  id: string;
  amount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  payment_method: string;
  requested_at: string;
  processed_at?: string;
  failure_reason?: string;
}

interface SellerBalance {
  total_earnings: number;
  available_balance: number;
  pending_balance: number;
  total_withdrawn: number;
  platform_fee_rate: number;
}

export const SellerPayoutsTab: React.FC = () => {
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  const { showToast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuthStore();
  const userId = user?.id || '';

  // DEBUG: Log do userId atual
  console.log('SellerPayoutsTab - userId:', userId, 'user:', user);

  // Fetch seller balance
  const { data: balanceData, isLoading: isLoadingBalance, refetch: refetchBalance } = useQuery({
    queryKey: ['seller-balance', userId],
    queryFn: () => {
      console.log('Fetching balance for userId:', userId);
      return marketplaceService.getSellerBalance(userId);
    },
    enabled: !!userId,
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  // DEBUG: Log dos dados recebidos
  console.log('SellerPayoutsTab - balanceData:', balanceData);

  // Fetch withdrawal history
  const { data: historyData, isLoading: isLoadingHistory, refetch: refetchHistory } = useQuery({
    queryKey: ['withdrawal-history', userId, statusFilter],
    queryFn: () => marketplaceService.getWithdrawalHistory(userId, {
      status: statusFilter,
      limit: 50
    }),
    enabled: !!userId
  });

  // Request withdrawal mutation
  const withdrawMutation = useMutation({
    mutationFn: (amount: number) => 
      marketplaceService.requestWithdrawal(userId, amount),
    onSuccess: () => {
      showToast('Withdrawal request submitted successfully', 'success');
      setShowWithdrawModal(false);
      setWithdrawAmount('');
      queryClient.invalidateQueries({ queryKey: ['seller-balance'] });
      queryClient.invalidateQueries({ queryKey: ['withdrawal-history'] });
    },
    onError: (error: any) => {
      showToast(error.response?.data?.message || 'Failed to request withdrawal', 'error');
    }
  });

  const balance = balanceData?.balance as SellerBalance;
  const withdrawals = historyData?.withdrawals || [];

  // Função para forçar refresh
  const handleForceRefresh = () => {
    console.log('Forcing refresh for userId:', userId);
    queryClient.invalidateQueries({ queryKey: ['seller-balance'] });
    queryClient.invalidateQueries({ queryKey: ['withdrawal-history'] });
    refetchBalance();
    refetchHistory();
    showToast('Data refreshed!', 'success');
  };

  const handleWithdrawRequest = () => {
    const amount = parseFloat(withdrawAmount);
    if (isNaN(amount) || amount <= 0) {
      showToast('Please enter a valid amount', 'error');
      return;
    }
    if (amount > balance.available_balance) {
      showToast('Insufficient available balance', 'error');
      return;
    }
    if (amount < 10) {
      showToast('Minimum withdrawal amount is $10', 'error');
      return;
    }
    withdrawMutation.mutate(amount);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-400" />;
      case 'processing':
        return <ClockIcon className="h-5 w-5 text-blue-400" />;
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-400" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-400" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-400';
      case 'processing':
        return 'text-blue-400';
      case 'completed':
        return 'text-green-400';
      case 'failed':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  useEffect(() => {
    if (!userId) return;

    const handleNotification = (data: any) => {
      if (data.notificationType === 'payout_completed') {
        showToast('Your payout was completed! 💸', 'success');
        refetchBalance();
        refetchHistory();
      }
    };

    websocketService.onNotification(handleNotification);

    return () => {
      websocketService.off('notification', handleNotification);
    };
  }, [userId, refetchBalance, refetchHistory, showToast]);

  // Auto-refresh quando detectar dados zerados (problema de cache)
  useEffect(() => {
    if (balanceData && balance && 
        balance.total_earnings === 0 && 
        balance.available_balance === 0 && 
        balance.pending_balance === 0) {
      console.log('Detected zero balance, forcing refresh...');
      setTimeout(() => {
        handleForceRefresh();
      }, 1000);
    }
  }, [balanceData, balance]);

  if (isLoadingBalance || isLoadingHistory) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* DEBUG INFO - Remover depois */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
          <h4 className="text-blue-400 font-medium mb-2">🔧 Debug Info</h4>
          <p className="text-sm text-blue-300">
            <strong>User ID:</strong> {userId || 'Not found'}<br/>
            <strong>User Email:</strong> {user?.email || 'Not found'}<br/>
            <strong>User Name:</strong> {user?.firstName} {user?.lastName}<br/>
            <strong>Is Authenticated:</strong> {user ? 'Yes' : 'No'}<br/>
            <strong>Balance Data:</strong> {balanceData ? 'Loaded' : 'Loading...'}<br/>
            <strong>API Response:</strong> {JSON.stringify(balance, null, 2)}
          </p>
          <Button
            variant="ghost"
            onClick={handleForceRefresh}
            className="mt-2 text-xs"
          >
            🔄 Force Refresh
          </Button>
        </div>
      )}

      {/* Balance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Total Earnings</h3>
            <CurrencyDollarIcon className="h-5 w-5 text-gray-500" />
          </div>
          <p className="text-2xl font-bold text-white">
            ${balance?.total_earnings.toFixed(2) || '0.00'}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            After {(balance?.platform_fee_rate || 0) * 100}% platform fee
          </p>
        </div>

        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Available Balance</h3>
            <BanknotesIcon className="h-5 w-5 text-green-500" />
          </div>
          <p className="text-2xl font-bold text-green-400">
            ${balance?.available_balance.toFixed(2) || '0.00'}
          </p>
          <p className="text-xs text-gray-500 mt-1">Ready to withdraw</p>
        </div>

        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Pending Balance</h3>
            <ClockIcon className="h-5 w-5 text-yellow-500" />
          </div>
          <p className="text-2xl font-bold text-yellow-400">
            ${balance?.pending_balance.toFixed(2) || '0.00'}
          </p>
          <p className="text-xs text-gray-500 mt-1">From recent sales</p>
        </div>

        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Total Withdrawn</h3>
            <ArrowDownTrayIcon className="h-5 w-5 text-blue-500" />
          </div>
          <p className="text-2xl font-bold text-blue-400">
            ${balance?.total_withdrawn ? parseFloat(balance.total_withdrawn.toString()).toFixed(2) : '0.00'}
          </p>
          <p className="text-xs text-gray-500 mt-1">Lifetime payouts</p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3">
        <Button
          variant="ghost"
          onClick={handleForceRefresh}
          className="flex items-center space-x-2"
        >
          <span>🔄 Refresh Data</span>
        </Button>
        <Button
          variant="primary"
          onClick={() => setShowWithdrawModal(true)}
          disabled={!balance || balance.available_balance < 10}
          className="flex items-center space-x-2"
        >
          <ArrowDownTrayIcon className="h-5 w-5" />
          <span>Request Withdrawal</span>
        </Button>
      </div>

      {/* Withdrawal History */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-lg">
        <div className="border-b border-gray-700 p-6">
          <h2 className="text-lg font-semibold text-white mb-4">Withdrawal History</h2>
          
          {/* Status Filter */}
          <div className="flex space-x-2">
            {['all', 'pending', 'processing', 'completed', 'failed'].map((status) => (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  statusFilter === status
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>

        <div className="p-6">
          {withdrawals.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400">No withdrawal history found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {withdrawals.map((withdrawal: WithdrawalRequest) => (
                <div 
                  key={withdrawal.id}
                  className="bg-gray-900/50 border border-gray-700 rounded-lg p-4 flex items-center justify-between"
                >
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(withdrawal.status)}
                    <div>
                      <p className="text-white font-medium">
                        ${withdrawal.amount.toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-400">
                        Requested {formatDistanceToNow(new Date(withdrawal.requested_at), { addSuffix: true })}
                      </p>
                    </div>
                  </div>

                  <div className="text-right">
                    <p className={`text-sm font-medium ${getStatusColor(withdrawal.status)}`}>
                      {withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1)}
                    </p>
                    {withdrawal.processed_at && (
                      <p className="text-xs text-gray-500">
                        Processed {formatDistanceToNow(new Date(withdrawal.processed_at), { addSuffix: true })}
                      </p>
                    )}
                    {withdrawal.failure_reason && (
                      <p className="text-xs text-red-400 mt-1">
                        {withdrawal.failure_reason}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Withdrawal Modal */}
      <Modal
        isOpen={showWithdrawModal}
        onClose={() => setShowWithdrawModal(false)}
        title="Request Withdrawal"
        size="sm"
      >
        <div className="space-y-6">
          <div>
            <p className="text-gray-300 mb-4">
              Enter the amount you want to withdraw. Minimum withdrawal is $10.
            </p>
            
            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 mb-4">
              <p className="text-sm text-gray-400">Available Balance</p>
              <p className="text-2xl font-bold text-green-400">
                ${balance?.available_balance.toFixed(2) || '0.00'}
              </p>
            </div>

            <Input
              type="number"
              placeholder="Enter amount"
              value={withdrawAmount}
              onChange={(e) => setWithdrawAmount(e.target.value)}
              variant="glass"
              min="10"
              max={balance?.available_balance.toString()}
              step="0.01"
            />
          </div>

          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <p className="text-sm text-blue-400">
              <strong>Note:</strong> Withdrawals are processed within 2-5 business days. 
              Funds will be transferred to your connected Stripe account.
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="ghost"
              onClick={() => setShowWithdrawModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleWithdrawRequest}
              loading={withdrawMutation.isPending}
              disabled={!withdrawAmount || parseFloat(withdrawAmount) < 10}
            >
              Request Withdrawal
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}; 