import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { usersService, AdminUser } from '@/services/usersService';
import { UsernameService } from '@/services/usernameService';
import { Avatar } from '@/components/ui/Avatar';
import { UserStatsModal } from '@/components/ui/UserStatsModal';
import { PinCard } from '@/components/ui/PinCard';
import { BoardCard } from '@/components/ui/BoardCard';
import { PinDetailModal } from '@/components/ui/PinDetailModal';
import { GlassModal } from '@/components/ui/GlassModal';
import { QRCodeDisplay } from '@/components/ui/QRCodeDisplay';
import { FollowersModal } from './components/FollowersModal';
import { FollowingModal } from './components/FollowingModal';
import { useToast } from '@/hooks/useToast';
import { copyToClipboard } from '@/utils/clipboard';

import { useUserStats } from '@/hooks/useUserStats';
import { useAuthStore } from '@/store/authStore';
import { Toast } from '@/components/ui/Toast';

// Remove mock data import - use only real data from APIs
import { EmptyBoardsMessage } from '@/components/ui/EmptyBoardsMessage';
import { ChangeProfilePhotoModal } from '@/components/ui/ChangeProfilePhotoModal';
import { ProfileMenu } from '@/components/ui/ProfileMenu';
import { boardsApiService } from '@/services/api/boardsApiService';
import { Board } from '@/services/boardsService';
import { pinsService } from '@/services/pinsService';
import { Pin as UIPin } from '@/types/pin';
import { useUserPins } from '@/hooks/api/useApiQueries';

import { followService, FollowStats } from '@/services/followService';
import { userNameUtils } from '@/utils/nameUtils';
import { profileUtils } from '@/utils/profileUtils';
import { FollowButton, FollowStats as FollowStatsComponent } from '@/components/ui/FollowButton';
import { Button } from '@/components/ui/design-system/Button';
import { colorTokens } from '@/components/ui/design-system/foundations';
import { 
  RectangleStackIcon, 
  ArrowPathIcon, 
  MapPinIcon,
  HeartIcon,
  ChatBubbleLeftIcon,

  PlusIcon,
  BookmarkIcon,
  UserPlusIcon,
  EnvelopeIcon,
  StarIcon,
  ShieldCheckIcon,
  CalendarIcon,
  MapIcon,
  Squares2X2Icon,
  ListBulletIcon,
  XMarkIcon,
  UserIcon,
  PencilIcon,
  LockClosedIcon,
  ShareIcon,
  LinkIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { 
  HeartIcon as HeartSolidIcon,
  BookmarkIcon as BookmarkSolidIcon 
} from '@heroicons/react/24/solid';

// Types - using Board from boardsService

type ViewMode = 'boards' | 'pins';

export const PublicProfile: React.FC = () => {
  const { username } = useParams<{ username: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { permissions, user: currentUser } = useAuthStore();
  const queryClient = useQueryClient();
  const { showToast } = useToast();
  // Remove local states - we'll use React Query data directly
  const [viewMode, setViewMode] = useState<ViewMode>('boards');
  const [showStatsModal, setShowStatsModal] = useState(false);
  const [statsModalTab, setStatsModalTab] = useState<'pins' | 'checkIns'>('pins');
  const [showPhotoEditor, setShowPhotoEditor] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isViewingAsPublic, setIsViewingAsPublic] = useState(false);
  const [isImpersonating, setIsImpersonating] = useState(false);
  const [originalUser, setOriginalUser] = useState<AdminUser | null>(null);
  const [followersModalOpen, setFollowersModalOpen] = useState(false);
  const [followingModalOpen, setFollowingModalOpen] = useState(false);
  
  // Pin Detail Modal state
  const [selectedPin, setSelectedPin] = useState<UIPin | null>(null);
  const [showPinDetailModal, setShowPinDetailModal] = useState(false);
  
  // Share Profile state
  const [showShareProfile, setShowShareProfile] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);

  // Remove the old useEffect and loadUserData function - we'll use React Query exclusively

  // Determine if we're using username or userId
  const isUsernameRoute = !!username;
  const identifier = username;
  
  // Check if we're in public view mode (accessed via /u/ or /public/ route)
  const isPublicView = location.pathname.startsWith('/u/') || location.pathname.startsWith('/public/');

  // Fetch user data by username or userId, or use current user if no params
  const { data: userData, isLoading: userDataLoading, error: userDataError } = useQuery({
    queryKey: ['user', identifier, isUsernameRoute ? 'username' : 'id'],
    queryFn: async () => {
      console.log('🔍 PublicProfile: Fetching user data', { identifier, isUsernameRoute, username });
      
      // If no identifier, this is the current user's own profile - fetch fresh data from DB
      if (!identifier && currentUser) {
        console.log('🔍 PublicProfile: Fetching current user data from DB');
        const userData = await usersService.getById(currentUser.id);
        if (userData) {
          return userData;
        }
        // Fallback to currentUser if DB fetch fails
        return currentUser;
      }
      
      if (isUsernameRoute && username) {
        console.log('🔍 PublicProfile: Fetching user by username:', username);
        // Fetch by username - but then get full data by ID to ensure consistency
        const userData = await UsernameService.getUserByUsername(username);
        if (!userData) {
          console.error('❌ PublicProfile: User not found by username:', username);
          throw new Error('User not found');
        }
        console.log('✅ PublicProfile: User found by username:', userData);
        // Re-fetch using usersService to ensure proper data transformation
        const fullUserData = await usersService.getById(userData.id);
        return fullUserData || userData;
      } else if (username) {
        console.log('🔍 PublicProfile: Fetching user by ID:', username);
        // Fetch by ID (fallback to existing service)
        return usersService.getById(username);
      }
      throw new Error('No identifier provided');
    },
    enabled: !!identifier || !!currentUser,
    // Force refetch to avoid stale cache issues
    staleTime: 0,
  });

  // Check if this is the current user's own profile
  // In public view mode, always treat as if viewing someone else's profile
  const isOwnProfile = !isPublicView && userData && currentUser && (
    userData.id === currentUser.id || 
    userData.email === currentUser.email ||
    userData.username === currentUser.username
  );

  // Check if profile is private and user is not the owner
  // Default to public if publicProfile is undefined (backwards compatibility)
  const publicProfile = userData?.preferences?.publicProfile ?? true;
  const isPrivateProfile = userData && !publicProfile && !isOwnProfile;
  
  // Force debug - temporarily override for testing
  const debugPrivateProfile = false; // Set to true to force public view for testing

  // Debug logs for privacy settings
  React.useEffect(() => {
    if (userData) {
      console.log('🔍 PublicProfile Debug:', {
        userId: userData.id,
        userEmail: userData.email,
        currentUserId: currentUser?.id,
        currentUserEmail: currentUser?.email,
        isOwnProfile,
        isPublicView,
        userPreferences: userData.preferences,
        publicProfile: userData.preferences?.publicProfile,
        publicProfileCalculated: publicProfile,
        isPrivateProfile,
        pathname: location.pathname,
        // Privacy calculation breakdown
        debugPrivateProfile: {
          publicProfileValue: userData.preferences?.publicProfile,
          publicProfileCalculated: publicProfile,
          isPrivateProfile
        }
      });
      
      // Alert if there's a mismatch
      if (userData.preferences?.publicProfile === true && isPrivateProfile) {
        console.error('🚨 PRIVACY MISMATCH: User has publicProfile=true but isPrivateProfile=true!', {
          publicProfile: userData.preferences?.publicProfile,
          isPrivateProfile,
          isOwnProfile,
          isPublicView,
          userPreferences: userData.preferences
        });
      }
    }
  }, [userData, currentUser, isOwnProfile, isPublicView, isPrivateProfile]);

  // Fetch user statistics
  const { data: userStats } = useUserStats(userData?.id);

  // Fetch user boards
  const { data: userBoards, isLoading: boardsLoading, error: boardsError } = useQuery({
    queryKey: ['userBoards', userData?.id],
    queryFn: async () => {
      console.log('🔍 PublicProfile: Fetching user boards for user:', userData!.id, 'isOwnProfile:', isOwnProfile);
      try {
        const boards = await boardsApiService.getUserBoards(userData!.id, isOwnProfile);
        console.log('✅ PublicProfile: User boards fetched:', boards);
        return boards;
      } catch (error) {
        console.error('❌ PublicProfile: Error fetching user boards:', error);
        throw error;
      }
    },
    enabled: !!userData?.id,
    retry: 1,
  });

  // Fetch user pins using the new hook
  const { data: userPins, isLoading: pinsLoading, error: pinsError } = useUserPins(userData?.id);

  // User activities removed - not implemented in the current system

  // Follow functionality now handled by FollowButton and FollowStats components

  // Auto-switch to pins tab if user has pins but no boards
  React.useEffect(() => {
    if (userPins && userBoards && !pinsLoading && !boardsLoading) {
      const hasPins = userPins.length > 0;
      const hasBoards = userBoards.length > 0;
      
      // If user has pins but no boards, switch to pins tab
      if (hasPins && !hasBoards && viewMode === 'boards') {
        console.log('🔄 Auto-switching to pins tab (has pins but no boards)');
        setViewMode('pins');
      }
    }
  }, [userPins, userBoards, pinsLoading, boardsLoading, viewMode]);

  const handleStatsClick = (tab: 'pins' | 'checkIns') => {
    setStatsModalTab(tab);
    setShowStatsModal(true);
  };

  // handleFollow removed - now handled by FollowButton component
  
  const handleShareProfile = () => {
    setShowShareProfile(true);
  };
  
  const handleCopyProfileLink = async () => {
    const profileUrl = `${window.location.origin}/u/${userData?.username || userData?.id}`;
    
    const success = await copyToClipboard(profileUrl, {
      onSuccess: () => {
        setLinkCopied(true);
        showToast('Profile link copied to clipboard!', 'success');
        setTimeout(() => setLinkCopied(false), 2000);
      },
      onError: (error) => {
        console.error('Failed to copy profile link:', error);
        showToast('Failed to copy link. Please copy manually.', 'error');
      },
      fallbackInputId: `profile-url-${userData?.id}`
    });
  };
  
  const handleShareVia = (platform: string) => {
    if (!userData) return;
    
    const profileUrl = `${window.location.origin}/u/${userData.username || userData.id}`;
    const shareText = `Check out ${userNameUtils.getFullName(userData)}'s profile on PinPal!`;
    const encodedText = encodeURIComponent(shareText);
    const encodedUrl = encodeURIComponent(profileUrl);
    
    let shareUrl = '';
    
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedText}`;
        break;
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodedText}%20${encodedUrl}`;
        break;
      case 'email':
        shareUrl = `mailto:?subject=${encodeURIComponent(`Check out ${userNameUtils.getFullName(userData)}'s profile`)}&body=${encodedText}%0A%0A${encodedUrl}`;
        break;
      default:
        return;
    }
    
    window.open(shareUrl, '_blank', 'width=600,height=400');
    showToast(`Sharing via ${platform}`, 'info');
    setShowShareProfile(false);
  };

  const handleViewAsPublic = () => {
    console.log('🔄 handleViewAsPublic clicked:', {
      user: userData?.id,
      username: userData?.username,
      publicProfile: userData?.preferences?.publicProfile,
      currentPath: location.pathname,
      willNavigateTo: userData?.username ? `/u/${userData.username}` : `/public/${userData?.id}`
    });
    
    // Navigate to public profile view
    if (userData?.username) {
      navigate(`/u/${userData.username}`);
    } else {
      navigate(`/public/${userData?.id}`);
    }
  };

  const handleBackToOwnProfile = () => {
    // Navigate back to own profile view
    if (currentUser) {
      navigate(profileUtils.getOwnProfileUrl(currentUser));
    } else {
      navigate('/profile');
    }
  };

  const handleUploadPhoto = async (file: File) => {
    if (!userData || !currentUser || !isOwnProfile) {
      console.error('Cannot upload photo: insufficient permissions');
      return;
    }

    try {
      console.log('🔄 Starting photo upload:', file.name);
      showToast('Uploading photo...');

      // Import Firebase Storage functions
      const { ref, uploadBytes, getDownloadURL, deleteObject } = await import('firebase/storage');
      const { storage } = await import('@/services/firebase');
      const { updateProfile } = await import('firebase/auth');
      const { auth } = await import('@/services/firebase');

      // Resize image if necessary
      const resizedFile = await resizeImageFile(file, 512, 512);
      
      // Delete old photo if it exists and is from Firebase Storage
      if (userData.avatarUrl && userData.avatarUrl.startsWith('https://firebasestorage.googleapis.com')) {
        try {
          const match = userData.avatarUrl.match(/\/o\/(.*?)\?/);
          if (match && match[1]) {
            const filePath = decodeURIComponent(match[1]);
            const oldFileRef = ref(storage, filePath);
            await deleteObject(oldFileRef);
            console.log('✅ Old photo deleted from Storage');
          }
        } catch (error) {
          console.warn('⚠️ Could not delete old photo:', error);
        }
      }
      
      // Upload new photo to Firebase Storage
      const storageRef = ref(storage, `avatars/${userData.id}/${Date.now()}_${file.name}`);
      await uploadBytes(storageRef, resizedFile);
      const downloadURL = await getDownloadURL(storageRef);
      
      console.log('✅ Photo uploaded to Storage:', downloadURL);
      
      // Update Firebase Auth profile
      if (auth.currentUser) {
        await updateProfile(auth.currentUser, { photoURL: downloadURL });
        console.log('✅ Firebase Auth profile updated');
      }
      
      // Update user in Firestore via usersService
      await usersService.update(userData.id, { avatarUrl: downloadURL });
      console.log('✅ User profile updated in Firestore');
      
      // Update authStore with new avatar URL
      const { useAuthStore } = await import('@/store/authStore');
      const authStore = useAuthStore.getState();
      if (authStore.user && authStore.user.id === userData.id) {
        authStore.setUser({
          ...authStore.user,
          avatarUrl: downloadURL
        });
        console.log('✅ AuthStore updated with new avatar');
      }
      
      // Invalidate React Query cache to force refresh
      queryClient.invalidateQueries({ queryKey: ['user', identifier, isUsernameRoute ? 'username' : 'id'] });
      queryClient.invalidateQueries({ queryKey: ['user', userData.id, 'id'] });
      console.log('✅ React Query cache invalidated');
      
      showToast('Photo uploaded successfully!');
      
    } catch (error) {
      console.error('❌ Error uploading photo:', error);
      showToast('Failed to upload photo. Please try again.', 'error');
    }
  };

  const handleRemovePhoto = async () => {
    if (!userData || !currentUser || !isOwnProfile) {
      console.error('Cannot remove photo: insufficient permissions');
      return;
    }

    try {
      console.log('🔄 Removing photo');
      showToast('Removing photo...');

      // Import Firebase functions
      const { ref, deleteObject } = await import('firebase/storage');
      const { storage } = await import('@/services/firebase');
      const { updateProfile } = await import('firebase/auth');
      const { auth } = await import('@/services/firebase');
      const { DEFAULT_AVATAR } = await import('@/store/authStore');

      // Delete photo from Firebase Storage if it exists
      if (userData.avatarUrl && userData.avatarUrl.startsWith('https://firebasestorage.googleapis.com')) {
        try {
          const match = userData.avatarUrl.match(/\/o\/(.*?)\?/);
          if (match && match[1]) {
            const filePath = decodeURIComponent(match[1]);
            const fileRef = ref(storage, filePath);
            await deleteObject(fileRef);
            console.log('✅ Photo deleted from Storage');
          }
        } catch (error) {
          console.warn('⚠️ Could not delete photo from Storage:', error);
        }
      }
      
      // Update Firebase Auth profile
      if (auth.currentUser) {
        await updateProfile(auth.currentUser, { photoURL: DEFAULT_AVATAR });
        console.log('✅ Firebase Auth profile updated');
      }
      
      // Update user in Firestore via usersService
      await usersService.update(userData.id, { avatarUrl: DEFAULT_AVATAR });
      console.log('✅ User profile updated in Firestore');
      
      // Update authStore with default avatar
      const { useAuthStore } = await import('@/store/authStore');
      const authStore = useAuthStore.getState();
      if (authStore.user && authStore.user.id === userData.id) {
        authStore.setUser({
          ...authStore.user,
          avatarUrl: DEFAULT_AVATAR
        });
        console.log('✅ AuthStore updated with default avatar');
      }
      
      // Invalidate React Query cache to force refresh
      queryClient.invalidateQueries({ queryKey: ['user', identifier, isUsernameRoute ? 'username' : 'id'] });
      queryClient.invalidateQueries({ queryKey: ['user', userData.id, 'id'] });
      console.log('✅ React Query cache invalidated');
      
      showToast('Photo removed successfully!');
      
    } catch (error) {
      console.error('❌ Error removing photo:', error);
      showToast('Failed to remove photo. Please try again.', 'error');
    }
  };

  // Utility function to resize image
  const resizeImageFile = async (file: File, maxWidth: number, maxHeight: number): Promise<File> => {
    return new Promise((resolve) => {
      const img = new window.Image();
      const url = URL.createObjectURL(file);
      img.onload = () => {
        let { width, height } = img;
        if (width > maxWidth || height > maxHeight) {
          const scale = Math.min(maxWidth / width, maxHeight / height);
          width = Math.round(width * scale);
          height = Math.round(height * scale);
        }
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        ctx?.drawImage(img, 0, 0, width, height);
        canvas.toBlob((blob) => {
          if (blob) {
            const resized = new File([blob], file.name, { type: file.type });
            resolve(resized);
          } else {
            resolve(file);
          }
        }, file.type);
        URL.revokeObjectURL(url);
      };
      img.onerror = () => resolve(file);
      img.src = url;
    });
  };

  const handleAvatarClick = () => {
    if (isOwnProfile) {
      setShowPhotoEditor(true);
    }
  };

  // Calculate real statistics from user data
  const calculateUserStats = () => {
    const boards = userBoards || [];
    const pins = userPins || [];
    
    console.log('📊 Calculating user stats:', {
      boardsCount: boards.length,
      pinsFromService: pins.length,
      boards: boards.map(b => ({ id: b.id, name: b.name, pinCount: b.pinCount }))
    });
    
    // For now, use the pins from pinsService as the authoritative source
    // The pinCount in boards might be outdated or mock data
    const totalPins = pins.length;
    
    console.log('📊 Final pin calculation:', {
      pinsFromService: pins.length,
      finalTotal: totalPins
    });
    
    // Follow stats will be handled by FollowStats component
    const followersCount = userData?.statistics?.followersCount ?? 0;
    const followingCount = userData?.statistics?.followingCount ?? 0;
    
    return {
      pins: totalPins,
      followers: followersCount,
      following: followingCount,
      boards: boards.length
    };
  };

  const realStats = calculateUserStats();

  // Get user data (real data from APIs)
  const currentUserData = {
    boards: userBoards || [],
    recentActivity: [], // Activities not implemented
    followers: realStats.followers,
    following: realStats.following,
    isFollowing: false,
    isOwnProfile: isOwnProfile || false
  };

  const handleImpersonate = () => {
    if (!userData || !permissions.canAccessAdmin) {
      console.error('Cannot impersonate: insufficient permissions');
      return;
    }

    // Convert user to AdminUser format for impersonation
    const adminUserData = {
      ...userData,
      status: userData.status || 'active',
      role: userData.role || 'user',
      emailVerified: userData.emailVerified || false,
      createdAt: userData.createdAt || new Date(),
      updatedAt: userData.updatedAt || new Date(),
      preferences: userData.preferences || {
        notifications: true,
        publicProfile: true,
        allowMessages: true
      },
      statistics: userData.statistics || {
        totalPins: 0,
        totalTrades: 0,
        totalCheckIns: 0,
        joinedAt: userData.createdAt || new Date()
      }
    };

    // TODO: Implement impersonation functionality
    console.log('Impersonation not implemented yet:', adminUserData);
    
    // Navigate to home page as the impersonated user
    navigate('/');
  };

  // Pin interaction handlers
  const handlePinLike = async (pinId: string) => {
    if (!currentUser) {
      showToast('Please sign in to like pins', 'error');
      return;
    }

    try {
      console.log('🔄 Toggling like for pin:', pinId);
      // TODO: Implement toggleLike method in pinsService
      console.log('Like functionality not implemented yet');
      showToast('Like functionality coming soon!', 'info');
    } catch (error) {
      console.error('❌ Error toggling pin like:', error);
      showToast('Failed to like pin', 'error');
    }
  };

  const handlePinSave = async (pinId: string) => {
    if (!currentUser) {
      showToast('Please sign in to save pins', 'error');
      return;
    }

    try {
      console.log('🔄 Toggling save for pin:', pinId);
      // Get current save status first
      const currentPin = userPins?.find((p: any) => p.id === pinId);
      const isSaved = currentPin?.isSaved || false;
      
      await pinsService.toggleSaved(pinId, !isSaved);
      
      // Invalidate user pins query to refresh the data
      queryClient.invalidateQueries({ 
        queryKey: ['pins', 'user', userData?.id] 
      });
      
      console.log('✅ Pin save toggled successfully');
    } catch (error) {
      console.error('❌ Error toggling pin save:', error);
      showToast('Failed to save pin', 'error');
    }
  };

  const handlePinComment = (pinId: string) => {
    // Find the pin and open the detail modal
    const pin = userPins?.find((p: any) => p.id === pinId);
    if (pin) {
      // Convert PostgreSQL format to UI format for modal
      const uiPin: UIPin = {
        id: pin.id,
        name: pin.title,
        image: pin.imageUrl,
        description: pin.description,
        category: 'other' as any,
        rarity: 'common' as any,
        condition: 'new' as any,
        isForTrade: pin.tradable,
        likes: pin.likesCount,
        comments: pin.commentsCount,
        userId: pin.userId,
        createdAt: new Date(pin.createdAt),
        updatedAt: new Date(pin.updatedAt),
        owner: pin.user ? {
          id: pin.user.id,
          name: pin.user.displayName,
          username: pin.user.username,
          avatar: pin.user.avatarUrl
        } : undefined
      };
      
      setSelectedPin(uiPin);
      setShowPinDetailModal(true);
    }
  };

  const handlePinClick = (pinId: string) => {
    // Same as comment handler - open detail modal
    handlePinComment(pinId);
  };

  // Debug loading states
  React.useEffect(() => {
    console.log('🔍 PublicProfile: Loading states:', {
      userDataLoading,
      pinsLoading,
      boardsLoading,
      user: !!userData,
      userPins: userPins?.length || 0,
      userBoards: userBoards?.length || 0,
      pinsError: !!pinsError,
      boardsError: !!boardsError,
      error: !!userDataError
    });
  }, [userDataLoading, pinsLoading, boardsLoading, userData, userPins, userBoards, pinsError, boardsError, userDataError]);

  // Use React Query loading state instead of local state
  const isLoading = userDataLoading || boardsLoading || pinsLoading;

  if (isLoading) {
    console.log('🔄 PublicProfile: Showing loading state');
    return (
      <div className="min-h-screen" style={{ backgroundColor: 'var(--color-black)' }}>
        <div className="animate-pulse">
          {/* Header skeleton */}
          <div style={{ backgroundColor: 'var(--color-black)' }} className="border-b border-gray-200 dark:border-gray-700">
            <div className="max-w-7xl mx-auto px-4 py-8">
              <div className="flex items-center space-x-6">
                <div className="w-32 h-32 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1 space-y-4">
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-64"></div>
                  <div className="flex space-x-4">
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Content skeleton */}
          <div className="max-w-7xl mx-auto px-4 py-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (userDataError || !userData) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--color-black)' }}>
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">User Not Found</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            The user you're looking for could not be found.
          </p>
          <button
            onClick={() => navigate('/admin/users')}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Users
          </button>
        </div>
      </div>
    );
  }

  const getRoleBadge = (role: string) => {
    // Não exibir badge para usuários normais (role: 'user')
    if (role === 'user') {
      return null;
    }

    const roleConfig = {
      admin: { color: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400', icon: StarIcon },
      moderator: { color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400', icon: ShieldCheckIcon },
      ambassador: { color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400', icon: StarIcon }
    };

    const config = roleConfig[role as keyof typeof roleConfig];
    if (!config) return null;

    const IconComponent = config.icon;

    return (
      <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${config.color}`}>
        {IconComponent && <IconComponent className="w-3 h-3 mr-1" />}
        {role}
      </span>
    );
  };

  // Função utilitária para normalizar dados do board (igual ao DraggableBoardGrid)
  const normalizeBoardData = (board: any) => {
    const pinCount = board.pinCount || board.stats?.pinsCount || 0;
    
    return {
      id: board.id,
      name: board.name,
      description: board.description || '',
              // CORREÇÃO: Suporte para dados PostgreSQL (cover_image_url) e Firebase (coverImageUrl)
        coverImage: (board as any).cover_image_url || board.coverImageUrl || board.coverImage || '',
      pinCount: pinCount,
      isPrivate: board.isPrivate !== undefined ? board.isPrivate : !board.isPublic,
      pins: [],
      lastUpdated: board.lastUpdated || board.updatedAt || board.createdAt
    };
  };

  const renderBoardsView = () => {
    const boards = currentUserData.boards || [];
    
    if (boards.length === 0) {
      return (
        <div className="text-center py-12">
          <EmptyBoardsMessage isOwnProfile={isOwnProfile || false} context="boards" />
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {boards.map((board: any) => {
          // Normalizar dados do board para o formato esperado pelo BoardCard
          const normalizedBoard = normalizeBoardData(board);
          
          return (
            <BoardCard
              key={board.id}
              board={normalizedBoard}
              onClick={(boardId) => {
                navigate(`/boards/${boardId}`);
              }}
              showActionsMenu={false} // Não mostrar menu de ações no perfil público
            />
          );
        })}
      </div>
    );
  };

  const renderPinsView = () => {
    // Use pins from pinsService as the authoritative source
    const allPins = userPins || [];
    const boards = currentUserData.boards || [];
    
    console.log('🔍 renderPinsView Debug:', {
      userPins,
      allPins,
      allPinsLength: allPins.length,
      pinsLoading,
      pinsError,
      userData: userData?.id
    });
    

    
    // Show loading state
    if (pinsLoading) {
      return (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading pins...</p>
        </div>
      );
    }
    
    // Show error state
    if (pinsError) {
      return (
        <div className="text-center py-12">
          <div className="text-red-600 dark:text-red-400 mb-4">
            <p>Error loading pins: {String(pinsError)}</p>
          </div>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      );
    }
    
    if (allPins.length === 0) {
      // Verificar se existem boards mas estão vazios
      const hasEmptyBoards = boards.length > 0;
      

      
      return (
        <div className="text-center py-12">
          <EmptyBoardsMessage 
            isOwnProfile={isOwnProfile || false} 
            context="pins" 
            hasEmptyBoards={hasEmptyBoards}
            boardsCount={boards.length}
          />
        </div>
      );
    }
    
    // Convert PostgreSQL Pin format to UI Pin format
    const convertedPins: UIPin[] = allPins.map((pin: any) => ({
      id: pin.id,
      name: pin.title,
      image: pin.imageUrl,
      description: pin.description,
      category: 'other' as any,
      rarity: 'common' as any,
      condition: 'new' as any,
      isForTrade: pin.tradable,
      likes: pin.likesCount,
      comments: pin.commentsCount,
      userId: pin.userId,
      createdAt: new Date(pin.createdAt),
      updatedAt: new Date(pin.updatedAt)
    }));

    console.log('🎯 Converted pins for rendering:', {
      convertedPinsLength: convertedPins.length,
      firstPin: convertedPins[0],
      allConvertedPins: convertedPins.map(p => ({ id: p.id, name: p.name, image: p.image }))
    });

    return (
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
        {convertedPins.map((pin) => (
          <PinCard
            key={pin.id}
            pin={pin}
            variant="grid"
            showOverlayActions={false}
            showContent={true}
            onLike={handlePinLike}
            onSave={handlePinSave}
            onComment={handlePinComment}
            onClick={handlePinClick}
            onUserClick={(userId) => {
              navigate(`/profile/${userId}`);
            }}
          />
        ))}
      </div>
    );
  };

  // Activity view removed - not implemented in the current system

  // Check if current user is viewing their own profile in public mode
  const isViewingOwnProfileAsPublic = isPublicView && userData && currentUser && (
    userData.id === currentUser.id || userData.email === currentUser.email
  );

  return (
    <>

      {/* Public View Banner - Outside the main container */}
      {isViewingOwnProfileAsPublic && (
        <div className="max-w-7xl mx-auto mb-4 px-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <UserIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                    Visualização Pública
                  </span>
                </div>
                <span className="text-sm text-blue-700 dark:text-blue-300">
                  Você está vendo seu perfil como outros usuários o veem
                </span>
              </div>
              <button
                onClick={handleBackToOwnProfile}
                className="px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors"
              >
                Voltar ao meu perfil
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Private Profile Message */}
      {(isPrivateProfile && !debugPrivateProfile) && (
        <div className="min-h-screen" style={{ backgroundColor: 'var(--color-black)' }}>
          <div style={{ backgroundColor: 'var(--color-black)' }} className="border-b border-gray-200 dark:border-custom-gray-800">
            <div className="px-4 py-8">
              <div className="flex flex-col md:flex-row items-center md:items-center space-y-6 md:space-y-0 md:space-x-8">
                {/* Avatar */}
                <div className="flex-shrink-0">
                  <Avatar firstName={userData.firstName} lastName={userData.lastName} src={userData.avatarUrl} size={128} />
                </div>

                {/* User Info */}
                <div className="flex-1 min-w-0 text-center md:text-left">
                  <div className="text-center md:text-left">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                      {userNameUtils.getFullName(userData)}
                    </h1>
                    {userData.username && (
                      <p className="text-gray-600 dark:text-gray-400 mb-3">
                        @{userData.username}
                      </p>
                    )}
                    <div className="flex items-center justify-center md:justify-start space-x-2 mb-3">
                      {getRoleBadge(userData.role)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Private Profile Content */}
          <div className="px-4 py-16 text-center">
            <div className="p-8" style={{ backgroundColor: colorTokens.surface.elevated }}>
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <LockClosedIcon className="w-8 h-8 text-gray-400 dark:text-gray-500" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                This Account is Private
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Follow this account to see their pins and boards.
              </p>
              {currentUser && userData && (
                <FollowButton userId={userData.id} size="md" variant="primary" />
              )}
            </div>
          </div>
        </div>
      )}

      {(!isPrivateProfile || debugPrivateProfile) && (
      <div className="min-h-screen" style={{ backgroundColor: 'var(--color-black)' }}>
        {/* Profile Header */}
      <div style={{ backgroundColor: 'var(--color-black)' }}>
        <div className="px-4 py-8">
          <div className="flex flex-col md:flex-row items-center md:items-center space-y-6 md:space-y-0 md:space-x-8">
            {/* Avatar */}
            <div className="flex-shrink-0">
              <div 
                className={`relative ${isOwnProfile ? 'cursor-pointer group' : ''}`}
                onClick={handleAvatarClick}
              >
                <Avatar firstName={userData.firstName} lastName={userData.lastName} src={userData.avatarUrl} size={128} />
                {isOwnProfile && (
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-white dark:bg-white dark:bg-black/50 rounded-full">
                    <span className="text-gray-900 dark:text-white text-sm font-medium">Change Photo</span>
                  </div>
                )}
              </div>
            </div>

            {/* User Info */}
            <div className="flex-1 min-w-0 text-center md:text-left">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                <div className="text-center md:text-left">
                  {/* Name and Action Buttons Row */}
                  <div className="flex flex-col md:flex-row md:items-center md:gap-4 mb-2">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                      {userNameUtils.getFullName(userData)}
                    </h1>
                    
                                         {/* Follow and Message Buttons - only for other users */}
                     {!isOwnProfile && currentUser && userData && currentUser.id !== userData.id && (
                       <div className="flex items-center gap-2 mt-2 md:mt-0 justify-center md:justify-start">
                         <FollowButton 
                           userId={userData.id} 
                           size="sm" 
                           variant="primary" 
                           showIcon={false}
                         />
                         <Button
                           onClick={() => navigate('/messages', { 
                             state: { 
                               startConversationWith: userData.id,
                               userName: userNameUtils.getFullName(userData)
                             } 
                           })}
                           size="sm"
                           variant="secondary"
                         >
                           Message
                         </Button>
                         <Button
                           onClick={handleShareProfile}
                           size="sm"
                           variant="outline"
                         >
                           <ShareIcon className="w-4 h-4" />
                         </Button>
                       </div>
                     )}
                  </div>
                  
                  {userData.username && (
                    <p className="text-gray-600 dark:text-gray-400 mb-2">
                      @{userData.username}
                    </p>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-center md:justify-start space-x-3">
                  {isOwnProfile && (
                    // Own profile actions
                    <button
                      onClick={() => navigate('/settings')}
                      className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <PencilIcon className="w-4 h-4 inline mr-2" />
                      Edit Profile
                    </button>
                  )}
                  
                  <ProfileMenu
                    isOpen={isImpersonating}
                    onToggle={() => setIsImpersonating(!isImpersonating)}
                    onClose={() => setIsImpersonating(false)}
                    isOwnProfile={isOwnProfile || false}
                    user={userData}
                    onViewAsPublic={handleViewAsPublic}
                    onImpersonate={handleImpersonate}
                  />
                </div>
              </div>

              {/* Bio */}
              {userData.bio && (
                <p className="text-gray-600 dark:text-gray-400 mb-3 max-w-2xl text-center md:text-left">
                  {userData.bio}
                </p>
              )}

              {/* Stats */}
              <div className="flex items-center justify-center md:justify-start space-x-8 mb-3">
                <button
                  onClick={() => handleStatsClick('pins')}
                  className="flex items-center gap-1 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                >
                  <span className="font-semibold text-gray-900 dark:text-white">{realStats.pins.toLocaleString()}</span>
                  <span className="text-sm text-gray-400">Pins</span>
                </button>
                <FollowStatsComponent userId={userData.id} showLabels={true} className="flex gap-8" />
              </div>

              {/* Additional Info */}
              <div className="flex items-center justify-center md:justify-start space-x-4 text-sm text-gray-600 dark:text-gray-400">
                {/* Location Display */}
                {userData.location && userData.preferences?.showLocation && (
                  <div className="flex items-center space-x-1">
                    <MapIcon className="w-4 h-4" />
                    <span>{userData.location}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="sticky top-0 z-10" style={{ backgroundColor: 'var(--color-black)' }}>
        <div className="px-4">
          <div className="flex justify-between items-center">
          <div className="flex space-x-8">
            <button
              onClick={() => setViewMode('boards')}
              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                viewMode === 'boards'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <Squares2X2Icon className="w-4 h-4 inline mr-2" />
              Boards ({realStats.boards})
            </button>
            <button
              onClick={() => setViewMode('pins')}
              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                viewMode === 'pins'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <RectangleStackIcon className="w-4 h-4 inline mr-2" />
              All Pins ({realStats.pins})
            </button>
            </div>
            
            {/* Create Board Button - only show for own profile and boards view */}
            {isOwnProfile && viewMode === 'boards' && (
              <button
                onClick={() => navigate('/boards/create', { 
                  state: { 
                    from: location,
                    context: 'boards',
                    returnTo: location.pathname 
                  } 
                })}
                className="flex items-center px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors"
              >
                <PlusIcon className="w-4 h-4 mr-1" />
                Create Board
              </button>
            )}
          </div>
        </div>
        {/* Simple gray line at bottom */}
        <div className="border-b border-gray-200 dark:border-gray-700"></div>
      </div>

      {/* Content */}
      <div className="px-4 py-8 w-full">

        
        {viewMode === 'boards' && renderBoardsView()}
        {viewMode === 'pins' && renderPinsView()}
      </div>



      {/* Stats Detail Modal */}
      {userStats && (
        <UserStatsModal
          isOpen={showStatsModal}
          onClose={() => setShowStatsModal(false)}
                              userName={userNameUtils.getFullName(userData)}
          userAvatar={userData.avatarUrl}
          stats={{
            pins: userStats.pins,
            checkIns: userStats.checkIns
          }}
          activeTab={statsModalTab}
        />
      )}

      {/* Followers Modal */}
      <FollowersModal
        userId={userData?.id || ''}
        isOpen={followersModalOpen}
        onClose={() => setFollowersModalOpen(false)}
      />

      {/* Following Modal */}
      <FollowingModal
        userId={userData?.id || ''}
        isOpen={followingModalOpen}
        onClose={() => setFollowingModalOpen(false)}
      />

      {/* Change Profile Photo Modal */}
      <ChangeProfilePhotoModal
        isOpen={showPhotoEditor}
        onClose={() => setShowPhotoEditor(false)}
        onUploadPhoto={handleUploadPhoto}
        onRemovePhoto={handleRemovePhoto}
        hasCurrentPhoto={!!(userData.avatarUrl && userData.avatarUrl.trim() !== '')}
      />

      {/* Pin Detail Modal */}
      {selectedPin && (
        <PinDetailModal
          pin={selectedPin}
          isOpen={showPinDetailModal}
          onClose={() => {
            setShowPinDetailModal(false);
            setSelectedPin(null);
          }}
          onLike={handlePinLike}
          onSave={handlePinSave}
          onUserClick={(userId) => {
            navigate(`/profile/${userId}`);
          }}
        />
      )}

      {/* Share Profile Modal */}
      {userData && (
        <GlassModal
          isOpen={showShareProfile}
          onClose={() => setShowShareProfile(false)}
          title={
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <ShareIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <div className="text-lg font-semibold text-gray-900 dark:text-white">
                  Share Profile
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-normal">
                  {userNameUtils.getFullName(userData)}
                </p>
              </div>
            </div>
          }
          maxWidth="md"
        >
          <div className="space-y-6">
            {/* Profile URL */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Profile URL
              </label>
              <div className="flex items-center space-x-2">
                                 <div className="flex-1 relative">
                   <input
                     id={`profile-url-${userData.id}`}
                     type="text"
                     value={`${window.location.origin}/u/${userData.username || userData.id}`}
                     readOnly
                     onClick={(e) => {
                       const target = e.target as HTMLInputElement;
                       target.select();
                       target.setSelectionRange(0, 99999);
                     }}
                     className="w-full px-3 py-2 pr-10 text-sm bg-gray-50/80 dark:bg-gray-700/50 border border-gray-300/50 dark:border-gray-600/50 rounded-lg text-gray-900 dark:text-white backdrop-blur-sm cursor-pointer"
                     title="Click to select URL"
                   />
                   <LinkIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                 </div>
                                 <Button
                   onClick={handleCopyProfileLink}
                   variant={linkCopied ? "secondary" : "outline"}
                   size="md"
                   leftIcon={linkCopied ? <CheckIcon className="w-4 h-4" /> : undefined}
                 >
                   {linkCopied ? 'Copied!' : 'Copy'}
                 </Button>
              </div>
            </div>

            {/* Share Options */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Share via
              </label>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  onClick={() => handleShareVia('twitter')}
                  variant="outline"
                  size="md"
                  fullWidth
                  leftIcon={
                    <div className="w-5 h-5 bg-blue-400 rounded-full flex items-center justify-center">
                      <span className="text-gray-900 dark:text-white text-xs font-bold">T</span>
                    </div>
                  }
                >
                  Twitter
                </Button>
                
                <Button
                  onClick={() => handleShareVia('facebook')}
                  variant="outline"
                  size="md"
                  fullWidth
                  leftIcon={
                    <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-gray-900 dark:text-white text-xs font-bold">f</span>
                    </div>
                  }
                >
                  Facebook
                </Button>
                
                <Button
                  onClick={() => handleShareVia('whatsapp')}
                  variant="outline"
                  size="md"
                  fullWidth
                  leftIcon={
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-gray-900 dark:text-white text-xs font-bold">W</span>
                    </div>
                  }
                >
                  WhatsApp
                </Button>
                
                <Button
                  onClick={() => handleShareVia('email')}
                  variant="outline"
                  size="md"
                  fullWidth
                  leftIcon={
                    <div className="w-5 h-5 bg-gray-500 rounded-full flex items-center justify-center">
                      <span className="text-gray-900 dark:text-white text-xs font-bold">@</span>
                    </div>
                  }
                >
                  Email
                </Button>
                             </div>
             </div>

             {/* QR Code */}
             <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                 QR Code
               </label>
               <QRCodeDisplay 
                 url={`${window.location.origin}/u/${userData.username || userData.id}`}
                 title={`Profile: ${userNameUtils.getFullName(userData)}`}
                 size={180}
               />
             </div>
           </div>
         </GlassModal>
       )}

      </div>
      )}
    </>
  );
}; 