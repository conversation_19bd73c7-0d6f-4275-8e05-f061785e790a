import React from 'react';
// Optimized icon imports
import PlusIcon from '@heroicons/react/24/outline/PlusIcon';
import LockClosedIcon from '@heroicons/react/24/outline/LockClosedIcon';
import PhotoIcon from '@heroicons/react/24/outline/PhotoIcon';
import { Board } from '@/types/user';
import { Timestamp } from 'firebase/firestore';
import { colorTokens } from '@/components/ui/design-system/foundations/tokens/colors';

interface BoardGridProps {
  boards: Board[];
  isOwnProfile: boolean;
  onBoardClick: (board: Board) => void;
  onCreateBoard?: () => void;
}

// Utility function to safely format timestamps
const formatTimestamp = (timestamp: Timestamp | undefined): string => {
  if (!timestamp) return 'Unknown';
  
  try {
    // If it's a Firestore Timestamp, convert to date and format
    if (timestamp && typeof timestamp === 'object' && 'toDate' in timestamp) {
      const date = timestamp.toDate();
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
    
    return 'Unknown';
  } catch (error) {
    console.warn('Error formatting timestamp:', error);
    return 'Unknown';
  }
};

export const BoardGrid: React.FC<BoardGridProps> = ({
  boards,
  isOwnProfile,
  onBoardClick,
  onCreateBoard
}) => {
  if (boards.length === 0 && !isOwnProfile) {
    return (
      <div className="text-center py-12">
        <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No boards yet</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          This user hasn't created any boards yet.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {/* Create Board Button (only for own profile) */}
      {isOwnProfile && (
        <button
          onClick={onCreateBoard}
          className="group relative bg-gray-50 dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
        >
          <div className="text-center">
            <PlusIcon className="mx-auto h-12 w-12 text-gray-400 group-hover:text-gray-500" />
            <span className="mt-2 block text-sm font-medium text-gray-900 dark:text-white">
              Create Board
            </span>
            <span className="mt-1 block text-sm text-gray-500 dark:text-gray-400">
              Organize your pins
            </span>
          </div>
        </button>
      )}

      {/* Board Cards */}
      {boards.map((board: any) => (
        <div
          key={board.id}
          onClick={() => onBoardClick(board)}
          className="group relative bg-gray-800 rounded-lg shadow-sm border hover:shadow-lg transition-all duration-300 cursor-pointer"
          style={{ backgroundColor: colorTokens.surface.elevated, borderColor: colorTokens.border.default }}
        >
          {/* Cover Image */}
          <div className="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-700 rounded-t-lg overflow-hidden">
            {/* CORREÇÃO: Suporte para dados PostgreSQL (cover_image_url) e Firebase (coverImageUrl) */}
            {(board.cover_image_url || board.coverImageUrl) ? (
              <img
                src={board.cover_image_url || board.coverImageUrl}
                alt={board.name}
                className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-200"
              />
            ) : (
              <div className="w-full h-32 flex items-center justify-center">
                <PhotoIcon className="h-8 w-8 text-gray-400" />
              </div>
            )}
            
            {/* Privacy Indicator */}
            {!(board.is_public !== undefined ? board.is_public : board.isPublic) && (
              <div className="absolute top-2 right-2">
                <div className="bg-white dark:bg-black/50 rounded-full p-1">
                  <LockClosedIcon className="h-4 w-4 text-gray-900 dark:text-white" />
                </div>
              </div>
            )}

            {/* Pin Count Overlay - CORREÇÃO: Suporte para PostgreSQL e Firebase */}
            <div className="absolute bottom-2 right-2">
              <div className="bg-white dark:bg-black/50 text-gray-900 dark:text-white text-xs px-2 py-1 rounded">
                {board.pins_count || board.pin_count || board.stats?.pinsCount || 0} pins
              </div>
            </div>
          </div>

          {/* Board Info */}
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {board.name}
            </h3>
            {board.description && (
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
                {board.description}
              </p>
            )}
            
            {/* Tags */}
            {board.tags && board.tags.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1">
                {board.tags.slice(0, 2).map((tag: string) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                  >
                    {tag}
                  </span>
                ))}
                {board.tags.length > 2 && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    +{board.tags.length - 2} more
                  </span>
                )}
              </div>
            )}

            {/* Updated Date */}
            <p className="mt-2 text-xs text-gray-400 dark:text-gray-500">
              Updated {formatTimestamp(board.updatedAt)}
            </p>
          </div>
        </div>
      ))}

      {/* Empty State for Own Profile */}
      {boards.length === 0 && isOwnProfile && (
        <div className="col-span-full text-center py-12">
          <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No boards yet</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Create your first board to organize your pins.
          </p>
          <div className="mt-6">
            <button
              onClick={onCreateBoard}
              className="inline-flex items-center px-4 py-2 shadow-sm text-sm font-medium rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              style={{ 
                backgroundColor: colorTokens.surface.elevated, 
                border: `1px solid ${colorTokens.border.default}` 
              }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = colorTokens.surface.interactive}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = colorTokens.surface.elevated}
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Board
            </button>
          </div>
        </div>
      )}
    </div>
  );
}; 