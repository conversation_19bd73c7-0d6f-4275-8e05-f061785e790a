import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
// Optimized icon imports - only import what we need
import UserIcon from '@heroicons/react/24/outline/UserIcon';
import MapPinIcon from '@heroicons/react/24/outline/MapPinIcon';
import CalendarIcon from '@heroicons/react/24/outline/CalendarIcon';
import UserPlusIcon from '@heroicons/react/24/outline/UserPlusIcon';
import UserMinusIcon from '@heroicons/react/24/outline/UserMinusIcon';
import Cog6ToothIcon from '@heroicons/react/24/outline/Cog6ToothIcon';
import EllipsisHorizontalIcon from '@heroicons/react/24/outline/EllipsisHorizontalIcon';
import UserIconSolid from '@heroicons/react/24/solid/UserIcon';
import { UserProfile as UserProfileType, UserTab, Board, Follow } from '@/types/user';
import { useAuthStore } from '@/store/authStore';
import { boardsApiService } from '@/services/api/boardsApiService';
import { useFollow } from '@/hooks/useFollow';
import { FollowersModal } from './FollowersModal';
import { FollowingModal } from './FollowingModal';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { toast } from 'react-hot-toast';
import { EmptyBoardsMessage } from '@/components/ui/EmptyBoardsMessage';
import { userNameUtils } from '@/utils/nameUtils';
import { usersService } from '@/services/usersService';
import { PageTemplate } from '@/components/layout/PageTemplate';
import { ResponsiveImage } from '@/components/ui/ResponsiveImage';
import { colorTokens } from '@/components/ui/design-system/foundations/tokens/colors';

interface UserProfileProps {
  userId?: string;
}

export const UserProfile: React.FC<UserProfileProps> = ({ userId: propUserId }) => {
  const { username } = useParams<{ username: string }>();
  const navigate = useNavigate();
  const { user: currentUser } = useAuthStore();
  
  const [profile, setProfile] = useState<UserProfileType | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<UserTab>('boards');
  const [followersModalOpen, setFollowersModalOpen] = useState(false);
  const [followingModalOpen, setFollowingModalOpen] = useState(false);

  // Debug effect
  useEffect(() => {
    console.log('🔍 Modal states changed:', { followersModalOpen, followingModalOpen });
  }, [followersModalOpen, followingModalOpen]);

  const userId = propUserId || username;
  const isOwnProfile = currentUser?.id === userId || currentUser?.username === username;
  
  // Use the follow hook for follow functionality
  const { isFollowing, toggleFollow, isLoading: followLoading } = useFollow({ 
    userId: profile?.id || '', 
    enabled: !!profile && !isOwnProfile 
  });

  useEffect(() => {
    if (userId) {
      loadUserProfile();
    }
  }, [userId]);



  const loadUserProfile = async () => {
    try {
      setLoading(true);
      
      let userProfile: UserProfileType;
      
      if (isOwnProfile && currentUser) {
        // Se é o próprio perfil, carrega dados reais do usuário logado
        try {
          const userData = await usersService.getById(currentUser.id);
          console.log('🔍 UserProfile - userData from usersService:', userData);
          if (userData) {
            userProfile = {
              ...userData,
              boards: [],
              recentPins: [],
              followers: [],
              following: [],
              isOwnProfile: true
            };
            console.log('🔍 UserProfile - final userProfile:', userProfile);
            console.log('🔍 UserProfile - location check:', {
              location: userProfile.location,
              showLocation: userProfile.preferences?.showLocation,
              shouldShow: userProfile.location && userProfile.preferences?.showLocation
            });
          } else {
            throw new Error('User data not found');
          }
        } catch (error) {
          console.error('Error loading user data from database:', error);
          // Fallback para dados do currentUser se não conseguir carregar do banco
          userProfile = {
            id: currentUser.id,
            email: currentUser.email,
            firstName: currentUser.firstName || 'User',
            lastName: currentUser.lastName || '',
            username: currentUser.username,
            avatarUrl: currentUser.avatarUrl,
            bio: currentUser.bio,
            location: currentUser.location,
            phoneNumber: currentUser.phoneNumber,
            
            // Campos obrigatórios com valores padrão
            emailVerified: currentUser.emailVerified || false,
            isActive: currentUser.isActive || true,
            isVerified: currentUser.isVerified || false,
            role: currentUser.role || 'user',
            status: currentUser.status || 'active',
            
            // Preferences com valores padrão
            preferences: currentUser.preferences || {
              notificationsEnabled: true,
              publicProfile: true,
              showLocation: true,
              showEmail: false,
              allowMessages: true,
              allowComments: true
            },
            
            // Stats com valores padrão
            stats: currentUser.stats || {
              pinsCount: 0,
              boardsCount: 0,
              followersCount: 0,
              followingCount: 0,
              checkInsCount: 0,
              tradesCompletedCount: 0,
              likesReceivedCount: 0
            },
            
            // Timestamps
            joinedAt: currentUser.joinedAt || { toDate: () => new Date() } as any,
            createdAt: currentUser.createdAt || { toDate: () => new Date() } as any,
            updatedAt: currentUser.updatedAt || { toDate: () => new Date() } as any,
            
            // Dados específicos do perfil
            boards: [],
            recentPins: [],
            followers: [],
            following: [],
            isOwnProfile: true
          };
        }
      } else {
        // Para outros usuários, usar dados mock por enquanto
        userProfile = {
          id: userId || '',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          username: username || 'johndoe',
          avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
          bio: 'Pin collector and trading enthusiast. Love Disney pins and vintage collectibles!',
          location: 'San Francisco, CA',
          phoneNumber: '+****************',
          
          // Campos obrigatórios da nova interface
          emailVerified: true,
          isActive: true,
          isVerified: true,
          role: 'user',
          status: 'active',
          
          // Preferences com nova estrutura
          preferences: {
            notificationsEnabled: true,
            publicProfile: true,
            showLocation: true,
            showEmail: false,
            allowMessages: true,
            allowComments: true
          },
          
          // Stats com nova estrutura
          stats: {
            pinsCount: 156,
            boardsCount: 12,
            followersCount: 234,
            followingCount: 89,
            checkInsCount: 45,
            tradesCompletedCount: 23,
            likesReceivedCount: 567
          },
          
          // Timestamps padronizados
          joinedAt: { toDate: () => new Date('2023-01-15') } as any,
          createdAt: { toDate: () => new Date('2023-01-15') } as any,
          updatedAt: { toDate: () => new Date() } as any,
          
          // Dados específicos do perfil
          boards: [],
          recentPins: [],
          followers: [],
          following: [],
          isOwnProfile: false
        };
      }

      // Load user's boards
      const boards = await boardsApiService.getUserBoards(userProfile.id, isOwnProfile);
      userProfile.boards = boards as unknown as Board[];

      setProfile(userProfile);
    } catch (error) {
      console.error('Error loading user profile:', error);
      toast.error('Failed to load user profile');
    } finally {
      setLoading(false);
    }
  };

  const handleFollow = () => {
    if (toggleFollow) {
      toggleFollow();
    }
  };

  const renderTabContent = () => {
    if (!profile) return null;

    switch (activeTab) {
      case 'boards':
        return (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {profile.boards.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <EmptyBoardsMessage isOwnProfile={isOwnProfile} context="boards" />
              </div>
            ) : (
              profile.boards.map((board) => (
                <div
                  key={board.id}
                  onClick={() => navigate(`/boards/${board.id}`)}
                  className="card-primary rounded-lg shadow-sm border hover:shadow-lg transition-all duration-300 cursor-pointer p-4"
                  style={{ borderColor: colorTokens.border.default }}
                >
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {board.name}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {board.stats?.pinsCount || 0} pins
                  </p>
                </div>
              ))
            )}
          </div>
        );
      case 'pins':
        const allPins = profile.recentPins || [];
        
        if (allPins.length === 0) {
          // Verificar se existem boards mas estão vazios
          const hasEmptyBoards = profile.boards.length > 0;
          
          return (
            <div className="text-center py-12">
              <EmptyBoardsMessage 
                isOwnProfile={isOwnProfile} 
                context="pins" 
                hasEmptyBoards={hasEmptyBoards}
                boardsCount={profile.boards.length}
              />
            </div>
          );
        }
        
        return (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {allPins.map((pin) => (
              <div
                key={pin.id}
                className="card-primary rounded-lg shadow-sm border hover:shadow-lg transition-all duration-300 cursor-pointer p-4"
                style={{ borderColor: colorTokens.border.default }}
              >
                <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {pin.name}
                </h3>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {pin.description}
                </p>
              </div>
            ))}
          </div>
        );
      case 'activity':
        return (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">Activity view coming soon...</p>
          </div>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <PageTemplate
        title="Profile"
        hideBackButton={false}
        onBackClick={() => navigate(-1)}
      >
        <div className="flex items-center justify-center py-16">
          <LoadingSpinner size="lg" />
        </div>
      </PageTemplate>
    );
  }

  if (!profile) {
    return (
      <PageTemplate
        title="Profile"
        hideBackButton={false}
        onBackClick={() => navigate(-1)}
      >
        <div className="text-center py-16">
          <UserIcon className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">User not found</h3>
          <p className="text-gray-400">The user you're looking for doesn't exist.</p>
        </div>
      </PageTemplate>
    );
  }

  return (
    <PageTemplate
      title={profile.username || `${profile.firstName} ${profile.lastName}`}
      hideBackButton={false}
      onBackClick={() => navigate(-1)}
      action={
        isOwnProfile ? (
          <button
            onClick={() => navigate('/settings')}
            className="p-2 text-gray-400 hover:text-gray-900 dark:text-white hover:bg-gray-800 rounded-lg transition-colors"
          >
            <Cog6ToothIcon className="w-5 h-5" />
          </button>
        ) : (
          <button className="p-2 text-gray-400 hover:text-gray-900 dark:text-white hover:bg-gray-800 rounded-lg transition-colors">
            <EllipsisHorizontalIcon className="w-5 h-5" />
          </button>
        )
      }
    >
      <div className="space-y-8">
        {/* Profile Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-6">
          {/* Avatar */}
          <div className="flex-shrink-0">
            <div className="w-24 h-24 rounded-full bg-gray-700 flex items-center justify-center overflow-hidden">
              {profile.avatarUrl ? (
                <ResponsiveImage
                  src={profile.avatarUrl}
                  alt={`${profile.firstName} ${profile.lastName}`}
                  className="w-full h-full object-cover"
                  width={96}
                  height={96}
                  priority={true}
                  loading="eager"
                />
              ) : (
                <UserIconSolid className="w-12 h-12 text-gray-400" />
              )}
            </div>
          </div>

          {/* Profile Info */}
          <div className="flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {profile.firstName} {profile.lastName}
                </h1>
                {profile.username && (
                  <p className="text-gray-400 text-lg">@{profile.username}</p>
                )}
                {profile.bio && (
                  <p className="text-gray-300 mt-2 max-w-md">{profile.bio}</p>
                )}
              </div>

              {/* Action Button */}
              {!isOwnProfile && (
                <button
                  onClick={handleFollow}
                  disabled={followLoading}
                  className={`flex items-center gap-2 px-6 py-2 rounded-lg font-medium transition-colors ${
                    isFollowing
                      ? 'bg-gray-700 text-white hover:bg-gray-600'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  } disabled:opacity-50`}
                >
                  {followLoading ? (
                    <LoadingSpinner size="sm" />
                  ) : isFollowing ? (
                    <>
                      <UserMinusIcon className="w-4 h-4" />
                      Unfollow
                    </>
                  ) : (
                    <>
                      <UserPlusIcon className="w-4 h-4" />
                      Follow
                    </>
                  )}
                </button>
              )}
            </div>

            {/* Profile Details */}
            <div className="flex flex-wrap items-center gap-4 mt-4 text-sm text-gray-400">
              {profile.location && profile.preferences?.showLocation && (
                <div className="flex items-center gap-1">
                  <MapPinIcon className="w-4 h-4" />
                  <span>{profile.location}</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <CalendarIcon className="w-4 h-4" />
                <span>
                  Joined {profile.joinedAt?.toDate?.()?.toLocaleDateString('en-US', {
                    month: 'long',
                    year: 'numeric'
                  }) || 'Recently'}
                </span>
              </div>
            </div>

            {/* Stats */}
            <div className="flex gap-6 mt-4">
              <button
                onClick={() => setFollowersModalOpen(true)}
                className="text-sm hover:text-gray-900 dark:text-white transition-colors"
              >
                <span className="font-semibold text-gray-900 dark:text-white">{profile.stats?.followersCount || 0}</span>
                <span className="text-gray-400 ml-1">followers</span>
              </button>
              <button
                onClick={() => setFollowingModalOpen(true)}
                className="text-sm hover:text-gray-900 dark:text-white transition-colors"
              >
                <span className="font-semibold text-gray-900 dark:text-white">{profile.stats?.followingCount || 0}</span>
                <span className="text-gray-400 ml-1">following</span>
              </button>
              <div className="text-sm">
                <span className="font-semibold text-gray-900 dark:text-white">{profile.stats?.pinsCount || 0}</span>
                <span className="text-gray-400 ml-1">pins</span>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-custom-gray-800">
          <nav className="flex space-x-8">
            {(['boards', 'pins'] as UserTab[]).map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-400'
                    : 'border-transparent text-gray-400 hover:text-white hover:border-gray-600'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="min-h-[400px]">
          {renderTabContent()}
        </div>
      </div>

      {/* Modals */}
      <FollowersModal
        isOpen={followersModalOpen}
        onClose={() => setFollowersModalOpen(false)}
        userId={profile.id}
      />

      <FollowingModal
        isOpen={followingModalOpen}
        onClose={() => setFollowingModalOpen(false)}
        userId={profile.id}
      />
    </PageTemplate>
  );
}; 