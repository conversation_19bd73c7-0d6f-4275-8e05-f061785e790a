import { useAuthStore } from '@/store/authStore';
import { useState, useEffect, useRef } from 'react';
import { Avatar } from '@/components/ui/Avatar';
import { updateProfile } from 'firebase/auth';
import { auth, storage } from '@/services/firebase';
import { ref, uploadBytes, getDownloadURL, ref as storageRef, deleteObject } from 'firebase/storage';
import { DEFAULT_AVATAR } from '@/store/authStore';
import { PageHeader } from '@/components/ui/PageHeader';
import { useToast } from '@/hooks/useToast';
import { CleanPageLayout } from '@/components/ui/design-system/Layout';
import { colorTokens } from '@/components/ui/design-system/foundations/tokens/colors';

export const Profile = () => {
  const user = useAuthStore((state) => state.user);
  const setUser = useAuthStore((state) => state.setUser);
  const { success, error } = useToast();
  const [editing, setEditing] = useState(false);
  const [firstName, setFirstName] = useState(() => user?.firstName || '');
  const [lastName, setLastName] = useState(() => user?.lastName || '');
  const [email, setEmail] = useState(() => user?.email || '');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploading, setUploading] = useState(false);

  const isGoogleUser = user?.avatarUrl?.includes('googleusercontent.com');

  // Placeholder handlers
  const handlePhotoChange = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };
  const handlePhotoRemove = async () => {
    if (!user) return;
    try {
      // Tenta deletar a foto antiga do Storage se for do Firebase
      if (user.avatarUrl && user.avatarUrl.startsWith('https://firebasestorage.googleapis.com')) {
        // Extrai o path entre '/o/' e '?' da URL
        const match = user.avatarUrl.match(/\/o\/(.*?)\?/);
        if (match && match[1]) {
          const filePath = decodeURIComponent(match[1]);
          const fileRef = storageRef(storage, filePath);
          await deleteObject(fileRef);
        }
      }
      // Atualiza o Auth
      if (auth.currentUser) {
        await updateProfile(auth.currentUser, { photoURL: DEFAULT_AVATAR });
      }
      // Atualiza o estado global
      setUser({ ...user, avatarUrl: DEFAULT_AVATAR });
    } catch (err) {
      error('Erro ao remover foto.');
    }
  };
  const handleSave = async () => {
    if (!user || !auth.currentUser) return;
    try {
      const displayName = `${firstName} ${lastName}`.trim();
      await updateProfile(auth.currentUser, { displayName });
      setUser({ ...user, firstName, lastName });
      setEditing(false);
    } catch (err) {
      error('Erro ao atualizar nome.');
    }
  };
  const handleCancel = () => {
    setFirstName(user?.firstName || '');
    setLastName(user?.lastName || '');
    setEmail(user?.email || '');
    setEditing(false);
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !user) return;
    setUploading(true);
    try {
      // Redimensionar imagem se necessário
      const resizedFile = await resizeImageFile(file, 512, 512);
      // Upload para o Firebase Storage
      const storageRef = ref(storage, `avatars/${user.id}/${Date.now()}_${file.name}`);
      await uploadBytes(storageRef, resizedFile);
      const url = await getDownloadURL(storageRef);
      // Atualizar no Firebase Auth
      if (auth.currentUser) {
        await updateProfile(auth.currentUser, { photoURL: url });
        setUser({
          ...user,
          avatarUrl: url,
        });
      }
      setUploading(false);
    } catch (err) {
      setUploading(false);
      console.error('Erro ao atualizar foto:', err);
      error('Erro ao atualizar foto.');
    }
  };

  // Função utilitária para redimensionar imagem
  async function resizeImageFile(file: File, maxWidth: number, maxHeight: number): Promise<File> {
    return new Promise((resolve) => {
      const img = new window.Image();
      const url = URL.createObjectURL(file);
      img.onload = () => {
        let { width, height } = img;
        if (width > maxWidth || height > maxHeight) {
          const scale = Math.min(maxWidth / width, maxHeight / height);
          width = Math.round(width * scale);
          height = Math.round(height * scale);
        }
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        ctx?.drawImage(img, 0, 0, width, height);
        canvas.toBlob((blob) => {
          if (blob) {
            const resized = new File([blob], file.name, { type: file.type });
            resolve(resized);
          } else {
            resolve(file);
          }
        }, file.type);
        URL.revokeObjectURL(url);
      };
      img.onerror = () => resolve(file);
      img.src = url;
    });
  }

  function getInitials(name?: string) {
    if (!name) return '?';
    return name
      .split(' ')
      .filter(Boolean)
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  }

  // console.log('Profile renderizou');
  useEffect(() => {
    // console.log('MOUNT');
    return () => {
      // console.log('UNMOUNT');
    };
  }, []);

  // console.log('editing:', editing);
  // console.log('user ref:', user);

  return (
    <CleanPageLayout 
      title="Your Profile" 
      backUrl="/"
      className="bg-white dark:bg-white dark:bg-black"
    >
      <div className="w-full p-4 sm:bg-white dark:bg-gray-800 sm:dark:bg-white dark:bg-gray-800 sm:rounded-2xl sm:shadow-lg sm:dark:shadow-gray-900/10 sm:max-w-2xl sm:mx-auto sm:p-10 sm:dark:border sm:dark:border-gray-200 dark:border-gray-700">
        <div className="flex flex-col items-center gap-2 mb-6">
          <Avatar name={`${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'Unknown User'} src={user?.avatarUrl} size={96} />
          <input
            type="file"
            accept="image/*,.heic,.heif"
            ref={fileInputRef}
            className="hidden"
            onChange={handleFileChange}
            disabled={uploading}
          />
          <button
            onClick={handlePhotoChange}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 font-medium hover:bg-gray-50 dark:hover:bg-gray-100 dark:bg-gray-700 w-full max-w-xs mt-2 disabled:opacity-60 dark:text-gray-700 dark:text-gray-200 transition-colors"
            disabled={uploading}
          >
            {uploading ? 'Uploading...' : 'Change photo'}
          </button>
          {user?.avatarUrl && user.avatarUrl.trim() !== '' && (
            <button onClick={handlePhotoRemove} className="text-gray-600 dark:text-gray-500 dark:text-gray-400 hover:underline w-full max-w-xs">
              Remove photo
            </button>
          )}
        </div>
        <hr className="mb-6 hidden sm:block dark:border-gray-200 dark:border-gray-700" />
        <form className="space-y-6" onSubmit={e => { e.preventDefault(); handleSave(); }}>
          <div className="px-2 sm:px-0">
            <div className="font-bold text-lg mb-1 dark:text-gray-700 dark:text-gray-200">First Name</div>
            {editing ? (
              <input
                className="mt-1 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-base w-full dark:bg-gray-100 dark:bg-gray-700 dark:text-gray-100"
                value={firstName}
                onChange={e => setFirstName(e.target.value)}
                autoFocus
              />
            ) : (
              <div className="dark:text-gray-600 dark:text-gray-300">{user?.firstName}</div>
            )}
          </div>
          <div className="px-2 sm:px-0">
            <div className="font-bold text-lg mb-1 dark:text-gray-700 dark:text-gray-200">Last Name</div>
            {editing ? (
              <input
                className="mt-1 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-base w-full dark:bg-gray-100 dark:bg-gray-700 dark:text-gray-100"
                value={lastName}
                onChange={e => setLastName(e.target.value)}
              />
            ) : (
              <div className="dark:text-gray-600 dark:text-gray-300">{user?.lastName}</div>
            )}
          </div>
          <div className="px-2 sm:px-0">
            <div className="font-bold text-lg mb-1 dark:text-gray-700 dark:text-gray-200">Email address</div>
            <div className="dark:text-gray-600 dark:text-gray-300">{user?.email}</div>
          </div>
          {isGoogleUser && (
            <div className="text-gray-500 dark:text-gray-400 text-xs mt-2 px-2 sm:px-0">
              A edição de nome e e-mail é bloqueada para contas Google.
            </div>
          )}
          <div className="flex gap-2 px-2 sm:px-0">
            {!isGoogleUser && !editing && (
              <button type="button" onClick={() => setEditing(true)} className="border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 font-medium hover:bg-gray-50 dark:hover:bg-gray-100 dark:bg-gray-700 w-full sm:w-auto dark:text-gray-700 dark:text-gray-200 transition-colors">Edit Profile</button>
            )}
            {!isGoogleUser && editing && (
              <>
                <button 
                  type="submit" 
                  className="text-gray-900 dark:text-white rounded-lg px-4 py-2 font-semibold transition w-full sm:w-auto"
                  style={{ 
                    backgroundColor: colorTokens.surface.elevated, 
                    border: `1px solid ${colorTokens.border.default}` 
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = colorTokens.surface.interactive}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = colorTokens.surface.elevated}
                >
                  Save
                </button>
                <button type="button" onClick={handleCancel} className="border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 font-medium hover:bg-gray-50 dark:hover:bg-gray-100 dark:bg-gray-700 w-full sm:w-auto dark:text-gray-700 dark:text-gray-200 transition-colors">Cancel</button>
              </>
            )}
          </div>
        </form>
      </div>
    </CleanPageLayout>
  );
}; 