import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import { useToast } from '@/hooks/useToast';
import { auth, googleProvider } from '@/services/firebase';
import { signInWithEmailAndPassword, signInWithPopup } from 'firebase/auth';
import { Input } from '@/components/ui/design-system/Input';
import { Button } from '@/components/ui/design-system/Button';
import { EnvelopeIcon, LockClosedIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { authTokens, authCardClassesSolid } from '@/theme/authTokens';
import { authIntegrationService } from '@/services/authIntegrationService';
import { Timestamp } from 'firebase/firestore';
import { nameUtils } from '@/utils/nameUtils';

interface LoginFormData {
  email: string;
  password: string;
}

export function Login() {
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const { login, isAuthenticated, authLoaded, permissions, adminUser } = useAuthStore();
  const navigate = useNavigate();
  const { success, error } = useToast();

  // Redirect if already authenticated
  useEffect(() => {
    if (authLoaded && isAuthenticated) {
      console.log('🔄 User already authenticated, redirecting...');
      
      // Check if user has admin access and redirect accordingly
      if (permissions.canAccessAdmin || adminUser?.role === 'admin') {
        console.log('🛡️ Redirecting to admin area');
        navigate('/admin', { replace: true });
      } else {
        console.log('🏠 Redirecting to dashboard');
        navigate('/dashboard', { replace: true });
      }
    }
  }, [authLoaded, isAuthenticated, permissions, adminUser, navigate]);



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await login(formData.email, formData.password);
      success('Login successful!');
      
      // Check if user has admin access and redirect accordingly
      const { permissions, adminUser } = useAuthStore.getState();
      if (permissions.canAccessAdmin || adminUser?.role === 'admin') {
        navigate('/admin');
      } else {
        navigate('/dashboard');
      }
    } catch (loginError: any) {
      console.error('Login error:', loginError);
      
      // Translate Firebase error messages to user-friendly English
      let errorMessage = 'Login failed';
      
      if (loginError.code) {
        switch (loginError.code) {
          case 'auth/invalid-credential':
            errorMessage = 'Invalid email or password';
            break;
          case 'auth/user-not-found':
            errorMessage = 'User not found';
            break;
          case 'auth/wrong-password':
            errorMessage = 'Incorrect password';
            break;
          case 'auth/invalid-email':
            errorMessage = 'Invalid email address';
            break;
          case 'auth/user-disabled':
            errorMessage = 'Account has been disabled';
            break;
          case 'auth/too-many-requests':
            errorMessage = 'Too many attempts. Please try again later';
            break;
          case 'auth/network-request-failed':
            errorMessage = 'Network error. Please check your connection';
            break;
          default:
            errorMessage = loginError.message || 'Unexpected login error';
        }
      } else {
        errorMessage = loginError.message || 'Unexpected login error';
      }
      
      error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsLoading(true);
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const user = result.user;
      
      // Sync user with Firestore and get complete profile
      const adminUser = await authIntegrationService.syncUserWithFirestore(user);
      const permissions = await authIntegrationService.getUserPermissions(user);
      
      // Update auth store with complete user data
      const nameParts = nameUtils.splitName(user.displayName || '');
      useAuthStore.getState().setUser({
        id: user.uid,
        email: user.email || '',
        firstName: nameParts.firstName,
        lastName: nameParts.lastName,
        username: adminUser.username || user.displayName?.toLowerCase().replace(/\s+/g, '-') || '',
        avatarUrl: user.photoURL || '',
        bio: adminUser.bio || '',
        location: adminUser.location || '',
        phoneNumber: adminUser.phoneNumber || '',
        emailVerified: user.emailVerified,
        isActive: adminUser.status === 'active',
        isVerified: adminUser.emailVerified,
        role: adminUser.role,
        status: adminUser.status,
        preferences: adminUser.preferences,
        stats: adminUser.stats || {
          pinsCount: 0,
          boardsCount: 0,
          followersCount: 0,
          followingCount: 0,
          checkInsCount: 0,
          tradesCompletedCount: 0,
          likesReceivedCount: 0
        },
        joinedAt: adminUser.joinedAt || Timestamp.now(),
        createdAt: adminUser.createdAt || Timestamp.now(),
        updatedAt: adminUser.updatedAt || Timestamp.now(),
        lastLoginAt: Timestamp.now(),
      });

      // Update auth store with admin data and permissions
      useAuthStore.setState({
        adminUser,
        permissions,
        isAuthenticated: true,
        authLoaded: true
      });

      success('Google login successful!');
      
      // Check if user has admin access and redirect accordingly
      if (permissions.canAccessAdmin || adminUser?.role === 'admin') {
        navigate('/admin');
      } else {
        navigate('/dashboard');
      }
    } catch (googleError: any) {
      console.error('Google login error:', googleError);
      
      // Translate Google login error messages to user-friendly English
      let errorMessage = 'Google login failed';
      
      if (googleError.code) {
        switch (googleError.code) {
          case 'auth/popup-closed-by-user':
            errorMessage = 'Login cancelled by user';
            break;
          case 'auth/popup-blocked':
            errorMessage = 'Popup blocked. Please allow popups for this site';
            break;
          case 'auth/cancelled-popup-request':
            errorMessage = 'Login cancelled';
            break;
          case 'auth/network-request-failed':
            errorMessage = 'Network error. Please check your connection';
            break;
          case 'auth/account-exists-with-different-credential':
            errorMessage = 'Account already exists with different credentials';
            break;
          default:
            errorMessage = googleError.message || 'Unexpected Google login error';
        }
      } else {
        errorMessage = googleError.message || 'Unexpected Google login error';
      }
      
      error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className={`min-h-screen flex items-center justify-center ${authTokens.background.page}`}>
      <div className="max-w-md w-full space-y-8 p-8">
        <div className={`${authCardClassesSolid} ${authTokens.transition.default}`}>
          <div className="text-center mb-8">
            <h2 className={`text-3xl font-bold ${authTokens.text.primary}`}>
              Welcome Back
            </h2>
            <p className={`mt-2 ${authTokens.text.secondary}`}>
              Sign in to your account
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <Input
              variant="auth"
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              leftIcon={<EnvelopeIcon className="w-5 h-5" />}
              placeholder="Enter your email"
              required
            />

            <Input
              variant="auth"
              label="Password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              leftIcon={<LockClosedIcon className="w-5 h-5" />}
              rightIcon={
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                >
                  {showPassword ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />}
                </button>
              }
              placeholder="Enter your password"
              required
            />

            <Button
              type="submit"
              variant="primary"
              size="lg"
              loading={isLoading}
              className="w-full"
            >
              {isLoading ? 'Signing in...' : 'Sign In'}
            </Button>
          </form>

                      <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className={`w-full border-t ${authTokens.border.subtle}`} />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className={`px-2 ${authTokens.background.card} ${authTokens.text.muted}`}>Or continue with</span>
                </div>
              </div>

            <Button
              onClick={handleGoogleLogin}
              variant="secondary"
              size="lg"
              loading={isLoading}
              className="mt-3 w-full"
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Sign in with Google
            </Button>
          </div>



          <div className="mt-6 text-center">
            <p className={`text-sm ${authTokens.text.secondary}`}>
              Don't have an account?{' '}
              <Link to="/signup" className={`font-medium ${authTokens.text.link}`}>
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 