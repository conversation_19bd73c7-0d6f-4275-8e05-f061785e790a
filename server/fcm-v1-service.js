// FCM REST API v1 Service
// Implementação seguindo: https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages

import { GoogleAuth } from 'google-auth-library';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FCMv1Service {
  constructor() {
    this.projectId = 'iconpal-cf925';
    this.fcmUrl = `https://fcm.googleapis.com/v1/projects/${this.projectId}/messages:send`;
    
    // Configurar autenticação
    this.auth = new GoogleAuth({
      keyFile: path.join(__dirname, '../firebase-service-account.json'),
      scopes: ['https://www.googleapis.com/auth/firebase.messaging']
    });
  }

  /**
   * Obter Access Token para autenticação
   */
  async getAccessToken() {
    try {
      const client = await this.auth.getClient();
      const accessToken = await client.getAccessToken();
      return accessToken.token;
    } catch (error) {
      console.error('❌ Erro ao obter access token:', error);
      throw new Error('Falha na autenticação: ' + error.message);
    }
  }

  /**
   * Enviar notificação usando FCM REST API v1
   * @param {string} token - FCM registration token
   * @param {Object} notification - Dados da notificação
   * @param {Object} data - Dados customizados (opcional)
   * @param {Object} webpush - Configurações específicas para web (opcional)
   */
  async sendNotification(token, notification, data = {}, webpush = {}) {
    try {
      console.log('📱 Enviando notificação via FCM API v1...');
      
      // Obter access token
      const accessToken = await this.getAccessToken();
      
      // Construir payload seguindo a documentação oficial
      const message = {
        message: {
          token: token,
          notification: {
            title: notification.title,
            body: notification.body,
            image: notification.image || undefined
          },
          data: data,
          webpush: {
            headers: {
              TTL: webpush.ttl || '86400', // 24 horas
              Urgency: webpush.urgency || 'normal'
            },
            notification: {
              icon: webpush.icon || '/pinpal-logo-icon.png',
              badge: webpush.badge || '/pinpal-logo-icon.png',
              tag: webpush.tag || 'pinpal-notification',
              requireInteraction: webpush.requireInteraction || true,
              actions: webpush.actions || [
                {
                  action: 'view',
                  title: 'Ver'
                },
                {
                  action: 'dismiss',
                  title: 'Dispensar'
                }
              ],
              data: {
                url: webpush.clickUrl || 'http://localhost:5773',
                ...data
              }
            },
            fcm_options: {
              link: webpush.clickUrl || 'http://localhost:5773'
            }
          }
        }
      };

      // Fazer requisição para FCM API v1
      const response = await fetch(this.fcmUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(message)
      });

      const responseData = await response.json();

      if (response.ok) {
        console.log('✅ Notificação enviada com sucesso!');
        console.log('📋 Message ID:', responseData.name);
        return {
          success: true,
          messageId: responseData.name,
          response: responseData
        };
      } else {
        console.error('❌ Erro ao enviar notificação:', responseData);
        throw new Error(`FCM Error: ${responseData.error?.message || 'Unknown error'}`);
      }

    } catch (error) {
      console.error('❌ Erro no serviço FCM:', error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  }

  /**
   * Enviar notificação para múltiplos tokens
   * @param {string[]} tokens - Array de FCM registration tokens
   * @param {Object} notification - Dados da notificação
   * @param {Object} data - Dados customizados (opcional)
   */
  async sendMulticastNotification(tokens, notification, data = {}) {
    const results = [];
    
    for (const token of tokens) {
      try {
        const result = await this.sendNotification(token, notification, data);
        results.push({ token, ...result });
      } catch (error) {
        results.push({ 
          token, 
          success: false, 
          error: error.message 
        });
      }
    }

    return results;
  }

  /**
   * Validar se um token FCM é válido
   * @param {string} token - FCM registration token
   */
  async validateToken(token) {
    try {
      // Enviar uma mensagem de teste (dry run)
      const accessToken = await this.getAccessToken();
      
      const testMessage = {
        message: {
          token: token,
          notification: {
            title: 'Test',
            body: 'Token validation'
          }
        },
        validate_only: true // Dry run - não envia realmente
      };

      const response = await fetch(this.fcmUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testMessage)
      });

      return response.ok;
    } catch (error) {
      console.error('❌ Erro ao validar token:', error);
      return false;
    }
  }

  /**
   * Criar notificação de teste
   */
  async sendTestNotification(token) {
    return await this.sendNotification(
      token,
      {
        title: '🎉 PinPal Test Notification',
        body: 'Sua configuração FCM está funcionando perfeitamente!',
        image: 'https://via.placeholder.com/512x256/4CAF50/FFFFFF?text=PinPal'
      },
      {
        type: 'test',
        timestamp: new Date().toISOString(),
        source: 'fcm-v1-service'
      },
      {
        clickUrl: 'http://localhost:5773',
        tag: 'pinpal-test',
        requireInteraction: true
      }
    );
  }
}

export default FCMv1Service;
