// FCM REST API v1 Service using Firebase Admin SDK
// Implementação seguindo: https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages

import admin from 'firebase-admin';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FCMv1Service {
  constructor() {
    this.projectId = 'iconpal-cf925';
    this.initializeFirebaseAdmin();
  }

  /**
   * Inicializar Firebase Admin SDK
   */
  initializeFirebaseAdmin() {
    try {
      // Verificar se já foi inicializado
      if (admin.apps.length > 0) {
        console.log('✅ Firebase Admin already initialized');
        return;
      }

      // Caminho para o service account
      const serviceAccountPath = path.join(__dirname, '../firebase-service-account.json');

      // Verificar se o arquivo existe
      if (!fs.existsSync(serviceAccountPath)) {
        console.log('⚠️ Service account file not found, trying environment variables...');

        // Tentar usar variáveis de ambiente
        if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
          const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
          admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
            projectId: this.projectId
          });
          console.log('✅ Firebase Admin initialized with environment variables');
        } else {
          throw new Error('Service account not found. Please configure firebase-service-account.json or FIREBASE_SERVICE_ACCOUNT_KEY environment variable');
        }
      } else {
        // Usar arquivo service account
        const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          projectId: this.projectId
        });
        console.log('✅ Firebase Admin initialized with service account file');
      }

    } catch (error) {
      console.error('❌ Error initializing Firebase Admin:', error);
      throw error;
    }
  }

  /**
   * Obter instância do Firebase Messaging
   */
  getMessaging() {
    return admin.messaging();
  }

  /**
   * Enviar notificação usando Firebase Admin SDK
   * @param {string} token - FCM registration token
   * @param {Object} notification - Dados da notificação
   * @param {Object} data - Dados customizados (opcional)
   * @param {Object} webpush - Configurações específicas para web (opcional)
   */
  async sendNotification(token, notification, data = {}, webpush = {}) {
    try {
      console.log('📱 Enviando notificação via Firebase Admin SDK...');
      console.log('🎯 Target token:', token.substring(0, 20) + '...');

      // Construir mensagem seguindo a documentação oficial
      const message = {
        token: token,
        notification: {
          title: notification.title,
          body: notification.body,
          imageUrl: notification.image || undefined
        },
        data: {
          // Converter todos os valores para string (obrigatório no FCM)
          ...Object.fromEntries(
            Object.entries(data).map(([key, value]) => [key, String(value)])
          )
        },
        webpush: {
          headers: {
            TTL: webpush.ttl || '86400', // 24 horas
            Urgency: webpush.urgency || 'normal'
          },
          notification: {
            title: notification.title,
            body: notification.body,
            icon: webpush.icon || '/pinpal-logo-icon.png',
            badge: webpush.badge || '/pinpal-logo-icon.png',
            tag: webpush.tag || 'pinpal-notification',
            requireInteraction: webpush.requireInteraction !== false,
            actions: webpush.actions || [
              {
                action: 'view',
                title: 'Ver'
              },
              {
                action: 'dismiss',
                title: 'Dispensar'
              }
            ],
            data: {
              url: webpush.clickUrl || 'http://localhost:5773',
              ...data
            }
          },
          fcmOptions: {
            link: webpush.clickUrl || 'http://localhost:5773'
          }
        }
      };

      // Enviar usando Firebase Admin SDK
      const messaging = this.getMessaging();
      const messageId = await messaging.send(message);

      console.log('✅ Notificação enviada com sucesso!');
      console.log('📋 Message ID:', messageId);

      return {
        success: true,
        messageId: messageId,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Erro no serviço FCM:', error);
      return {
        success: false,
        error: error.message,
        code: error.code || 'UNKNOWN_ERROR',
        details: error
      };
    }
  }

  /**
   * Enviar notificação para múltiplos tokens usando sendEachForMulticast
   * @param {string[]} tokens - Array de FCM registration tokens
   * @param {Object} notification - Dados da notificação
   * @param {Object} data - Dados customizados (opcional)
   */
  async sendMulticastNotification(tokens, notification, data = {}) {
    try {
      console.log(`📱 Enviando notificação multicast para ${tokens.length} tokens...`);

      const message = {
        notification: {
          title: notification.title,
          body: notification.body,
          imageUrl: notification.image || undefined
        },
        data: {
          // Converter todos os valores para string
          ...Object.fromEntries(
            Object.entries(data).map(([key, value]) => [key, String(value)])
          )
        },
        tokens: tokens
      };

      const messaging = this.getMessaging();
      const response = await messaging.sendEachForMulticast(message);

      console.log(`📊 Multicast results: ${response.successCount} success, ${response.failureCount} failures`);

      return {
        success: true,
        totalCount: tokens.length,
        successCount: response.successCount,
        failureCount: response.failureCount,
        responses: response.responses.map((resp, index) => ({
          token: tokens[index].substring(0, 20) + '...',
          success: resp.success,
          messageId: resp.messageId || null,
          error: resp.error?.message || null
        }))
      };

    } catch (error) {
      console.error('❌ Erro no multicast:', error);
      return {
        success: false,
        error: error.message,
        code: error.code || 'MULTICAST_ERROR'
      };
    }
  }

  /**
   * Validar se um token FCM é válido usando dry run
   * @param {string} token - FCM registration token
   */
  async validateToken(token) {
    try {
      console.log('🔍 Validando token FCM...');

      const testMessage = {
        token: token,
        notification: {
          title: 'Test',
          body: 'Token validation'
        },
        data: {
          test: 'true'
        }
      };

      const messaging = this.getMessaging();

      // Usar dry run para validar sem enviar
      await messaging.send(testMessage, true); // true = dry run

      console.log('✅ Token é válido');
      return true;

    } catch (error) {
      console.error('❌ Token inválido:', error.message);
      return false;
    }
  }

  /**
   * Criar notificação de teste
   */
  async sendTestNotification(token) {
    return await this.sendNotification(
      token,
      {
        title: '🎉 PinPal Test Notification',
        body: 'Sua configuração FCM está funcionando perfeitamente!',
        image: 'https://via.placeholder.com/512x256/4CAF50/FFFFFF?text=PinPal'
      },
      {
        type: 'test',
        timestamp: new Date().toISOString(),
        source: 'firebase-admin-sdk'
      },
      {
        clickUrl: 'http://localhost:5773',
        tag: 'pinpal-test',
        requireInteraction: true
      }
    );
  }

  /**
   * Verificar status do Firebase Admin
   */
  getStatus() {
    try {
      const hasServiceAccount = admin.apps.length > 0;
      return {
        initialized: hasServiceAccount,
        projectId: this.projectId,
        appsCount: admin.apps.length,
        sdkVersion: admin.SDK_VERSION
      };
    } catch (error) {
      return {
        initialized: false,
        error: error.message
      };
    }
  }
}

export default FCMv1Service;
