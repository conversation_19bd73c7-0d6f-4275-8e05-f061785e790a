/// <reference types="cypress" />
// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Comando para verificar erros CORS
Cypress.Commands.add('checkForCorsErrors', () => {
  cy.window().then((win) => {
    const corsErrors = (win as any).corsErrors || [];
    
    if (corsErrors.length > 0) {
      cy.log('❌ CORS errors detected:');
      corsErrors.forEach((error: string) => {
        cy.log(`  - ${error}`);
      });
      throw new Error(`CORS errors detected: ${corsErrors.length} errors`);
    } else {
      cy.log('✅ No CORS errors detected');
    }
  });
});

// Exemplo de outros comandos úteis que podem ser adicionados:
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... }) 