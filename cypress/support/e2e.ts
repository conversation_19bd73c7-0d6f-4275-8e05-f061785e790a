// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Configurações globais para testes do PinPal
beforeEach(() => {
  // Interceptar erros de console para não falhar os testes
  cy.window().then((win) => {
    const originalConsoleError = win.console.error;
    win.console.error = (...args) => {
      const message = args.join(' ');
      
      // Filtrar erros conhecidos que não devem falhar os testes
      const ignoredErrors = [
        'A listener indicated an asynchronous response',
        'Extension context invalidated',
        'message channel closed',
        'Could not establish connection',
        'Cross-Origin-Opener-Policy policy would block'
      ];
      
      const shouldIgnore = ignoredErrors.some(ignored => 
        message.includes(ignored)
      );
      
      if (!shouldIgnore) {
        originalConsoleError.apply(win.console, args);
      }
    };
  });
  
  // Limpar dados de testes anteriores
  cy.clearLocalStorage();
  cy.clearCookies();
});

// Configurações de timeout globais
Cypress.config('defaultCommandTimeout', 10000);
Cypress.config('pageLoadTimeout', 30000); 