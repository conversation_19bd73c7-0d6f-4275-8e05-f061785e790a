describe('Test Title Fix', () => {
  it('should successfully update pin title', () => {
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);

    // Interceptar requisições de update
    cy.intercept('PUT', '**/pins/**').as('updatePin');

    // Clicar no primeiro pin
    cy.get('img').first().click();
    cy.wait(2000);

    // Abrir menu e editar
    cy.get('button[title*="More"]').click();
    cy.wait(1000);
    cy.contains('button', 'Edit pin').click();
    cy.wait(2000);

    // Editar o nome
    cy.get('label').contains('Pin Name').parent().find('input').then(($input) => {
      const newTitle = `Tí<PERSON>lo Corrigido ${Date.now()}`;
      cy.wrap($input).clear().type(newTitle);
      cy.wrap($input).should('have.value', newTitle);
      
      // Salvar
      cy.contains('button', 'Save').click();
      
      // Verificar se a requisição foi feita com o title
      cy.wait('@updatePin', { timeout: 10000 }).then((interception) => {
        const body = interception.request.body;
        cy.log('📡 Request body:', JSON.stringify(body, null, 2));
        
        // Verificar se o title está presente
        expect(body).to.have.property('title');
        expect(body.title).to.equal(newTitle);
        
        cy.log('✅ Title foi enviado corretamente!');
      });
    });
  });
}); 