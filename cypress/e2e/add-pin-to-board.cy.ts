/// <reference types="cypress" />

describe('Add Pin to Board', () => {
  beforeEach(() => {
    // Ignorar erros de aplicação que não afetam o teste
    cy.on('uncaught:exception', (err, runnable) => {
      // Ignorar erros de módulos dinâmicos e outros erros de desenvolvimento
      if (err.message.includes('Failed to fetch dynamically imported module') ||
          err.message.includes('Loading chunk') ||
          err.message.includes('ChunkLoadError')) {
        return false;
      }
      // Permitir que outros erros falhem o teste
      return true;
    });

    // Interceptar chamadas da API para evitar erros de rede
    cy.intercept('POST', '**/executeQuery**', { fixture: 'empty-response.json' }).as('dataConnectQuery');
    cy.intercept('POST', '**/executeMutation**', { fixture: 'success-response.json' }).as('dataConnectMutation');
    cy.intercept('GET', '**/api/boards/**', { fixture: 'board-response.json' }).as('getBoard');
    
    // Visitar a página inicial
    cy.visit('http://localhost:5773');
    
    // Aguardar o carregamento da aplicação
    cy.wait(5000);
  });

  it('should navigate to board and test add pin functionality', () => {
    // Verificar se estamos na página de login e fazer login se necessário
    cy.url().then((url) => {
      if (url.includes('/login')) {
        cy.log('Fazendo login...');
        
        // Aguardar a página de login carregar
        cy.get('body').should('be.visible');
        
        // Procurar por campos de login
        cy.get('body').then(($body) => {
          if ($body.find('input[type="email"]').length > 0) {
            cy.get('input[type="email"]').type('<EMAIL>');
            cy.get('input[type="password"]').type('123456');
            cy.get('button[type="submit"]').click();
            cy.wait(5000);
          } else {
            cy.log('Campos de login não encontrados, tentando login via Google...');
            // Tentar encontrar botão de login com Google
            cy.get('button').contains(/google|login/i).first().click();
            cy.wait(3000);
          }
        });
      }
    });

    // Navegar diretamente para o board após login
    cy.visit('http://localhost:5773/boards/77c884b8-9dd9-4761-a1f8-c6d638dc4b86');
    cy.wait(5000);

    // Verificar se chegamos na página do board
    cy.url().should('include', '/boards/');
    cy.get('body').should('be.visible');

    // Log do conteúdo da página para debug
    cy.get('body').then(($body) => {
      cy.log('Conteúdo da página do board:', $body.text().substring(0, 300));
    });

    // Procurar pelo botão "Add Pin" com várias estratégias
    cy.get('body').then(($body) => {
      const bodyText = $body.text();
      
      if (bodyText.includes('Add Pin') || bodyText.includes('Add')) {
        cy.log('Botão Add Pin encontrado na página');
        
        // Tentar diferentes seletores para o botão Add Pin
        if ($body.find('button:contains("Add Pin")').length > 0) {
          cy.get('button:contains("Add Pin")').click();
        } else if ($body.find('button').filter(':contains("Add")').length > 0) {
          cy.get('button').contains('Add').click();
        } else if ($body.find('[data-testid="add-pin-button"]').length > 0) {
          cy.get('[data-testid="add-pin-button"]').click();
        } else {
          // Procurar por qualquer botão com ícone de plus
          cy.get('button').first().click();
        }
        
        cy.wait(2000);

        // Verificar se o modal abriu
        cy.get('body').then(($modalBody) => {
          const modalText = $modalBody.text();
          
          if (modalText.includes('Create New Pin') || modalText.includes('Add Pin') || modalText.includes('Pin Name')) {
            cy.log('Modal de criação de pin aberto com sucesso!');
            
            // Preencher o formulário
            cy.get('input').first().clear().type('Pin Teste Cypress');
            cy.wait(500);
            
            // Tentar preencher o segundo campo
            cy.get('input').eq(1).clear().type('Serie Teste Cypress');
            cy.wait(500);

            // Tentar preencher descrição se existir
            cy.get('body').then(($formBody) => {
              if ($formBody.find('textarea').length > 0) {
                cy.get('textarea').first().type('Descrição do pin de teste');
              } else if ($formBody.find('input').length > 2) {
                cy.get('input').eq(2).type('Descrição do pin de teste');
              }
            });

            cy.wait(1000);

            // Tentar submeter o formulário
            cy.get('button').contains(/create|add|save/i).click();
            cy.wait(5000);

            // Verificar resultado
            cy.get('body').then(($resultBody) => {
              const resultText = $resultBody.text();
              
              if (resultText.includes('Pin created') || resultText.includes('success')) {
                cy.log('✅ Pin criado com sucesso!');
              } else if (resultText.includes('error') || resultText.includes('Error')) {
                cy.log('⚠️ Erro na criação do pin (esperado durante desenvolvimento)');
              } else {
                cy.log('📋 Formulário submetido, aguardando resultado...');
              }
            });

          } else {
            cy.log('❌ Modal de criação não abriu ou não foi reconhecido');
            cy.log('Conteúdo atual:', modalText.substring(0, 200));
          }
        });

      } else {
        cy.log('❌ Botão Add Pin não encontrado na página');
        cy.log('Conteúdo da página:', bodyText.substring(0, 500));
        
        // Tentar clicar em qualquer botão disponível para debug
        if ($body.find('button').length > 0) {
          cy.log('Tentando clicar no primeiro botão disponível...');
          cy.get('button').first().click();
          cy.wait(2000);
        }
      }
    });

    // Aguardar para observar o resultado final
    cy.wait(3000);
  });

  it('should test direct board access and pin creation', () => {
    // Tentar acessar diretamente o board
    cy.visit('http://localhost:5773/boards/77c884b8-9dd9-4761-a1f8-c6d638dc4b86');
    cy.wait(5000);

    // Verificar o estado da página
    cy.get('body').should('be.visible');
    
    cy.get('body').then(($body) => {
      const pageText = $body.text();
      cy.log('Estado da página:', pageText.substring(0, 300));
      
      // Se estiver na página de login, tentar fazer login
      if (pageText.includes('Login') || pageText.includes('Sign in')) {
        cy.log('Redirecionado para login, tentando autenticar...');
        
        // Tentar login automático ou pular autenticação para teste
        cy.window().then((win) => {
          // Simular usuário logado no localStorage se possível
          win.localStorage.setItem('user', JSON.stringify({
            id: 'test-user-id',
            email: '<EMAIL>',
            username: 'testuser'
          }));
        });
        
        // Tentar navegar novamente
        cy.visit('http://localhost:5773/boards/77c884b8-9dd9-4761-a1f8-c6d638dc4b86');
        cy.wait(3000);
      }
      
      // Verificar se agora estamos no board
      cy.url().then((currentUrl) => {
        cy.log('URL atual:', currentUrl);
        
        if (currentUrl.includes('/boards/')) {
          cy.log('✅ Sucesso! Estamos na página do board');
          
          // Procurar por elementos da interface do board
          cy.get('body').then(($boardBody) => {
            const boardText = $boardBody.text();
            
            if (boardText.includes('Add Pin') || boardText.includes('No pins yet')) {
              cy.log('✅ Interface do board carregada corretamente');
            } else {
              cy.log('⚠️ Interface do board pode não ter carregado completamente');
            }
          });
          
        } else {
          cy.log('❌ Ainda não conseguimos acessar o board');
        }
      });
    });

    // Aguardar para observação
    cy.wait(5000);
  });
}); 