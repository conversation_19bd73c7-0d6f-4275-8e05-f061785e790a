describe('Seller Onboarding Modal', () => {
  beforeEach(() => {
    // Login como usuário de teste
    cy.visit('/');
    
    // Mock do Firebase Auth
    cy.window().then((win) => {
      // Simular usuário logado
      win.localStorage.setItem('auth-storage', JSON.stringify({
        state: {
          user: {
            id: 'gMcFplcYxxMGzoHcmmZlNngSl0u1',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User'
          },
          isAuthenticated: true
        },
        version: 0
      }));
    });
    
    // Recarregar para aplicar o auth
    cy.reload();
    cy.wait(2000);
  });

  it('should open and stay open when clicking Get Started', () => {
    // Navegar para uma página que tem o AddPinModal (My Pins)
    cy.visit('/my-pins');
    cy.wait(2000);

    // Clicar no botão "Add Pin" para abrir o AddPinModal
    cy.get('[data-testid="add-pin-button"]', { timeout: 10000 })
      .should('be.visible')
      .click();

    // Aguardar o modal aparecer
    cy.get('[data-testid="add-pin-modal"]', { timeout: 5000 })
      .should('be.visible');

    // Verificar se há warning de seller activation
    cy.get('button').contains('Get Started', { timeout: 5000 })
      .should('be.visible')
      .then(($btn) => {
        console.log('Found Get Started button:', $btn);
      });

    // Clicar no botão "Get Started"
    cy.get('button').contains('Get Started')
      .click();

    // Verificar se o SellerOnboardingModal aparece
    cy.get('[role="dialog"]', { timeout: 10000 })
      .should('be.visible')
      .then(($modal) => {
        console.log('Modal found:', $modal);
        // Verificar se contém conteúdo esperado
        cy.wrap($modal).should('contain.text', 'Seller');
      });

    // Aguardar um pouco para ver se o modal permanece aberto
    cy.wait(3000);

    // Verificar se o modal ainda está visível
    cy.get('[role="dialog"]')
      .should('be.visible')
      .then(() => {
        console.log('✅ Modal is still open after 3 seconds');
      });

    // Testar se o modal tem o conteúdo correto
    cy.get('[role="dialog"]')
      .should('contain.text', 'Enable Seller Features')
      .or('contain.text', 'Activate Seller')
      .or('contain.text', 'Get Started');
  });

  it('should show correct step based on seller status', () => {
    // Interceptar a API call para controlar a resposta
    cy.intercept('GET', '**/api/marketplace/sellers/connect/status/**', {
      statusCode: 200,
      body: {
        success: true,
        hasStripeAccount: false,
        accountStatus: 'not_activated',
        payoutsEnabled: false,
        canSell: false,
        canList: false,
        needsBasicActivation: true,
        needsPaymentSetup: false,
        onboardingUrl: null,
        message: 'Seller features not activated. Enable to start listing pins.'
      }
    }).as('getSellerStatus');

    cy.visit('/my-pins');
    cy.wait(2000);

    // Abrir AddPinModal
    cy.get('[data-testid="add-pin-button"]', { timeout: 10000 })
      .should('be.visible')
      .click();

    // Clicar em Get Started
    cy.get('button').contains('Get Started', { timeout: 5000 })
      .click();

    // Aguardar a API call
    cy.wait('@getSellerStatus');

    // Verificar se o modal mostra o step correto
    cy.get('[role="dialog"]', { timeout: 10000 })
      .should('be.visible')
      .and('contain.text', 'Enable Seller Features');
  });

  it('should handle basic seller activation', () => {
    // Mock da API de ativação básica
    cy.intercept('POST', '**/api/marketplace/sellers/activate-basic', {
      statusCode: 200,
      body: {
        success: true,
        message: 'Seller features activated successfully'
      }
    }).as('activateBasicSeller');

    cy.visit('/my-pins');
    cy.wait(2000);

    // Abrir modal e ativar seller
    cy.get('[data-testid="add-pin-button"]', { timeout: 10000 })
      .click();

    cy.get('button').contains('Get Started', { timeout: 5000 })
      .click();

    // Aguardar modal aparecer e clicar em ativar
    cy.get('[role="dialog"]', { timeout: 10000 })
      .should('be.visible');

    // Procurar por botão de ativação (pode ter textos diferentes)
    cy.get('button').contains(/Enable|Activate|Start/, { timeout: 5000 })
      .first()
      .click();

    // Verificar se a API foi chamada
    cy.wait('@activateBasicSeller', { timeout: 10000 });

    // Verificar se houve sucesso
    cy.get('body').should('contain.text', 'success')
      .or('contain.text', 'activated')
      .or('contain.text', 'enabled');
  });
}); 