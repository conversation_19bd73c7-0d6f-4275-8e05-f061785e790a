/// <reference types="cypress" />

describe('Profile Pins - Verification Test', () => {
  beforeEach(() => {
    cy.clearLocalStorage();
    cy.clearCookies();
    
    // Ignorar erros de aplicação para focar no teste funcional
    cy.on('uncaught:exception', (err, runnable) => {
      return false;
    });
  });

  it('should verify pins functionality after fix', () => {
    cy.log('🧪 Verifying pins functionality after fix...');

    // 1. Visitar a aplicação
    cy.visit('http://localhost:5773', { failOnStatusCode: false });
    cy.wait(3000);

    // 2. Verificar se há usuário autenticado
    cy.window().then((win) => {
      const authStore = (win as any).useAuthStore?.getState?.();
      if (authStore && authStore.user) {
        cy.log('✅ User is authenticated:', authStore.user.email);
      } else {
        cy.log('⚠️ No authenticated user found');
      }
    });

    // 3. Navegar para o perfil
    cy.visit('http://localhost:5773/profile', { failOnStatusCode: false });
    cy.wait(5000);

    // 4. Verificar se a página do perfil carregou
    cy.get('body').should('be.visible');
    cy.screenshot('profile-loaded');

    // 5. Procurar pela aba "All Pins" ou similar
    cy.get('body').then(($body) => {
      const bodyText = $body.text();
      
      // Verificar se há tabs de navegação
      const hasPinsTab = bodyText.includes('All Pins') || 
                        bodyText.includes('Pins') ||
                        bodyText.includes('pins');
      
      if (hasPinsTab) {
        cy.log('✅ Pins tab/section found');
        
        // Tentar clicar na aba de pins
        const pinsButton = $body.find('button, a, div').filter((i, el) => {
          const text = (el.textContent || '').toLowerCase();
          return text.includes('all pins') || text === 'pins';
        });

        if (pinsButton.length > 0) {
          cy.log('🔄 Clicking on pins tab...');
          cy.wrap(pinsButton.first()).click({ force: true });
          cy.wait(3000);
        }
      }
    });

    // 6. Verificar o estado da seção de pins após as correções
    cy.get('body').then(($body) => {
      const bodyText = $body.text();
      
      // Verificar se há indicadores de loading
      if (bodyText.includes('Loading pins') || $body.find('.animate-spin').length > 0) {
        cy.log('⏳ Pins are loading - this is expected behavior');
        cy.wait(5000);
        cy.screenshot('pins-loading-state');
      }
      
      // Verificar se há mensagem de erro
      if (bodyText.includes('Error loading pins')) {
        cy.log('❌ Error loading pins detected');
        cy.screenshot('pins-error-state');
        
        // Verificar se há botão de retry
        const retryButton = $body.find('button').filter((i, el) => {
          const text = (el.textContent || '').toLowerCase();
          return text.includes('retry') || text.includes('tentar novamente');
        });
        
        if (retryButton.length > 0) {
          cy.log('🔄 Retry button found, clicking...');
          cy.wrap(retryButton.first()).click({ force: true });
          cy.wait(3000);
          cy.screenshot('after-retry');
        }
      }
      
      // Verificar se há estado vazio
      if (bodyText.includes('No pins yet') || 
          bodyText.includes('Nenhum pin') ||
          bodyText.includes('Start adding pins')) {
        cy.log('📭 Empty pins state detected - this is normal if user has no pins');
        cy.screenshot('pins-empty-state');
        
        // Verificar se há sugestão para criar pins
        const hasCreateSuggestion = bodyText.includes('Start adding') ||
                                   bodyText.includes('Create') ||
                                   bodyText.includes('Add pin');
        
        if (hasCreateSuggestion) {
          cy.log('✅ Empty state with creation suggestion - good UX');
        }
      }
      
      // Verificar se há pins sendo exibidos
      const pinImages = $body.find('img').filter((i, el) => {
        const src = el.getAttribute('src') || '';
        const alt = el.getAttribute('alt') || '';
        return (src && !src.includes('avatar') && !src.includes('logo')) ||
               alt.includes('pin');
      }).length;
      
      if (pinImages > 0) {
        cy.log(`✅ Found ${pinImages} pin images displayed`);
        cy.screenshot('pins-displayed');
      }
      
      // Verificar se há elementos com data-testid relacionados a pins
      const pinTestIds = $body.find('[data-testid*="pin"]').length;
      if (pinTestIds > 0) {
        cy.log(`✅ Found ${pinTestIds} elements with pin test IDs`);
      }
    });

    // 7. Testar a funcionalidade de debug que adicionamos
    cy.window().then((win) => {
      // Verificar se os serviços estão disponíveis
      cy.log('🔍 Checking if pinsService is available...');
      
      win.eval(`
        import('/src/services/pinsService.ts').then(module => {
          console.log('✅ pinsService loaded successfully');
          window.pinsServiceAvailable = true;
        }).catch(error => {
          console.log('❌ Failed to load pinsService:', error);
          window.pinsServiceAvailable = false;
        });
      `);
    });

    // 8. Screenshot final
    cy.screenshot('profile-pins-verification-final');
    cy.log('🎉 Profile pins verification completed');
  });

  it('should test pins service integration', () => {
    cy.log('🧪 Testing pins service integration...');

    cy.visit('http://localhost:5773/profile', { failOnStatusCode: false });
    cy.wait(3000);

    // Testar se o serviço de pins está funcionando
    cy.window().then((win) => {
      cy.log('🔍 Testing pins service...');
      
      // Verificar se o usuário está autenticado
      const authStore = (win as any).useAuthStore?.getState?.();
      
      if (authStore && authStore.user) {
        cy.log('✅ User authenticated, testing getUserPins...');
        
        // Testar a função getUserPins
        win.eval(`
          import('/src/services/pinsService.ts').then(async (module) => {
            try {
              const pins = await module.pinsService.getUserPins('${authStore.user.id}', 10);
              console.log('✅ getUserPins successful:', pins);
              window.pinsTestResult = { success: true, count: pins.length };
            } catch (error) {
              console.log('❌ getUserPins failed:', error);
              window.pinsTestResult = { success: false, error: error.message };
            }
          });
        `);
        
        // Aguardar resultado
        cy.wait(3000);
        
        cy.window().then((win) => {
          const result = (win as any).pinsTestResult;
          if (result) {
            if (result.success) {
              cy.log(`✅ Pins service working - found ${result.count} pins`);
            } else {
              cy.log(`❌ Pins service error: ${result.error}`);
            }
          }
        });
      }
    });

    cy.screenshot('pins-service-test');
  });

  it('should verify GraphQL query is working', () => {
    cy.log('🧪 Testing GraphQL query functionality...');

    cy.visit('http://localhost:5773/profile', { failOnStatusCode: false });
    cy.wait(3000);

    // Interceptar requisições GraphQL
    cy.intercept('POST', '**/graphql', (req) => {
      if (req.body.query && req.body.query.includes('GetUserPins')) {
        cy.log('✅ GetUserPins query intercepted');
        req.alias = 'getUserPinsQuery';
      }
    });

    // Recarregar a página para disparar as queries
    cy.reload();
    cy.wait(5000);

    // Verificar se a query foi executada
    cy.get('@getUserPinsQuery').should('exist');
    cy.log('✅ GetUserPins query was executed');

    cy.screenshot('graphql-query-test');
  });
}); 