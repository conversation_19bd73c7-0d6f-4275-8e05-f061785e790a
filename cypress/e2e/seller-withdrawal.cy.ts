/// <reference types="cypress" />

// Teste e2e – fluxo completo de saque do seller (mock)
// Pré-requisitos: usuário sellerId="seller-test-001" existente
// Banco em modo de desenvolvimento com Stripe MOCK_MODE=true

const sellerId = 'seller-test-001';

context('Seller Withdrawal Flow', () => {
  it('should request a withdrawal and receive payout completed notification', () => {
    // 1. Solicitar saque de $25
    cy.request('POST', `http://localhost:3001/api/marketplace/withdrawals/seller/${sellerId}/request`, {
      amount: 25,
      payment_method: 'stripe'
    }).its('body').then(body => {
      expect(body.success).to.be.true;
      const withdrawalId = body.withdrawal.id;

      // 2. Admin processa – muda status para processing (o endpoint cuidará da transferência & completa)
      cy.request('PUT', `http://localhost:3001/api/marketplace/withdrawals/${withdrawalId}/status`, {
        status: 'processing'
      }).its('body').then(body2 => {
        expect(body2.success).to.be.true;
        expect(body2.withdrawal.status).to.eq('completed');
      });
    });
  });
}); 