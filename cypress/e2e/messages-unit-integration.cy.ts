/// <reference types="cypress" />

// E2E test complementando a cobertura unitária do módulo de mensagens
// Foca no fluxo completo: Usuário A envia → Usuário B recebe → responde

describe('Mensagens - Fluxo Completo E2E', () => {
  const baseUrl = 'http://localhost:5773';
  
  beforeEach(() => {
    // Garantir que porta 5773 está sendo usada
    cy.visit(baseUrl, { 
      failOnStatusCode: false,
      timeout: 10000 
    });
    
    // Aguardar carregamento da aplicação
    cy.get('body', { timeout: 15000 }).should('be.visible');
  });

  it('deve completar fluxo de envio e recebimento de mensagem', () => {
    // Simular login do Usuário A
    cy.window().then((win) => {
      // Mock de autenticação simples para teste
      win.localStorage.setItem('pinpal_user', JSON.stringify({
        id: 'user-a',
        displayName: 'Usu<PERSON>rio A',
        email: '<EMAIL>'
      }));
    });

    // Navegar para mensagens
    cy.get('[data-testid="messages-tab"], [href*="messages"], [aria-label*="message"]')
      .first()
      .click({ force: true });

    // Aguardar carregar lista de conversas
    cy.get('[data-testid="conversation-list"], .conversation-item', { timeout: 10000 })
      .should('be.visible');

    // Criar nova conversa ou selecionar existente
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="new-conversation-btn"]').length > 0) {
        // Botão de nova conversa existe
        cy.get('[data-testid="new-conversation-btn"]').click();
        
        // Preencher dados da nova conversa
        cy.get('[data-testid="user-search-input"], [placeholder*="search"], input[type="text"]')
          .first()
          .type('Usuário B');
          
        cy.get('[data-testid="user-option"], .user-option')
          .first()
          .click();
          
        cy.get('[data-testid="create-conversation-btn"], button[type="submit"]')
          .click();
      } else {
        // Selecionar primeira conversa da lista
        cy.get('.conversation-item, [data-testid="conversation-item"]')
          .first()
          .click();
      }
    });

    // Aguardar carregar a conversa
    cy.get('[data-testid="conversation-view"], .conversation-view', { timeout: 10000 })
      .should('be.visible');

    // Verificar elementos essenciais da conversa
    cy.get('[data-testid="message-input"], textarea[placeholder*="message"], input[placeholder*="message"]')
      .should('be.visible')
      .and('be.enabled');

    cy.get('[data-testid="send-button"], button[type="submit"], [aria-label*="send"]')
      .should('be.visible')
      .and('be.enabled');

    // Enviar mensagem
    const mensagemTeste = `Teste E2E - ${Date.now()}`;
    
    cy.get('[data-testid="message-input"], textarea[placeholder*="message"], input[placeholder*="message"]')
      .first()
      .clear()
      .type(mensagemTeste);

    cy.get('[data-testid="send-button"], button[type="submit"], [aria-label*="send"]')
      .first()
      .click();

    // Verificar se mensagem apareceu na conversa
    cy.contains(mensagemTeste, { timeout: 10000 })
      .should('be.visible');

    // Verificar se input foi limpo após envio
    cy.get('[data-testid="message-input"], textarea[placeholder*="message"], input[placeholder*="message"]')
      .first()
      .should('have.value', '');

    // Testar funcionalidades adicionais se disponíveis
    cy.get('body').then(($body) => {
      // Testar arquivamento se botão existir
      if ($body.find('[data-testid="archive-conversation"], [title*="archive"]').length > 0) {
        cy.get('[data-testid="archive-conversation"], [title*="archive"]')
          .first()
          .click();
          
        // Verificar se conversa foi arquivada (pode aparecer toast ou mudança de estado)
        cy.get('body').should('contain.text', 'arquivada');
      }

      // Testar busca se campo existir
      if ($body.find('[data-testid="search-conversations"], [placeholder*="search"]').length > 0) {
        cy.get('[data-testid="search-conversations"], [placeholder*="search"]')
          .first()
          .type('Usuário');
          
        // Verificar se resultados de busca aparecem
        cy.get('.conversation-item, [data-testid="conversation-item"]', { timeout: 5000 })
          .should('have.length.at.least', 1);
      }
    });
  });

  it('deve lidar com estados de erro graciosamente', () => {
    // Simular erro de rede
    cy.intercept('POST', '**/api/messages/**', { 
      statusCode: 500,
      body: { error: 'Network error' }
    }).as('messageError');

    // Navegar para mensagens
    cy.visit(`${baseUrl}/messages`, { failOnStatusCode: false });

    // Tentar enviar mensagem que falhará
    cy.get('[data-testid="message-input"], textarea[placeholder*="message"]')
      .first()
      .type('Mensagem que falhará');

    cy.get('[data-testid="send-button"], button[type="submit"]')
      .first()
      .click();

    // Verificar se erro é tratado (toast, mensagem de erro, etc.)
    cy.get('body', { timeout: 10000 }).then(($body) => {
      const text = $body.text().toLowerCase();
      const hasError = text.includes('erro') || text.includes('error') || text.includes('falha');
      if (!hasError) {
        throw new Error('Expected error message not found');
      }
    });
  });

  it('deve funcionar corretamente em dispositivos móveis', () => {
    // Simular viewport móvel
    cy.viewport('iphone-x');

    cy.visit(baseUrl, { failOnStatusCode: false });

    // Verificar se interface se adapta ao mobile
    cy.get('[data-testid="mobile-menu"], .mobile-menu, [aria-label*="menu"]')
      .should('be.visible');

    // Navegar para mensagens via menu móvel
    cy.get('[data-testid="mobile-menu"], .mobile-menu, [aria-label*="menu"]')
      .click();

    cy.get('[data-testid="messages-link"], [href*="messages"]')
      .click();

    // Verificar se conversa funciona em mobile
    cy.get('[data-testid="conversation-list"]', { timeout: 10000 })
      .should('be.visible');

    // Testar swipe gestures se implementado
    cy.get('.conversation-item')
      .first()
      .trigger('touchstart', { touches: [{ clientX: 100, clientY: 100 }] })
      .trigger('touchmove', { touches: [{ clientX: 200, clientY: 100 }] })
      .trigger('touchend');
  });

  // Teste de performance
  it('deve carregar mensagens rapidamente', () => {
    cy.visit(`${baseUrl}/messages`, { failOnStatusCode: false });

    // Medir tempo de carregamento
    const startTime = Date.now();
    
    cy.get('[data-testid="conversation-list"], .conversation-item')
      .should('be.visible')
      .then(() => {
        const loadTime = Date.now() - startTime;
        cy.log(`Load time: ${loadTime}ms`);
        // Verificar se carregou em tempo razoável
        if (loadTime > 5000) {
          throw new Error(`Slow loading: ${loadTime}ms`);
        }
      });

    // Verificar se não há memory leaks (básico)
    cy.window().then((win) => {
      // Verificar se não há muitos listeners
      const eventListeners = (win as any)._eventListeners || [];
      if (eventListeners.length > 100) {
        throw new Error(`Too many event listeners: ${eventListeners.length}`);
      }
    });
  });
}); 