describe('Test Auth Fix - Não redirecionar com sessão ativa', () => {
  it('deve manter usuário autenticado e não redirecionar para login', () => {
    // Visitar a página inicial
    cy.visit('http://localhost:5773');
    
    // Aguardar um pouco para carregamento inicial
    cy.wait(2000);
    
    // Verificar se não está na página de login (ou seja, manteve autenticação)
    cy.url().should('not.include', '/login');
    
    // Se estiver logado, deve estar numa página protegida
    cy.url().should('match', /\/(home|explore|my-pins|dashboard|collection)/);
    
    // Verificar se os elementos da interface estão presentes (indicando que está autenticado)
    cy.get('body').should('exist');
    
    // Navegar para uma rota protegida específica
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(2000);
    
    // Verificar que não foi redirecionado para login
    cy.url().should('include', '/my-pins');
    cy.url().should('not.include', '/login');
    
    // Tentar acessar outra rota protegida
    cy.visit('http://localhost:5773/explore');
    cy.wait(2000);
    
    // Verificar que permaneceu autenticado
    cy.url().should('include', '/explore');
    cy.url().should('not.include', '/login');
    
    // Log de sucesso
    cy.window().then((win) => {
      win.console.log('✅ Teste de autenticação passou - usuário permaneceu logado');
    });
  });

  it('deve verificar estado do authStore', () => {
    cy.visit('http://localhost:5773');
    cy.wait(3000);
    
    // Executar script de debug no navegador
    cy.window().then((win) => {
      // Verificar localStorage
      const authStorage = win.localStorage.getItem('auth-storage');
      if (authStorage) {
        cy.log('✅ Auth storage found in localStorage');
        const parsed = JSON.parse(authStorage);
        cy.log(`Auth state: ${parsed.state?.isAuthenticated ? 'Authenticated' : 'Not authenticated'}`);
      } else {
        cy.log('❌ No auth storage in localStorage');
      }
      
      // Verificar se há erro de redirecionamento
      cy.url().then((url) => {
        if (url.includes('/login')) {
          cy.log('❌ Foi redirecionado para login - problema não resolvido');
        } else {
          cy.log('✅ Não foi redirecionado para login - correção funcionou');
        }
      });
    });
  });
}); 