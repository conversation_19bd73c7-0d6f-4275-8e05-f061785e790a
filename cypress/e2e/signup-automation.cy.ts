describe('Signup Automation Test', () => {
  const testUser = {
    firstName: 'Test',
    lastName: 'User',
    email: `test-${Date.now()}@pinpal.com`,
    username: `testuser${Date.now()}`,
    password: 'Test123!@#'
  };

  beforeEach(() => {
    // Limpar localStorage antes de cada teste
    cy.clearLocalStorage();
    cy.clearCookies();
  });

  it('should complete signup process successfully', () => {
    console.log('🧪 Starting automated signup test...');
    console.log('📧 Email:', testUser.email);
    console.log('👤 Username:', testUser.username);

    // 1. Navegar para a página inicial
    cy.visit('/');
    cy.wait(2000); // Aguardar carregamento

    // 2. Navegar para signup
    cy.get('body').then(($body) => {
      if ($body.text().includes('Sign Up') || $body.text().includes('Criar Conta')) {
        // Tentar encontrar e clicar no botão de signup
        cy.contains(/Sign Up|Criar Conta|Cadastrar/i).click();
      } else {
        // Navegar diretamente para a rota de signup
        cy.visit('/signup');
      }
    });

    cy.wait(1000);

    // 3. Verificar se está na página de signup
    cy.get('body').should(($body) => {
      const text = $body.text();
      expect(text.includes('Sign Up') || text.includes('Criar Conta')).to.be.true;
    });

    // 4. Monitor console for CORS errors before submitting
    cy.window().then((win) => {
      const originalConsoleError = win.console.error;
      const corsErrors: string[] = [];
      
      win.console.error = (...args) => {
        const message = args.join(' ');
        if (message.includes('CORS') || 
            message.includes('googleMapsConfig') || 
            message.includes('blocked') ||
            (message.includes('400') && message.includes('identitytoolkit.googleapis.com'))) {
          corsErrors.push(message);
        }
        originalConsoleError.apply(win.console, args);
      };

      // Store for later verification
      (win as any).corsErrors = corsErrors;
    });

    // 5. Preencher campos do formulário
    cy.get('input[name="firstName"], input[placeholder*="nome"], input[placeholder*="first"]')
      .first()
      .clear()
      .type(testUser.firstName);

    cy.get('input[name="lastName"], input[placeholder*="sobrenome"], input[placeholder*="last"]')
      .first()
      .clear()
      .type(testUser.lastName);

    cy.get('input[name="email"], input[type="email"], input[placeholder*="email"]')
      .first()
      .clear()
      .type(testUser.email);

    cy.get('input[name="username"], input[placeholder*="username"], input[placeholder*="usuário"]')
      .first()
      .clear()
      .type(testUser.username);

    cy.get('input[name="password"], input[type="password"], input[placeholder*="senha"]')
      .first()
      .clear()
      .type(testUser.password);

    cy.wait(1000);

    // 6. Submeter formulário
    cy.get('button[type="submit"], button')
      .contains(/Sign Up|Criar Conta|Cadastrar/i)
      .click();

    cy.wait(5000); // Aguardar processamento do signup

    // 7. Verificar se signup foi bem-sucedido
    cy.get('body').then(($body) => {
      const bodyText = $body.text();
      
      // Verificar sinais de sucesso
      const successIndicators = [
        'dashboard',
        'welcome',
        'bem-vindo',
        'profile',
        'perfil',
        'logout',
        'sair'
      ];

      const hasSuccess = successIndicators.some(indicator => 
        bodyText.toLowerCase().includes(indicator)
      );

      if (hasSuccess) {
        cy.log('✅ Signup appears successful - user is logged in');
      } else {
        // Verificar se ainda está na página de signup (pode indicar erro)
        if (bodyText.toLowerCase().includes('sign up') || 
            bodyText.toLowerCase().includes('criar conta')) {
          cy.log('⚠️ Still on signup page - checking for errors');
          
          // Procurar por mensagens de erro
          if ($body.text().includes('error') || 
              $body.text().includes('erro') ||
              $body.text().includes('invalid') ||
              $body.text().includes('inválido')) {
            cy.log('❌ Error message detected on page');
          }
        } else {
          cy.log('✅ User appears to be redirected - signup may be successful');
        }
      }
    });

    // 8. Verificar se houve erros CORS
    cy.window().then((win) => {
      const corsErrors = (win as any).corsErrors || [];
      
      if (corsErrors.length === 0) {
        cy.log('✅ No CORS errors detected during signup process');
      } else {
        cy.log('❌ CORS errors detected:');
        corsErrors.forEach((error: string) => {
          cy.log(`  - ${error}`);
        });
      }

      // Assertion: should have no CORS errors
      expect(corsErrors.length).to.equal(0);
    });

    // 9. Capturar screenshot final
    cy.screenshot('signup-final-state');

    cy.log('🎉 Automated signup test completed!');
  });

  it('should handle signup form validation', () => {
    cy.visit('/signup');
    cy.wait(1000);

    // Tentar submeter formulário vazio
    cy.get('button[type="submit"], button')
      .contains(/Sign Up|Criar Conta|Cadastrar/i)
      .click();

    cy.wait(1000);

    // Verificar se mensagens de validação aparecem
    cy.get('body').should(($body) => {
      const text = $body.text().toLowerCase();
      const hasValidation = text.includes('required') || 
                           text.includes('obrigatório') ||
                           text.includes('field') ||
                           text.includes('campo');
      
      if (hasValidation) {
        cy.log('✅ Form validation is working');
      } else {
        cy.log('⚠️ No validation messages detected');
      }
    });
  });

  it('should not have CORS script conflicts', () => {
    cy.visit('/');
    cy.wait(2000);

    // Verificar se não há conflitos de scripts CORS
    cy.window().then((win) => {
      // Verificar se fetch não foi sobrescrito
      const fetchString = win.fetch.toString();
      const hasCustomOverride = fetchString.includes('corsProblematicUrls') || 
                               fetchString.includes('BLOCKED: Google tile server');
      
      expect(hasCustomOverride).to.be.false;
      cy.log('✅ Native fetch implementation confirmed');
    });

    // Verificar console por erros relacionados a CORS
    cy.window().then((win) => {
      const consoleLogs: string[] = [];
      const originalLog = win.console.log;
      
      win.console.log = (...args) => {
        const message = args.join(' ');
        if (message.includes('CORS') || message.includes('googleMapsConfig')) {
          consoleLogs.push(message);
        }
        originalLog.apply(win.console, args);
      };

      cy.wait(2000);

      cy.then(() => {
        if (consoleLogs.length === 0) {
          cy.log('✅ No CORS-related console messages detected');
        } else {
          cy.log('ℹ️ CORS-related messages found:');
          consoleLogs.forEach(log => cy.log(`  - ${log}`));
        }
      });
    });
  });
}); 