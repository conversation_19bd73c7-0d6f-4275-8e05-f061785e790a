/// <reference types="cypress" />

describe('Board Workflow - Manual Test', () => {
  beforeEach(() => {
    // Ignorar erros de aplicação
    cy.on('uncaught:exception', (err, runnable) => {
      if (err.message.includes('Failed to fetch dynamically imported module') ||
          err.message.includes('Loading chunk') ||
          err.message.includes('ChunkLoadError') ||
          err.message.includes('operation') ||
          err.message.includes('Failed to fetch')) {
        return false;
      }
      return true;
    });
  });

  it('should test board creation workflow (manual)', () => {
    // Visitar diretamente a página de boards (assumindo usuário logado)
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(5000);
    cy.screenshot('01-my-pins-page');

    // Verificar se a página carregou
    cy.get('body').should('be.visible');
    
    // Procurar pelo botão New Board
    cy.get('body').then(($body) => {
      if ($body.text().includes('New Board')) {
        cy.log('✅ Found New Board button - user is logged in');
        
        // TESTE 1: CRIAR BOARD
        cy.contains('New Board').click();
        cy.wait(1000);
        cy.screenshot('02-new-board-modal');
        
        const boardName = `Cypress Test ${Date.now()}`;
        cy.get('input').first().clear().type(boardName);
        cy.get('textarea').first().clear().type('Board criado pelo Cypress');
        
        cy.contains('Create Board').click();
        cy.wait(3000);
        cy.screenshot('03-board-created');
        
        // Verificar se o board apareceu
        cy.contains(boardName, { timeout: 10000 }).should('be.visible');
        cy.log('✅ Board created successfully');
        
        // TESTE 2: EDITAR BOARD
        cy.contains(boardName).parents('div').first().within(() => {
          cy.get('div').trigger('mouseover');
          cy.wait(1000);
          
          // Procurar pelo menu de 3 pontinhos
          cy.get('button').then(($buttons) => {
            const menuButton = $buttons.filter('[title*="option"]');
            if (menuButton.length > 0) {
              cy.wrap(menuButton.first()).click();
              cy.contains('Edit').click();
              
              cy.wait(1000);
              cy.screenshot('04-edit-modal');
              
              const editedName = `${boardName} - Edited`;
              cy.get('input').first().clear().type(editedName);
              cy.contains('Update Board').click();
              cy.wait(3000);
              
              cy.contains(editedName).should('be.visible');
              cy.log('✅ Board edited successfully');
              
              // TESTE 3: ADICIONAR PIN
              cy.contains(editedName).click();
              cy.wait(3000);
              cy.screenshot('05-board-view');
              
              cy.get('body').then(($boardBody) => {
                if ($boardBody.text().includes('Add Pin')) {
                  cy.contains('Add Pin').click();
                  cy.wait(1000);
                  cy.screenshot('06-add-pin-modal');
                  
                  const pinName = `Test Pin ${Date.now()}`;
                  cy.get('input').first().clear().type(pinName);
                  cy.get('textarea').first().clear().type('Pin criado pelo Cypress');
                  
                  cy.contains('Create').click();
                  cy.wait(3000);
                  cy.screenshot('07-pin-created');
                  
                  cy.log('✅ Pin creation attempted');
                }
              });
              
              // TESTE 4: VOLTAR E DELETAR BOARD
              cy.visit('http://localhost:5773/my-pins');
              cy.wait(3000);
              
              cy.contains(editedName).parents('div').first().within(() => {
                cy.get('div').trigger('mouseover');
                cy.wait(1000);
                
                cy.get('button').then(($delButtons) => {
                  const delMenuButton = $delButtons.filter('[title*="option"]');
                  if (delMenuButton.length > 0) {
                    cy.wrap(delMenuButton.first()).click();
                    cy.contains('Delete').click();
                    
                    cy.wait(1000);
                    cy.screenshot('08-delete-confirmation');
                    
                    cy.get('body').then(($confirmBody) => {
                      if ($confirmBody.text().includes('Delete Board')) {
                        cy.contains('Delete Board').click();
                        cy.wait(3000);
                        cy.screenshot('09-board-deleted');
                        cy.log('✅ Board deletion attempted');
                      }
                    });
                  }
                });
              });
            } else {
              cy.log('⚠️ Menu button not found - testing menu visibility');
              cy.screenshot('menu-not-found');
            }
          });
        });
        
      } else {
        cy.log('⚠️ User not logged in or New Board button not found');
        cy.screenshot('not-logged-in');
      }
    });
  });

  it('should test board card hover and menu visibility', () => {
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);
    
    cy.get('body').should('be.visible');
    cy.screenshot('page-loaded');
    
    // Procurar por cards de board existentes
    cy.get('div').then(($divs) => {
      const potentialCards = $divs.filter('[class*="bg-white"], [class*="card"], [class*="rounded"]');
      
      if (potentialCards.length > 0) {
        cy.log(`Found ${potentialCards.length} potential board cards`);
        
        // Testar hover no primeiro card
        cy.wrap(potentialCards.first()).within(() => {
          cy.get('div').first().trigger('mouseover');
          cy.wait(2000);
          cy.screenshot('card-hover');
          
          // Procurar por botões que aparecem no hover
          cy.get('button').then(($buttons) => {
            cy.log(`Found ${$buttons.length} buttons in card`);
            $buttons.each((index, button) => {
              const title = button.getAttribute('title');
              const text = button.textContent;
              cy.log(`Button ${index}: title="${title}", text="${text}"`);
            });
          });
        });
      } else {
        cy.log('No board cards found');
      }
    });
  });

  it('should test component structure and interactions', () => {
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);
    
    // Análise detalhada da estrutura da página
    cy.get('body').should('be.visible');
    
    // Contar elementos por tipo
    cy.get('button').then(($buttons) => {
      cy.log(`Total buttons: ${$buttons.length}`);
    });
    
    cy.get('div').then(($divs) => {
      cy.log(`Total divs: ${$divs.length}`);
    });
    
    cy.get('[class*="grid"]').then(($grids) => {
      cy.log(`Grid elements: ${$grids.length}`);
    });
    
    cy.get('[class*="card"]').then(($cards) => {
      cy.log(`Card elements: ${$cards.length}`);
    });
    
    cy.get('[class*="bg-white"]').then(($whites) => {
      cy.log(`White background elements: ${$whites.length}`);
    });
    
    // Capturar screenshot final
    cy.screenshot('structure-analysis');
    
    // Testar se conseguimos encontrar qualquer elemento interativo
    cy.get('button, a, [role="button"]').then(($interactive) => {
      if ($interactive.length > 0) {
        cy.log(`Found ${$interactive.length} interactive elements`);
        cy.wrap($interactive.first()).should('be.visible');
      }
    });
  });
}); 