describe('Complete Edit Pin Name Test', () => {
  beforeEach(() => {
    cy.visit('http://localhost:5773');
    cy.wait(3000);
  });

  it('should create a pin and then edit its name', () => {
    cy.log('🧪 Teste completo: Criar pin e editar nome');

    // Primeiro, tentar navegar para uma página onde podemos criar pins
    cy.get('body').then(($body) => {
      // Procurar por botões de "Add Pin" ou "Create Pin"
      const addPinButton = $body.find('button, a').filter((i, el) => {
        const text = Cypress.$(el).text().toLowerCase();
        return text.includes('add pin') || text.includes('create pin') || text.includes('new pin');
      }).first();

      if (addPinButton.length > 0) {
        cy.log('✅ Botão Add Pin encontrado');
        cy.wrap(addPinButton).click();
        cy.wait(2000);
        
        // Criar um pin primeiro
        cy.createTestPin().then(() => {
          cy.wait(3000);
          // Agora tentar editar o pin criado
          cy.editPinName();
        });
      } else {
        cy.log('⚠️ Botão Add Pin não encontrado, tentando navegar para My Pins');
        // Tentar navegar para My Pins
        cy.visit('http://localhost:5773/my-pins');
        cy.wait(3000);
        cy.editExistingPin();
      }
    });
  });
});

// Comando para criar um pin de teste
Cypress.Commands.add('createTestPin', () => {
  cy.log('🔄 Criando pin de teste');
  
  cy.get('body').then(($body) => {
    const modal = $body.find('[role="dialog"], .modal, div[class*="modal"]').last();
    
    if (modal.length > 0) {
      cy.log('✅ Modal de criação encontrado');
      
      // Preencher nome do pin
      const nameInput = modal.find('input').filter((i, el) => {
        const label = Cypress.$(el).closest('div').find('label').text().toLowerCase();
        const placeholder = Cypress.$(el).attr('placeholder')?.toLowerCase() || '';
        return label.includes('name') || label.includes('title') || 
               placeholder.includes('name') || placeholder.includes('title');
      }).first();

      if (nameInput.length > 0) {
        const testPinName = `Test Pin ${Date.now()}`;
        cy.wrap(nameInput).clear().type(testPinName);
        cy.log(`📝 Nome do pin: ${testPinName}`);
        
        // Procurar campo de imagem ou URL
        const imageInput = modal.find('input').filter((i, el) => {
          const label = Cypress.$(el).closest('div').find('label').text().toLowerCase();
          const placeholder = Cypress.$(el).attr('placeholder')?.toLowerCase() || '';
          return label.includes('image') || placeholder.includes('image') || 
                 label.includes('url') || placeholder.includes('url');
        }).first();

        if (imageInput.length > 0) {
          cy.wrap(imageInput).clear().type('https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop');
          cy.log('📝 URL da imagem inserida');
        }

        // Procurar botão de salvar/criar
        const saveButton = modal.find('button').filter((i, el) => {
          const text = Cypress.$(el).text().toLowerCase();
          return text.includes('create') || text.includes('save') || text.includes('add');
        }).first();

        if (saveButton.length > 0) {
          cy.wrap(saveButton).click();
          cy.wait(3000);
          cy.log('✅ Pin criado com sucesso');
        }
      }
    }
  });
});

// Comando para editar pin existente
Cypress.Commands.add('editExistingPin', () => {
  cy.log('🔄 Procurando pin existente para editar');
  
  cy.get('body').then(($body) => {
    // Procurar por qualquer elemento que pareça ser um pin
    const pinElements = $body.find('img, div[class*="pin"], div[class*="card"]').filter((i, el) => {
      const $el = Cypress.$(el);
      const alt = $el.attr('alt') || '';
      const src = $el.attr('src') || '';
      const className = $el.attr('class') || '';
      
      return alt.toLowerCase().includes('pin') || 
             src.includes('pin') || 
             className.includes('pin') ||
             className.includes('card');
    });

    if (pinElements.length > 0) {
      cy.log(`✅ Encontrados ${pinElements.length} elementos que podem ser pins`);
      cy.wrap(pinElements.first()).click();
      cy.wait(2000);
      cy.editPinName();
    } else {
      cy.log('❌ Nenhum pin encontrado para editar');
      cy.get('body').screenshot('no-pins-to-edit');
    }
  });
});

// Comando para editar nome do pin
Cypress.Commands.add('editPinName', () => {
  cy.log('🔄 Iniciando edição do nome do pin');
  
  cy.get('body').then(($body) => {
    // Procurar pelo botão de menu (três pontos)
    const moreButtons = $body.find('button').filter((i, el) => {
      const $el = Cypress.$(el);
      const title = $el.attr('title') || '';
      const ariaLabel = $el.attr('aria-label') || '';
      const hasEllipsis = $el.find('svg').length > 0;
      
      return title.toLowerCase().includes('more') || 
             ariaLabel.toLowerCase().includes('more') ||
             hasEllipsis;
    });

    if (moreButtons.length > 0) {
      cy.log('✅ Botão de menu encontrado');
      cy.wrap(moreButtons.first()).click();
      cy.wait(1000);

      // Procurar opção "Edit pin"
      cy.get('body').then(($menuBody) => {
        const editButton = $menuBody.find('button').filter((i, el) => {
          const text = Cypress.$(el).text().toLowerCase();
          return text.includes('edit pin') || text.includes('edit');
        }).first();

        if (editButton.length > 0) {
          cy.log('✅ Botão Edit Pin encontrado');
          cy.wrap(editButton).click();
          cy.wait(2000);

          // Testar a edição do nome
          cy.performNameEdit();
        } else {
          cy.log('❌ Botão Edit Pin não encontrado');
          cy.get('body').screenshot('edit-button-missing');
        }
      });
    } else {
      cy.log('❌ Botão de menu não encontrado');
      cy.get('body').screenshot('menu-button-missing');
    }
  });
});

// Comando para realizar a edição do nome
Cypress.Commands.add('performNameEdit', () => {
  cy.log('🔄 Realizando edição do nome');
  
  cy.get('body').then(($body) => {
    const modal = $body.find('[role="dialog"], .modal, div[class*="modal"]').last();
    
    if (modal.length > 0) {
      cy.log('✅ Modal de edição encontrado');

      // Procurar campo de nome
      const nameInput = modal.find('input').filter((i, el) => {
        const $el = Cypress.$(el);
        const label = $el.closest('div').find('label').text().toLowerCase();
        const placeholder = $el.attr('placeholder')?.toLowerCase() || '';
        
        return label.includes('pin name') || label.includes('name') || 
               placeholder.includes('pin name') || placeholder.includes('name');
      }).first();

      if (nameInput.length > 0) {
        cy.log('✅ Campo de nome encontrado');
        
        const originalValue = nameInput.val();
        const newName = `Pin Editado ${Date.now()}`;
        
        cy.log(`📝 Valor original: ${originalValue}`);
        cy.log(`📝 Novo valor: ${newName}`);

        // Limpar e inserir novo nome
        cy.wrap(nameInput).clear().type(newName);
        cy.wait(1000);

        // Verificar se foi inserido
        cy.wrap(nameInput).should('have.value', newName);

        // Procurar botão Save
        const saveButton = modal.find('button').filter((i, el) => {
          const text = Cypress.$(el).text().toLowerCase();
          return text.includes('save') || text.includes('update');
        }).first();

        if (saveButton.length > 0) {
          cy.log('✅ Botão Save encontrado');
          
          // Interceptar a requisição de update
          cy.intercept('PUT', '**/pins/**').as('updatePin');
          
          cy.wrap(saveButton).click();
          
          // Aguardar a requisição
          cy.wait('@updatePin', { timeout: 10000 }).then((interception) => {
            cy.log('📡 Requisição de update interceptada:');
            cy.log(JSON.stringify(interception.request.body, null, 2));
            
            if (interception.response) {
              cy.log(`📡 Status da resposta: ${interception.response.statusCode}`);
              
              if (interception.response.statusCode === 200) {
                cy.log('✅ Update realizado com sucesso no backend');
              } else {
                cy.log('❌ Erro no update do backend');
              }
            }
          });

          cy.wait(3000);

          // Verificar se o nome foi atualizado na interface
          cy.get('body').then(($updatedBody) => {
            const pageText = $updatedBody.text();
            if (pageText.includes(newName)) {
              cy.log('✅ SUCESSO: Nome atualizado na interface!');
            } else {
              cy.log('❌ FALHA: Nome NÃO foi atualizado na interface');
              cy.log(`🔍 Procurando: ${newName}`);
              cy.get('body').screenshot('name-not-updated');
            }
          });

        } else {
          cy.log('❌ Botão Save não encontrado');
          cy.get('body').screenshot('save-button-missing');
        }
      } else {
        cy.log('❌ Campo de nome não encontrado');
        cy.get('body').screenshot('name-field-missing');
      }
    } else {
      cy.log('❌ Modal de edição não encontrado');
      cy.get('body').screenshot('edit-modal-missing');
    }
  });
});

declare global {
  namespace Cypress {
    interface Chainable {
      createTestPin(): Chainable<void>;
      editExistingPin(): Chainable<void>;
      editPinName(): Chainable<void>;
      performNameEdit(): Chainable<void>;
    }
  }
} 