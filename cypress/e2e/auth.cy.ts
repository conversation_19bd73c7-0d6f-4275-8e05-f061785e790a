describe('Authentication Flow', () => {
  beforeEach(() => {
    cy.visit('/login');
  });

  it('should login successfully with valid credentials', () => {
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('password123');
    cy.get('button[type="submit"]').click();

    // Should redirect to dashboard
    cy.url().should('include', '/');
    cy.get('[data-testid="user-menu"]').should('exist');
  });

  it('should show error with invalid credentials', () => {
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('wrongpassword');
    cy.get('button[type="submit"]').click();

    // Should show error message
    cy.get('[data-testid="error-message"]')
      .should('be.visible')
      .and('contain', 'Invalid credentials');
  });

  it('should logout successfully', () => {
    // Login first
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('password123');
    cy.get('button[type="submit"]').click();

    // Click user menu and logout
    cy.get('[data-testid="user-menu"]').click();
    cy.get('[data-testid="logout-button"]').click();

    // Should redirect to login
    cy.url().should('include', '/login');
  });
}); 