describe('Sistema de Mensagens - Correção de Criação de Conversa', () => {
  const API_BASE = 'http://localhost:3001';
  const USER1_ID = 'gMcFplcYxxMGzoHcmmZlNngSl0u1'; // Usuário real do frontend
  const USER2_ID = 'user-test-cypress'; // Usuário de teste

  it('deve corrigir o erro findOrCreateConversation com dois participantes', () => {
    cy.log('🧪 Testando correção do erro de criação de conversa');
    
    // TESTE 1: Verificar que API funciona com 2 participantes
    cy.request({
      method: 'GET',
      url: `${API_BASE}/api/messages/conversations/find`,
      qs: {
        participants: `${USER1_ID},${USER2_ID}`
      },
      failOnStatusCode: false
    }).then((response) => {
      // Pode retornar 404 (não encontrou) ou 200 (encontrou)
      expect([200, 404]).to.include(response.status);
      
      if (response.status === 404) {
        cy.log('📭 Nenhuma conversa encontrada - vai criar nova');
      } else {
        cy.log('✅ Conversa existente encontrada:', response.body.conversation.id);
      }
    });
    
    // TESTE 2: Criar nova conversa com 2 participantes (comportamento correto)
    cy.request('POST', `${API_BASE}/api/messages/conversations`, {
      participants: [USER1_ID, USER2_ID],
      type: 'DIRECT'
    }).then((response) => {
      expect(response.status).to.eq(201);
      expect(response.body).to.have.property('id');
      expect(response.body.participants).to.have.length(2);
      expect(response.body.participants).to.include(USER1_ID);
      expect(response.body.participants).to.include(USER2_ID);
      
      cy.log(`✅ Nova conversa criada: ${response.body.id}`);
      
      const conversationId = response.body.id;
      
      // TESTE 3: Verificar que conversa pode ser encontrada após criação
      cy.request({
        method: 'GET',
        url: `${API_BASE}/api/messages/conversations/find`,
        qs: {
          participants: `${USER1_ID},${USER2_ID}`
        }
      }).then((findResponse) => {
        expect(findResponse.status).to.eq(200);
        expect(findResponse.body.conversation.id).to.eq(conversationId);
        cy.log('✅ Conversa encontrada após criação');
      });
    });
  });
  
  it('deve rejeitar criação de conversa com apenas 1 participante (problema original)', () => {
    cy.log('🚫 Testando que API rejeita conversa com apenas 1 participante');
    
    // Tentar criar conversa com apenas 1 participante (comportamento INCORRETO do bug)
    cy.request({
      method: 'POST',
      url: `${API_BASE}/api/messages/conversations`,
      body: {
        participants: [USER1_ID], // Apenas 1 participante - deveria falhar
        type: 'DIRECT'
      },
      failOnStatusCode: false
    }).then((response) => {
      // Deveria falhar com erro 400
      expect(response.status).to.eq(400);
      expect(response.body.error).to.include('At least 2 participants are required');
      cy.log('✅ API corretamente rejeitou conversa com 1 participante');
    });
  });
  
  it('deve testar busca com apenas 1 participante (comportamento do bug)', () => {
    cy.log('🔍 Testando busca com apenas 1 participante');
    
    // Tentar buscar conversa com apenas 1 participante
    cy.request({
      method: 'GET',
      url: `${API_BASE}/api/messages/conversations/find`,
      qs: {
        participants: USER1_ID // Apenas 1 participante
      },
      failOnStatusCode: false
    }).then((response) => {
      // Deveria falhar com erro 400
      expect(response.status).to.eq(400);
      expect(response.body.error).to.include('At least 2 participants are required');
      cy.log('✅ API corretamente rejeitou busca com 1 participante');
    });
  });
  
  it('deve executar teste Node.js de validação da correção', () => {
    cy.log('🧪 Executando teste completo de validação');
    
    // Executar o script de teste que criamos
    cy.exec('node scripts/test-conversation-creation-fix.js').then((result) => {
      expect(result.code).to.eq(0);
      expect(result.stdout).to.include('✅ Correção validada com sucesso!');
      expect(result.stdout).to.include('O frontend agora deveria funcionar corretamente!');
      cy.log('✅ Teste Node.js passou com sucesso');
    });
  });
  
  it('deve validar que sistema de mensagens completo está funcionando', () => {
    cy.log('🔧 Validação completa do sistema');
    
    // Verificar status dos servidores
    cy.request('GET', `${API_BASE}/api/messages/conversations/user/${USER1_ID}`)
      .then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body).to.have.property('conversations');
        cy.log(`✅ Backend funcionando - ${response.body.conversations.length} conversas carregadas`);
      });
      
    // Testar envio de mensagem
    cy.request('POST', `${API_BASE}/api/messages/conversations`, {
      participants: [USER1_ID, 'test-final-user'],
      type: 'DIRECT'
    }).then((convResponse) => {
      const conversationId = convResponse.body.id;
      
      return cy.request('POST', `${API_BASE}/api/messages/send`, {
        conversationId,
        senderId: USER1_ID,
        content: 'Teste da correção funcionando!',
        contentType: 'TEXT'
      });
    }).then((msgResponse) => {
      expect(msgResponse.status).to.eq(201);
      expect(msgResponse.body.content).to.eq('Teste da correção funcionando!');
      cy.log('✅ Envio de mensagem funcionando');
    });
  });
}); 