describe('Debug Title Field Specific Issue', () => {
  it('should debug why title field is not saving', () => {
    // Interceptar TODAS as requisições relacionadas a pins
    cy.intercept('GET', '**/pins/**').as('getPin');
    cy.intercept('PUT', '**/pins/**').as('updatePin');
    cy.intercept('POST', '**/pins/**').as('createPin');
    cy.intercept('PATCH', '**/pins/**').as('patchPin');

    cy.visit('http://localhost:5773');
    cy.wait(3000);

    // Capturar TODOS os logs do console
    cy.window().then((win) => {
      const originalLog = win.console.log;
      const originalError = win.console.error;
      const originalWarn = win.console.warn;
      
      win.console.log = cy.stub().callsFake((...args) => {
        originalLog.apply(win.console, args);
        return args;
      }).as('consoleLog');
      
      win.console.error = cy.stub().callsFake((...args) => {
        originalError.apply(win.console, args);
        return args;
      }).as('consoleError');
      
      win.console.warn = cy.stub().callsFake((...args) => {
        originalWarn.apply(win.console, args);
        return args;
      }).as('consoleWarn');
    });

    // Navegar para My Pins
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);

    // Primeiro, vamos criar um pin de teste para garantir que temos algo para editar
    cy.createTestPinForTitleDebug().then(() => {
      cy.wait(3000);
      // Agora testar a edição específica do título
      cy.debugTitleFieldSpecifically();
    });
  });
});

// Comando para criar pin de teste específico
Cypress.Commands.add('createTestPinForTitleDebug', () => {
  cy.log('🔄 Criando pin específico para debug do título');
  
  cy.get('body').then(($body) => {
    // Procurar botão Add Pin
    const addButtons = $body.find('button, a').filter((i, el) => {
      const text = Cypress.$(el).text().toLowerCase();
      return text.includes('add pin') || text.includes('create') || text.includes('new pin');
    });

    if (addButtons.length > 0) {
      cy.log('✅ Botão Add Pin encontrado');
      cy.wrap(addButtons.first()).click();
      cy.wait(2000);

      // Preencher formulário de criação
      cy.get('body').then(($formBody) => {
        const modal = $formBody.find('[role="dialog"], .modal').last();
        
        if (modal.length > 0) {
          cy.log('✅ Modal de criação encontrado');
          
          // Encontrar campo de nome
          const nameInput = modal.find('input').filter((i, el) => {
            const $el = Cypress.$(el);
            const label = $el.closest('div').find('label').text().toLowerCase();
            const placeholder = $el.attr('placeholder')?.toLowerCase() || '';
            return label.includes('name') || label.includes('title') || 
                   placeholder.includes('name') || placeholder.includes('title');
          }).first();

          if (nameInput.length > 0) {
            const testName = `Pin Debug Title ${Date.now()}`;
            cy.wrap(nameInput).clear().type(testName);
            cy.log(`📝 Nome do pin criado: ${testName}`);

            // Encontrar campo de URL da imagem
            const imageInput = modal.find('input').filter((i, el) => {
              const $el = Cypress.$(el);
              const label = $el.closest('div').find('label').text().toLowerCase();
              const placeholder = $el.attr('placeholder')?.toLowerCase() || '';
              return label.includes('image') || label.includes('url') || 
                     placeholder.includes('image') || placeholder.includes('url');
            }).first();

            if (imageInput.length > 0) {
              cy.wrap(imageInput).clear().type('https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop');
            }

            // Salvar
            const saveButton = modal.find('button').filter((i, el) => {
              const text = Cypress.$(el).text().toLowerCase();
              return text.includes('create') || text.includes('save') || text.includes('add');
            }).first();

            if (saveButton.length > 0) {
              cy.wrap(saveButton).click();
              cy.wait(3000);
              cy.log('✅ Pin criado para teste');
            }
          }
        }
      });
    } else {
      cy.log('⚠️ Botão Add Pin não encontrado, assumindo que já existem pins');
    }
  });
});

// Comando para debug específico do campo título
Cypress.Commands.add('debugTitleFieldSpecifically', () => {
  cy.log('🔍 Iniciando debug específico do campo título');

  // Recarregar página para garantir estado limpo
  cy.reload();
  cy.wait(3000);

  cy.get('body').then(($body) => {
    // Encontrar qualquer pin na página
    const pinElements = $body.find('img, div').filter((i, el) => {
      const $el = Cypress.$(el);
      const src = $el.attr('src') || '';
      const className = $el.attr('class') || '';
      return (src.includes('http') && src.includes('image')) || 
             (className.includes('card') && $el.find('img').length > 0);
    });

    cy.log(`🎯 Pins encontrados na página: ${pinElements.length}`);

    if (pinElements.length > 0) {
      // Clicar no primeiro pin
      cy.wrap(pinElements.first()).click();
      cy.wait(2000);

      // Aguardar modal de detalhes abrir
      cy.get('body').then(($modalBody) => {
        const detailModal = $modalBody.find('[role="dialog"], .modal').filter(':visible').last();
        
        if (detailModal.length > 0) {
          cy.log('✅ Modal de detalhes aberto');

          // Procurar botão More Options
          const moreButton = detailModal.find('button').filter((i, el) => {
            const title = Cypress.$(el).attr('title') || '';
            return title.toLowerCase().includes('more');
          }).first();

          if (moreButton.length > 0) {
            cy.log('✅ Botão More Options encontrado');
            cy.wrap(moreButton).click();
            cy.wait(1000);

            // Procurar Edit Pin
            cy.get('body').then(($menuBody) => {
              const editButton = $menuBody.find('button').filter((i, el) => {
                const text = Cypress.$(el).text().toLowerCase();
                return text.includes('edit pin');
              }).first();

              if (editButton.length > 0) {
                cy.log('✅ Botão Edit Pin encontrado');
                cy.wrap(editButton).click();
                cy.wait(2000);

                // AGORA O DEBUG ESPECÍFICO DO TÍTULO
                cy.debugTitleFieldInEditModal();
              }
            });
          }
        }
      });
    }
  });
});

// Comando para debug do campo título no modal de edição
Cypress.Commands.add('debugTitleFieldInEditModal', () => {
  cy.log('🔍 Debug específico do campo título no modal de edição');

  cy.get('body').then(($body) => {
    const editModal = $body.find('[role="dialog"], .modal').filter(':visible').last();
    
    if (editModal.length > 0) {
      cy.log('✅ Modal de edição encontrado');

      // Listar TODOS os inputs
      const allInputs = editModal.find('input');
      cy.log(`📝 Total de inputs no modal: ${allInputs.length}`);

      allInputs.each((i, input) => {
        const $input = Cypress.$(input);
        const label = $input.closest('div').find('label').text();
        const placeholder = $input.attr('placeholder') || '';
        const value = $input.val();
        const name = $input.attr('name') || '';
        const id = $input.attr('id') || '';
        
        cy.log(`Input ${i}:`);
        cy.log(`  - Label: "${label}"`);
        cy.log(`  - Placeholder: "${placeholder}"`);
        cy.log(`  - Value: "${value}"`);
        cy.log(`  - Name: "${name}"`);
        cy.log(`  - ID: "${id}"`);
      });

      // Encontrar especificamente o campo título
      const titleInput = allInputs.filter((i, el) => {
        const $el = Cypress.$(el);
        const label = $el.closest('div').find('label').text().toLowerCase();
        const placeholder = $el.attr('placeholder')?.toLowerCase() || '';
        
        return label.includes('pin name') || label.includes('name') || 
               placeholder.includes('pin name') || placeholder.includes('name');
      }).first();

      if (titleInput.length > 0) {
        cy.log('✅ Campo título encontrado!');
        
        const originalValue = titleInput.val();
        const newValue = `TÍTULO EDITADO ${Date.now()}`;
        
        cy.log(`📝 Valor original: "${originalValue}"`);
        cy.log(`📝 Novo valor: "${newValue}"`);

        // Interceptar requisições ANTES de fazer qualquer mudança
        cy.intercept('PUT', '**/pins/**').as('updatePinRequest');

        // Focar no campo e limpar
        cy.wrap(titleInput).focus().clear();
        cy.wait(500);

        // Digitar novo valor
        cy.wrap(titleInput).type(newValue);
        cy.wait(500);

        // Verificar se o valor foi inserido
        cy.wrap(titleInput).should('have.value', newValue);
        cy.log('✅ Novo valor inserido no campo');

        // Verificar se há eventos onChange sendo disparados
        cy.wrap(titleInput).trigger('change');
        cy.wrap(titleInput).trigger('blur');
        cy.wait(500);

        // Procurar botão Save
        const saveButtons = editModal.find('button').filter((i, el) => {
          const text = Cypress.$(el).text().toLowerCase();
          return text.includes('save') || text.includes('update');
        });

        cy.log(`💾 Botões Save encontrados: ${saveButtons.length}`);

        if (saveButtons.length > 0) {
          cy.log('✅ Clicando no botão Save...');
          cy.wrap(saveButtons.first()).click();

          // Aguardar requisição e analisar
          cy.wait('@updatePinRequest', { timeout: 10000 }).then((interception) => {
            cy.log('📡 REQUISIÇÃO INTERCEPTADA:');
            cy.log(`URL: ${interception.request.url}`);
            cy.log(`Method: ${interception.request.method}`);
            
            const requestBody = interception.request.body;
            cy.log(`Body completo: ${JSON.stringify(requestBody, null, 2)}`);
            
            // Verificar especificamente o campo title
            if (requestBody && requestBody.title !== undefined) {
              cy.log(`✅ Campo 'title' PRESENTE na requisição: "${requestBody.title}"`);
              
              if (requestBody.title === newValue) {
                cy.log('✅ Valor CORRETO sendo enviado!');
              } else {
                cy.log(`❌ Valor INCORRETO! Esperado: "${newValue}", Enviado: "${requestBody.title}"`);
              }
            } else {
              cy.log('❌ Campo "title" AUSENTE na requisição!');
              cy.log(`Campos presentes: ${Object.keys(requestBody || {}).join(', ')}`);
            }

            // Verificar resposta
            if (interception.response) {
              cy.log(`📡 Status da resposta: ${interception.response.statusCode}`);
              
              if (interception.response.statusCode === 200) {
                cy.log('✅ Backend respondeu com sucesso');
              } else {
                cy.log(`❌ Erro no backend: ${interception.response.statusCode}`);
                cy.log(`Resposta: ${JSON.stringify(interception.response.body, null, 2)}`);
              }
            }
          }).catch(() => {
            cy.log('❌ NENHUMA REQUISIÇÃO INTERCEPTADA!');
            cy.log('Isso indica que o formulário não está enviando dados');
          });

          cy.wait(3000);

          // Verificar logs do console para erros
          cy.get('@consoleError').then((stub) => {
            if (stub.callCount > 0) {
              cy.log('❌ ERROS NO CONSOLE:');
              stub.getCalls().forEach((call, i) => {
                cy.log(`Erro ${i}: ${call.args.join(' ')}`);
              });
            } else {
              cy.log('✅ Nenhum erro no console');
            }
          });

          // Verificar logs específicos do nosso código
          cy.get('@consoleLog').then((stub) => {
            if (stub.callCount > 0) {
              cy.log('📋 LOGS RELEVANTES:');
              stub.getCalls().forEach((call, i) => {
                const message = call.args.join(' ');
                if (message.includes('🔄') || message.includes('📝') || 
                    message.includes('✅') || message.includes('❌') ||
                    message.includes('EditPinModal') || message.includes('handleSaveEdit') ||
                    message.includes('title') || message.includes('name')) {
                  cy.log(`${i}: ${message}`);
                }
              });
            }
          });

        } else {
          cy.log('❌ Botão Save não encontrado!');
        }
      } else {
        cy.log('❌ Campo título não encontrado!');
      }
    } else {
      cy.log('❌ Modal de edição não encontrado!');
    }
  });
});

declare global {
  namespace Cypress {
    interface Chainable {
      createTestPinForTitleDebug(): Chainable<void>;
      debugTitleFieldSpecifically(): Chainable<void>;
      debugTitleFieldInEditModal(): Chainable<void>;
    }
  }
} 