describe('Test Specific Pin Edit Scenario', () => {
  it('should test editing a pin with name "nome"', () => {
    // Interceptar requisições para debug
    cy.intercept('PUT', '**/pins/**').as('updatePin');
    cy.intercept('GET', '**/pins/**').as('getPin');
    
    cy.visit('http://localhost:5773');
    cy.wait(3000);

    // Capturar logs do console
    cy.window().then((win) => {
      win.console.log = cy.stub().as('consoleLog');
      win.console.error = cy.stub().as('consoleError');
    });

    // Navegar para uma página com pins
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);

    // Procurar por qualquer pin na página
    cy.get('body').then(($body) => {
      // Procurar por elementos que podem ser pins
      const potentialPins = $body.find('img, div[class*="card"], div[class*="pin"]').filter((i, el) => {
        const $el = Cypress.$(el);
        const src = $el.attr('src') || '';
        const alt = $el.attr('alt') || '';
        const className = $el.attr('class') || '';
        
        return src.includes('http') || alt.length > 0 || className.includes('card');
      });

      if (potentialPins.length > 0) {
        cy.log(`✅ Encontrados ${potentialPins.length} elementos que podem ser pins`);
        
        // Clicar no primeiro pin
        cy.wrap(potentialPins.first()).click();
        cy.wait(2000);

        // Aguardar modal abrir e procurar botão de menu
        cy.get('body').then(($modalBody) => {
          const moreButton = $modalBody.find('button[title*="More"], button[title*="more"]').first();
          
          if (moreButton.length > 0) {
            cy.log('✅ Botão More Options encontrado');
            cy.wrap(moreButton).click();
            cy.wait(1000);

            // Procurar botão Edit Pin
            cy.get('body').then(($menuBody) => {
              const editButton = $menuBody.find('button').filter((i, el) => {
                return Cypress.$(el).text().toLowerCase().includes('edit pin');
              }).first();

              if (editButton.length > 0) {
                cy.log('✅ Botão Edit Pin encontrado');
                cy.wrap(editButton).click();
                cy.wait(2000);

                // Agora testar a edição específica
                cy.testSpecificEdit();
              }
            });
          }
        });
      } else {
        // Se não encontrar pins, criar um pin de teste primeiro
        cy.log('⚠️ Nenhum pin encontrado, tentando criar um pin de teste');
        cy.createTestPinForEdit();
      }
    });
  });
});

// Comando para criar um pin de teste
Cypress.Commands.add('createTestPinForEdit', () => {
  cy.log('🔄 Criando pin de teste para edição');
  
  // Procurar botão Add Pin
  cy.get('body').then(($body) => {
    const addButton = $body.find('button, a').filter((i, el) => {
      const text = Cypress.$(el).text().toLowerCase();
      return text.includes('add pin') || text.includes('create');
    }).first();

    if (addButton.length > 0) {
      cy.wrap(addButton).click();
      cy.wait(2000);

      // Preencher formulário de criação
      cy.get('body').then(($formBody) => {
        const modal = $formBody.find('[role="dialog"], .modal').last();
        
        if (modal.length > 0) {
          // Preencher nome
          const nameInput = modal.find('input').first();
          if (nameInput.length > 0) {
            cy.wrap(nameInput).clear().type('nome');
          }

          // Preencher URL da imagem
          const imageInput = modal.find('input').eq(1);
          if (imageInput.length > 0) {
            cy.wrap(imageInput).clear().type('https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop');
          }

          // Salvar
          const saveButton = modal.find('button').filter((i, el) => {
            const text = Cypress.$(el).text().toLowerCase();
            return text.includes('create') || text.includes('save');
          }).first();

          if (saveButton.length > 0) {
            cy.wrap(saveButton).click();
            cy.wait(3000);
            
            // Agora tentar editar o pin criado
            cy.reload();
            cy.wait(3000);
            cy.testSpecificEdit();
          }
        }
      });
    }
  });
});

// Comando para testar edição específica
Cypress.Commands.add('testSpecificEdit', () => {
  cy.log('🔄 Testando edição específica do pin');

  cy.get('body').then(($body) => {
    const editModal = $body.find('[role="dialog"], .modal').last();
    
    if (editModal.length > 0) {
      cy.log('✅ Modal de edição encontrado');

      // Procurar campo Pin Name
      const nameInput = editModal.find('input').filter((i, el) => {
        const $el = Cypress.$(el);
        const label = $el.closest('div').find('label').text().toLowerCase();
        return label.includes('pin name') || label.includes('name');
      }).first();

      if (nameInput.length > 0) {
        cy.log('✅ Campo Pin Name encontrado');
        
        // Verificar valor atual
        cy.wrap(nameInput).then(($input) => {
          const currentValue = $input.val();
          cy.log(`📝 Valor atual do campo: "${currentValue}"`);
          
          // Se o valor for "nome", testar a edição
          if (currentValue === 'nome' || currentValue === '') {
            const newName = `Pin Editado ${Date.now()}`;
            cy.log(`📝 Alterando para: "${newName}"`);

            // Limpar e digitar novo nome
            cy.wrap(nameInput).clear().type(newName);
            cy.wait(1000);

            // Verificar se foi inserido
            cy.wrap(nameInput).should('have.value', newName);
            cy.log('✅ Novo nome inserido com sucesso');

            // Procurar botão Save
            const saveButton = editModal.find('button').filter((i, el) => {
              const text = Cypress.$(el).text().toLowerCase();
              return text.includes('save') || text.includes('update');
            }).first();

            if (saveButton.length > 0) {
              cy.log('✅ Botão Save encontrado');
              
              // Interceptar requisição
              cy.intercept('PUT', '**/pins/**').as('updatePin');
              
              cy.wrap(saveButton).click();
              
              // Aguardar requisição e verificar
              cy.wait('@updatePin', { timeout: 10000 }).then((interception) => {
                if (interception) {
                  cy.log('📡 Requisição de update capturada:');
                  cy.log(`URL: ${interception.request.url}`);
                  cy.log(`Body: ${JSON.stringify(interception.request.body, null, 2)}`);
                  
                  // Verificar se o campo title está sendo enviado
                  const body = interception.request.body;
                  if (body && body.title) {
                    cy.log(`✅ Campo 'title' encontrado na requisição: "${body.title}"`);
                  } else {
                    cy.log('❌ Campo "title" NÃO encontrado na requisição');
                    cy.log('📋 Campos enviados:', Object.keys(body || {}));
                  }

                  if (interception.response) {
                    cy.log(`📡 Status da resposta: ${interception.response.statusCode}`);
                    if (interception.response.statusCode === 200) {
                      cy.log('✅ Update realizado com sucesso no backend');
                    } else {
                      cy.log('❌ Erro no backend');
                    }
                  }
                }
              }).catch(() => {
                cy.log('⚠️ Nenhuma requisição interceptada');
              });

              cy.wait(3000);

              // Verificar logs do console
              cy.get('@consoleLog').then((stub) => {
                if (stub.callCount > 0) {
                  cy.log('📋 Logs do console:');
                  stub.getCalls().slice(-10).forEach((call, i) => {
                    cy.log(`Log ${i}: ${call.args.join(' ')}`);
                  });
                }
              });

              // Verificar se o nome foi atualizado na interface
              cy.wait(2000);
              cy.get('body').then(($updatedBody) => {
                const pageText = $updatedBody.text();
                if (pageText.includes(newName)) {
                  cy.log('✅ SUCESSO: Nome atualizado na interface!');
                } else {
                  cy.log('❌ FALHA: Nome NÃO foi atualizado na interface');
                  cy.log(`🔍 Procurando: "${newName}"`);
                  cy.log(`📄 Texto da página (primeiros 500 chars): ${pageText.substring(0, 500)}`);
                }
              });

            } else {
              cy.log('❌ Botão Save não encontrado');
            }
          } else {
            cy.log(`⚠️ Valor atual não é "nome": "${currentValue}"`);
          }
        });
      } else {
        cy.log('❌ Campo Pin Name não encontrado');
      }
    } else {
      cy.log('❌ Modal de edição não encontrado');
    }
  });
});

declare global {
  namespace Cypress {
    interface Chainable {
      createTestPinForEdit(): Chainable<void>;
      testSpecificEdit(): Chainable<void>;
    }
  }
} 