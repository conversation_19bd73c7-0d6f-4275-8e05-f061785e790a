describe('Test Name Mapping Fix', () => {
  it('should successfully update pin name with correct mapping', () => {
    cy.visit('http://localhost:5773/my-pins');
    cy.wait(3000);

    // Interceptar requisições de update
    cy.intercept('PUT', '**/pins/**').as('updatePin');

    // Capturar logs do console
    cy.window().then((win) => {
      win.console.log = cy.stub().as('consoleLog');
      win.console.error = cy.stub().as('consoleError');
    });

    // Clicar no primeiro pin
    cy.get('img').first().click();
    cy.wait(2000);

    // Abrir menu e editar
    cy.get('button[title*="More"]').click();
    cy.wait(1000);
    cy.contains('button', 'Edit pin').click();
    cy.wait(2000);

    // Editar o nome
    cy.get('label').contains('Pin Name').parent().find('input').then(($input) => {
      const newTitle = `Nome Corrigido ${Date.now()}`;
      cy.wrap($input).clear().type(newTitle);
      cy.wrap($input).should('have.value', newTitle);
      
      // Salvar
      cy.contains('button', 'Save').click();
      cy.wait(3000);
      
      // Verificar logs para confirmar o mapeamento
      cy.get('@consoleLog').should('have.been.calledWith', '✅ Mapeando name → title:', newTitle);
      cy.get('@consoleLog').should('have.been.calledWith', '✅ Incluindo title:', newTitle);
      
      // Verificar se não houve erros
      cy.get('@consoleError').should('not.have.been.called');
      
      // Verificar se o modal fechou (indicando sucesso)
      cy.get('label').contains('Pin Name').should('not.exist');
    });
  });
}); 