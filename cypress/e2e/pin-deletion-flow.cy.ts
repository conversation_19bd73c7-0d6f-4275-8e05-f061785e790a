/// <reference types="cypress" />

describe('Pin Deletion Flow - Complete Test', () => {
  beforeEach(() => {
    // Ignorar erros de aplicação que não afetam o teste
    cy.on('uncaught:exception', (err, runnable) => {
      if (err.message.includes('Failed to fetch dynamically imported module') ||
          err.message.includes('Loading chunk') ||
          err.message.includes('ChunkLoadError') ||
          err.message.includes('operation "GetBoardPins" not found') ||
          err.message.includes('Failed to fetch board pins')) {
        return false;
      }
      return true;
    });

    // Limpar localStorage antes de cada teste
    cy.clearLocalStorage();
    
    // Visitar a página de teste
    cy.visit('http://localhost:5773/test-pin-deletion.html');
    cy.wait(2000);
  });

  it('should complete the full pin deletion flow: board → trash → permanent deletion', () => {
    cy.log('🧪 Iniciando teste completo do fluxo de exclusão de pin');

    // 1. Executar teste completo automatizado
    cy.get('button').contains('Executar Teste Completo').click();
    cy.wait(1000);

    // 2. Verificar se o teste passou
    cy.get('#test-results').should('contain', 'TESTE PASSOU!');
    cy.get('#test-results').should('contain', 'Lixeira vazia: ✅');
    cy.get('#test-results').should('contain', 'Pin removido do board: ✅');

    // 3. Verificar logs no console
    cy.get('#console-output').should('contain', 'TESTE PASSOU');
    cy.get('#console-output').should('contain', 'Pin de Teste" movido para lixeira');
    cy.get('#console-output').should('contain', 'excluído(s) permanentemente');

    cy.log('✅ Teste completo do fluxo de exclusão passou!');
  });

  it('should test manual step-by-step deletion flow', () => {
    cy.log('🔧 Testando fluxo manual passo a passo');

    // 1. Configurar board de teste
    cy.get('button').contains('Configurar Board de Teste').click();
    cy.wait(500);

    // Verificar estado inicial
    cy.get('#current-state').should('contain', 'Board: 2 pin(s)');
    cy.get('#current-state').should('contain', 'Lixeira: 0 pin(s)');
    cy.get('#current-state').should('contain', 'Pin de teste no board: ✅ Sim');

    // 2. Excluir pin (mover para lixeira)
    cy.get('button').contains('Excluir Pin (→ Lixeira)').click();
    cy.wait(500);

    // Verificar que pin foi movido para lixeira
    cy.get('#current-state').should('contain', 'Board: 1 pin(s)');
    cy.get('#current-state').should('contain', 'Lixeira: 1 pin(s)');
    cy.get('#current-state').should('contain', 'Pin de teste no board: ❌ Não');

    // 3. Verificar lixeira
    cy.get('button').contains('Verificar Lixeira').click();
    cy.wait(500);

    cy.get('#console-output').should('contain', 'Lixeira contém 1 pin(s)');
    cy.get('#console-output').should('contain', 'Pin de Teste');

    // 4. Esvaziar lixeira
    cy.get('button').contains('Esvaziar Lixeira').click();
    cy.wait(500);

    // Verificar que lixeira foi esvaziada
    cy.get('#current-state').should('contain', 'Lixeira: 0 pin(s)');
    cy.get('#console-output').should('contain', 'excluído(s) permanentemente');

    // 5. Verificar estado final
    cy.get('button').contains('Verificar Estado Final').click();
    cy.wait(500);

    cy.get('#test-results').should('contain', 'TESTE PASSOU!');
    cy.get('#test-results').should('contain', 'Lixeira vazia: ✅');
    cy.get('#test-results').should('contain', 'Pin removido do board: ✅');

    cy.log('✅ Teste manual passo a passo passou!');
  });

  it('should verify localStorage operations work correctly', () => {
    cy.log('💾 Testando operações do localStorage');

    // Configurar board
    cy.get('button').contains('Configurar Board de Teste').click();
    cy.wait(500);

    // Verificar localStorage do board
    cy.window().then((win) => {
      const boardData = win.localStorage.getItem('board-board-test-123-pin-order');
      expect(boardData).to.not.be.null;
      if (boardData) {
        const pins = JSON.parse(boardData);
        expect(pins).to.have.length(2);
        expect(pins).to.include('pin-test-456');
      }
    });

    // Excluir pin
    cy.get('button').contains('Excluir Pin (→ Lixeira)').click();
    cy.wait(500);

    // Verificar localStorage da lixeira
    cy.window().then((win) => {
      const trashData = win.localStorage.getItem('deletedPins-board-test-123');
      expect(trashData).to.not.be.null;
      if (trashData) {
        const trashPins = JSON.parse(trashData);
        expect(trashPins).to.have.length(1);
        expect(trashPins[0].id).to.equal('pin-test-456');
        expect(trashPins[0].boardId).to.equal('board-test-123');
      }
    });

    // Verificar que pin foi removido do board
    cy.window().then((win) => {
      const boardData = win.localStorage.getItem('board-board-test-123-pin-order');
      if (boardData) {
        const pins = JSON.parse(boardData);
        expect(pins).to.have.length(1);
        expect(pins).to.not.include('pin-test-456');
      }
    });

    // Esvaziar lixeira
    cy.get('button').contains('Esvaziar Lixeira').click();
    cy.wait(500);

    // Verificar que lixeira foi limpa
    cy.window().then((win) => {
      const trashData = win.localStorage.getItem('deletedPins-board-test-123');
      expect(trashData).to.be.null;
    });

    cy.log('✅ Operações do localStorage funcionando corretamente!');
  });

  it('should handle edge cases correctly', () => {
    cy.log('🔍 Testando casos extremos');

    // Tentar esvaziar lixeira vazia
    cy.get('button').contains('Esvaziar Lixeira').click();
    cy.wait(500);
    cy.get('#console-output').should('contain', 'Lixeira já estava vazia');

    // Verificar lixeira vazia
    cy.get('button').contains('Verificar Lixeira').click();
    cy.wait(500);
    cy.get('#console-output').should('contain', 'Lixeira está vazia');

    // Configurar board e excluir pin
    cy.get('button').contains('Configurar Board de Teste').click();
    cy.wait(500);
    cy.get('button').contains('Excluir Pin (→ Lixeira)').click();
    cy.wait(500);

    // Tentar excluir o mesmo pin novamente (deve falhar graciosamente)
    cy.get('button').contains('Excluir Pin (→ Lixeira)').click();
    cy.wait(500);

    // Verificar que ainda há apenas 1 pin na lixeira
    cy.get('#current-state').should('contain', 'Lixeira: 1 pin(s)');

    cy.log('✅ Casos extremos tratados corretamente!');
  });

  afterEach(() => {
    // Limpar dados de teste após cada teste
    cy.get('button').contains('Limpar Dados de Teste').click();
    cy.wait(500);
  });
}); 