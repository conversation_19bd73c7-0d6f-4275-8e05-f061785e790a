{"main": {"id": "5059d414510b4c11", "type": "split", "children": [{"id": "f3bce0335ccf5091", "type": "tabs", "children": [{"id": "565251c3692ea2a4", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Planos de Implementação/Untitled 2.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Untitled 2"}}]}], "direction": "vertical"}, "left": {"id": "8bc455904ddb1694", "type": "split", "children": [{"id": "9634669ab1047523", "type": "tabs", "children": [{"id": "5e411c70c9a18d20", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "57b542e1410ff4c6", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "07615bc6a3d0038b", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "3e109cf680519a4b", "type": "split", "children": [{"id": "548f21b2d95ee3d6", "type": "tabs", "children": [{"id": "5be5d34a576a75c5", "type": "leaf", "state": {"type": "backlink", "state": {"file": "Fluxo Completo do Sistema de Reports - Da Denúncia à Resolução.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for Fluxo Completo do Sistema de Reports - Da Denúncia à Resolução"}}, {"id": "23f0202a6eb9c240", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "Fluxo Completo do Sistema de Reports - Da Denúncia à Resolução.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from Fluxo Completo do Sistema de Reports - Da Denúncia à Resolução"}}, {"id": "44a281711863868e", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "1cfd8dd51a84f325", "type": "leaf", "state": {"type": "outline", "state": {"file": "Fluxo Completo do Sistema de Reports - Da Denúncia à Resolução.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of Fluxo Completo do Sistema de Reports - Da Denúncia à Resolução"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "565251c3692ea2a4", "lastOpenFiles": ["Planos de Implementação/Untitled 1.md", "Planos de Implementação/Untitled 2.md", "Planos de Implementação/Untitled.md", "Planos de Implementação/módulos são essenciais para documentar no Storybook.md", "Planos de Implementação/**PLANO DE IMPLEMENTAÇÃO MVP SELLER**.md", "outros/Untitled 2.md", "outros/Untitled 1.md", "outros/Untitled.md", "outros/Home vs Explore.md", "Untitled 2.md", "outros", "getting-started/index.md", "getting-started/environment.md", "getting-started/docs-commands.md", "getting-started/Untitled.md", "prioridades de produto.md", "estratégia app.md", "oportunidades de otimização.md", "endpoints do sistema PinPal.md", "Sistema Trading Map - Implementação Completa.md", "Untitled 1.md", "Dashboard de Métricas.md", "funcionalidade de Block.md", "Untitled.md", "getting-started/installation.md", "Fluxo Completo do Sistema de Reports - Da Denúncia à Resolução.md", "deployment/comandos-uteis.md", "tests", "Planos de Implementação", "AORGANIZADO"]}