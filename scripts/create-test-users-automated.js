#!/usr/bin/env node

import { createUserWithEmailAndPassword, getAuth } from 'firebase/auth';
import { doc, setDoc, getFirestore } from 'firebase/firestore';
import { initializeApp } from 'firebase/app';
import { Pool } from 'pg';
import crypto from 'crypto';

// Configuração do Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDr2dNLyGxZPJRp3HfKNxqMbFqjlOjGwxA",
  authDomain: "pinpal-project.firebaseapp.com",
  projectId: "pinpal-project",
  storageBucket: "pinpal-project.appspot.com",
  messagingSenderId: "1081932630476",
  appId: "1:1081932630476:web:7e5e3e5c8c9b8b8b8b8b8b",
  measurementId: "G-XXXXXXXXXX"
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Configuração do PostgreSQL
const pool = new Pool({
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5433'),
  database: process.env.POSTGRES_DATABASE || 'postgres',
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'pinpal123',
  ssl: false,
});

// Lista de usuários para criar
const testUsers = [
  {
    username: 'newuser2024',
    firstName: 'Alex',
    lastName: 'Silva',
    email: '<EMAIL>',
    password: 'TestPass123!',
    bio: 'Disney pin collector and trader from Brazil'
  },
  {
    username: 'disney_lover',
    firstName: 'Emma',
    lastName: 'Johnson',
    email: '<EMAIL>',
    password: 'TestPass123!',
    bio: 'Passionate about Disney pins, especially classic characters'
  },
  {
    username: 'pin_collector',
    firstName: 'Marcus',
    lastName: 'Chen',
    email: '<EMAIL>',
    password: 'TestPass123!',
    bio: 'Collector of rare Disney and Marvel pins'
  },
  {
    username: 'trading_pro',
    firstName: 'Sofia',
    lastName: 'Rodriguez',
    email: '<EMAIL>',
    password: 'TestPass123!',
    bio: 'Professional pin trader with 5+ years experience'
  },
  {
    username: 'collector_pro',
    firstName: 'James',
    lastName: 'Wilson',
    email: '<EMAIL>',
    password: 'TestPass123!',
    bio: 'Serious collector focusing on limited edition pins'
  }
];

async function generateUserId() {
  return crypto.randomUUID();
}

async function createUserInPostgreSQL(user, firebaseUid) {
  const userId = await generateUserId();
  
  try {
    const query = `
      INSERT INTO "user" (
        id, firebase_uid, username, first_name, last_name, email, bio,
        created_at, updated_at, is_active, email_verified
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW(), true, true)
      RETURNING *
    `;
    
    const values = [
      userId,
      firebaseUid,
      user.username,
      user.firstName,
      user.lastName,
      user.email,
      user.bio
    ];
    
    const result = await pool.query(query, values);
    console.log(`✅ PostgreSQL: User ${user.username} created with ID: ${userId}`);
    return result.rows[0];
  } catch (error) {
    console.error(`❌ PostgreSQL error for ${user.username}:`, error.message);
    throw error;
  }
}

async function createUserInFirestore(user, firebaseUid, postgresUser) {
  try {
    const userDoc = {
      id: firebaseUid,
      username: user.username,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      bio: user.bio,
      postgresId: postgresUser.id,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      emailVerified: true,
      profileComplete: true
    };
    
    await setDoc(doc(db, 'users', firebaseUid), userDoc);
    console.log(`✅ Firestore: User ${user.username} document created`);
    return userDoc;
  } catch (error) {
    console.error(`❌ Firestore error for ${user.username}:`, error.message);
    throw error;
  }
}

async function createCompleteUser(user) {
  console.log(`\n🔨 Creating user: ${user.username} (${user.email})`);
  console.log('─'.repeat(60));
  
  let firebaseUser = null;
  let postgresUser = null;
  let firestoreUser = null;
  
  try {
    // 1. Criar no Firebase Auth
    console.log('🔐 Step 1: Creating Firebase Auth user...');
    firebaseUser = await createUserWithEmailAndPassword(auth, user.email, user.password);
    console.log(`✅ Firebase Auth: User created with UID: ${firebaseUser.user.uid}`);
    
    // 2. Criar no PostgreSQL
    console.log('🐘 Step 2: Creating PostgreSQL user...');
    postgresUser = await createUserInPostgreSQL(user, firebaseUser.user.uid);
    
    // 3. Criar no Firestore
    console.log('🔥 Step 3: Creating Firestore document...');
    firestoreUser = await createUserInFirestore(user, firebaseUser.user.uid, postgresUser);
    
    console.log(`🎉 SUCCESS: User ${user.username} created in all systems!`);
    
    return {
      success: true,
      user: {
        username: user.username,
        email: user.email,
        firebaseUid: firebaseUser.user.uid,
        postgresId: postgresUser.id,
        password: user.password // Para referência de teste
      }
    };
    
  } catch (error) {
    console.error(`❌ FAILED: Error creating user ${user.username}:`, error.message);
    
    // Cleanup em caso de erro
    if (firebaseUser && !postgresUser) {
      console.log('🧹 Cleaning up Firebase Auth user...');
      try {
        await firebaseUser.user.delete();
      } catch (cleanupError) {
        console.error('❌ Cleanup failed:', cleanupError.message);
      }
    }
    
    return {
      success: false,
      user: user.username,
      error: error.message
    };
  }
}

async function createAllUsers() {
  console.log('🚀 AUTOMATED USER CREATION SCRIPT');
  console.log('=' .repeat(80));
  console.log(`📝 Creating ${testUsers.length} test users for PinPal...\n`);
  
  // Testar conexões
  try {
    await pool.query('SELECT 1');
    console.log('✅ PostgreSQL connection: OK');
  } catch (error) {
    console.error('❌ PostgreSQL connection failed:', error.message);
    return;
  }
  
  const results = [];
  const successful = [];
  const failed = [];
  
  // Criar usuários sequencialmente para evitar conflitos
  for (const user of testUsers) {
    const result = await createCompleteUser(user);
    results.push(result);
    
    if (result.success) {
      successful.push(result.user);
    } else {
      failed.push({ username: result.user, error: result.error });
    }
    
    // Pequena pausa entre criações
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Relatório final
  console.log('\n' + '=' .repeat(80));
  console.log('📊 FINAL REPORT');
  console.log('=' .repeat(80));
  
  console.log(`\n✅ SUCCESSFUL (${successful.length}):`);
  successful.forEach(user => {
    console.log(`   👤 ${user.username} (${user.email})`);
    console.log(`      🔑 Password: ${user.password}`);
    console.log(`      🆔 Firebase UID: ${user.firebaseUid}`);
    console.log(`      🗄️ Postgres ID: ${user.postgresId}`);
    console.log('');
  });
  
  if (failed.length > 0) {
    console.log(`\n❌ FAILED (${failed.length}):`);
    failed.forEach(failure => {
      console.log(`   👤 ${failure.username}: ${failure.error}`);
    });
  }
  
  console.log('\n🎯 NEXT STEPS:');
  console.log('1. Go to http://localhost:5773');
  console.log('2. Click "Sign In" (not Sign Up)');
  console.log('3. Use any email/password combination above');
  console.log('4. Start testing the platform!');
  
  await pool.end();
}

// Executar script
createAllUsers()
  .then(() => {
    console.log('\n🏁 Script completed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n💥 Script failed:', error);
    process.exit(1);
  }); 