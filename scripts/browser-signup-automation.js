// 🚀 SCRIPT DE AUTOMAÇÃO DE SIGN UP NO NAVEGADOR
// Execute este script no Console do navegador (F12 → Console)
// ou salve como bookmarklet para usar facilmente

// Lista de usuários para criar automaticamente
const testUsers = [
  {
    username: 'newuser2024',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    password: 'TestPass123!'
  },
  {
    username: 'disney_lover',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    password: 'TestPass123!'
  },
  {
    username: 'pin_collector',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    password: 'TestPass123!'
  },
  {
    username: 'trading_pro',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    password: 'TestPass123!'
  },
  {
    username: 'collector_pro',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: 'james.wils<PERSON>@testmail.com',
    password: 'TestPass123!'
  },
  {
    username: 'teste123',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    password: 'TestPass123!'
  },
  {
    username: 'pintrader2024',
    firstName: 'David',
    lastName: 'Brown',
    email: '<EMAIL>',
    password: 'TestPass123!'
  }
];

// Função para esperar elemento aparecer
function waitForElement(selector, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector);
    if (element) {
      resolve(element);
      return;
    }
    
    const observer = new MutationObserver(() => {
      const element = document.querySelector(selector);
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found within ${timeout}ms`));
    }, timeout);
  });
}

// Função para esperar
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Função para preencher campo
function fillField(selector, value) {
  const element = document.querySelector(selector);
  if (element) {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    console.log(`✅ Preenchido ${selector}: ${value}`);
    return true;
  } else {
    console.log(`❌ Campo não encontrado: ${selector}`);
    return false;
  }
}

// Função para clicar em elemento
function clickElement(selector) {
  const element = document.querySelector(selector);
  if (element) {
    element.click();
    console.log(`✅ Clicado: ${selector}`);
    return true;
  } else {
    console.log(`❌ Elemento não encontrado: ${selector}`);
    return false;
  }
}

// Função para navegar para Sign Up
async function navigateToSignUp() {
  console.log('🔍 Procurando botão Sign Up...');
  
  // Possíveis seletores para o botão Sign Up
  const signUpSelectors = [
    'a[href*="signup"]',
    'button:contains("Sign Up")',
    '[data-testid="signup-button"]',
    '.signup-button',
    'a:contains("Sign Up")',
    'button[type="button"]:contains("Sign Up")'
  ];
  
  for (const selector of signUpSelectors) {
    if (clickElement(selector)) {
      await sleep(2000);
      return true;
    }
  }
  
  // Se não encontrou, tenta navegar diretamente
  console.log('🔄 Navegando diretamente para /signup...');
  window.location.href = window.location.origin + '/signup';
  await sleep(3000);
  return true;
}

// Função para preencher formulário de Sign Up
async function fillSignUpForm(user) {
  console.log(`📝 Preenchendo formulário para: ${user.username}`);
  
  // Aguardar formulário carregar
  await sleep(2000);
  
  // Possíveis seletores para os campos
  const fieldSelectors = {
    firstName: [
      'input[name="firstName"]',
      'input[placeholder*="First"]',
      '#firstName',
      '[data-testid="first-name"]'
    ],
    lastName: [
      'input[name="lastName"]',
      'input[placeholder*="Last"]',
      '#lastName',
      '[data-testid="last-name"]'
    ],
    username: [
      'input[name="username"]',
      'input[placeholder*="username"]',
      '#username',
      '[data-testid="username"]'
    ],
    email: [
      'input[name="email"]',
      'input[type="email"]',
      '#email',
      '[data-testid="email"]'
    ],
    password: [
      'input[name="password"]',
      'input[type="password"]',
      '#password',
      '[data-testid="password"]'
    ]
  };
  
  // Preencher cada campo
  for (const [fieldName, selectors] of Object.entries(fieldSelectors)) {
    let filled = false;
    for (const selector of selectors) {
      if (fillField(selector, user[fieldName])) {
        filled = true;
        break;
      }
    }
    if (!filled) {
      console.log(`⚠️ Campo ${fieldName} não encontrado`);
    }
    await sleep(500);
  }
}

// Função para submeter formulário
async function submitForm() {
  console.log('🚀 Submetendo formulário...');
  
  const submitSelectors = [
    'button[type="submit"]',
    'button:contains("Sign Up")',
    'button:contains("Create Account")',
    '[data-testid="submit-button"]',
    '.submit-button'
  ];
  
  for (const selector of submitSelectors) {
    if (clickElement(selector)) {
      return true;
    }
  }
  
  // Tentar submeter via Enter
  const form = document.querySelector('form');
  if (form) {
    form.dispatchEvent(new Event('submit', { bubbles: true }));
    console.log('✅ Formulário submetido via evento');
    return true;
  }
  
  console.log('❌ Não foi possível submeter o formulário');
  return false;
}

// Função para aguardar sucesso ou erro
async function waitForResult() {
  console.log('⏳ Aguardando resultado...');
  
  for (let i = 0; i < 30; i++) { // 30 segundos máximo
    await sleep(1000);
    
    // Verificar se houve sucesso (redirecionamento ou mensagem)
    if (window.location.pathname !== '/signup' && window.location.pathname !== '/') {
      console.log('✅ Sucesso! Usuário criado e redirecionado');
      return { success: true, message: 'Usuário criado com sucesso' };
    }
    
    // Verificar mensagens de erro
    const errorSelectors = [
      '.error-message',
      '.alert-error',
      '[role="alert"]',
      '.text-red-500',
      '.text-red-600'
    ];
    
    for (const selector of errorSelectors) {
      const errorElement = document.querySelector(selector);
      if (errorElement && errorElement.textContent.trim()) {
        console.log('❌ Erro encontrado:', errorElement.textContent);
        return { success: false, error: errorElement.textContent };
      }
    }
  }
  
  return { success: false, error: 'Timeout - não foi possível determinar o resultado' };
}

// Função para fazer logout
async function logout() {
  console.log('🚪 Fazendo logout...');
  
  const logoutSelectors = [
    'button:contains("Logout")',
    'button:contains("Sign Out")',
    '[data-testid="logout-button"]',
    '.logout-button'
  ];
  
  for (const selector of logoutSelectors) {
    if (clickElement(selector)) {
      await sleep(2000);
      return true;
    }
  }
  
  // Tentar limpar localStorage e recarregar
  localStorage.clear();
  sessionStorage.clear();
  window.location.href = window.location.origin;
  await sleep(3000);
  return true;
}

// Função principal para criar um usuário
async function createUser(user) {
  console.log(`\n🔨 Criando usuário: ${user.username}`);
  console.log('═'.repeat(50));
  
  try {
    // 1. Navegar para Sign Up
    await navigateToSignUp();
    
    // 2. Preencher formulário
    await fillSignUpForm(user);
    
    // 3. Submeter
    await submitForm();
    
    // 4. Aguardar resultado
    const result = await waitForResult();
    
    if (result.success) {
      console.log(`✅ Usuário ${user.username} criado com sucesso!`);
      
      // 5. Fazer logout para próximo usuário
      await logout();
      
      return { success: true, username: user.username };
    } else {
      console.log(`❌ Falha ao criar ${user.username}: ${result.error}`);
      return { success: false, username: user.username, error: result.error };
    }
    
  } catch (error) {
    console.error(`💥 Erro ao criar ${user.username}:`, error);
    return { success: false, username: user.username, error: error.message };
  }
}

// Função principal para criar todos os usuários
async function createAllUsers() {
  console.log('🚀 AUTOMAÇÃO DE SIGN UP INICIADA');
  console.log('═'.repeat(80));
  console.log(`📝 Criando ${testUsers.length} usuários automaticamente...\n`);
  
  const results = [];
  const successful = [];
  const failed = [];
  
  for (const user of testUsers) {
    const result = await createUser(user);
    results.push(result);
    
    if (result.success) {
      successful.push(result.username);
    } else {
      failed.push({ username: result.username, error: result.error });
    }
    
    // Pausa entre usuários
    await sleep(2000);
  }
  
  // Relatório final
  console.log('\n' + '═'.repeat(80));
  console.log('📊 RELATÓRIO FINAL');
  console.log('═'.repeat(80));
  
  console.log(`\n✅ CRIADOS COM SUCESSO (${successful.length}):`);
  successful.forEach(username => {
    console.log(`   👤 ${username}`);
  });
  
  if (failed.length > 0) {
    console.log(`\n❌ FALHARAM (${failed.length}):`);
    failed.forEach(failure => {
      console.log(`   👤 ${failure.username}: ${failure.error}`);
    });
  }
  
  console.log(`\n🎯 RESUMO: ${successful.length}/${testUsers.length} usuários criados`);
  console.log('\n🏁 Automação concluída!');
  
  return { successful, failed, total: testUsers.length };
}

// Função para executar um usuário específico
function createSingleUser(username) {
  const user = testUsers.find(u => u.username === username);
  if (user) {
    return createUser(user);
  } else {
    console.log(`❌ Usuário ${username} não encontrado na lista`);
    console.log('Usuários disponíveis:', testUsers.map(u => u.username));
  }
}

// Expor funções globalmente para uso no console
window.PinPalSignUpAutomation = {
  createAllUsers,
  createSingleUser,
  testUsers: testUsers.map(u => u.username)
};

console.log('🎯 SCRIPT DE AUTOMAÇÃO CARREGADO!');
console.log('\n📋 COMANDOS DISPONÍVEIS:');
console.log('• PinPalSignUpAutomation.createAllUsers() - Criar todos os usuários');
console.log('• PinPalSignUpAutomation.createSingleUser("username") - Criar usuário específico');
console.log('• PinPalSignUpAutomation.testUsers - Ver lista de usuários');
console.log('\n🚀 Execute: PinPalSignUpAutomation.createAllUsers()'); 