#!/usr/bin/env node

// 🚀 AUTOMAÇÃO DE SIGN UP VIA PUPPETEER
// Executa automaticamente o sign up de todos os usuários de teste

const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');

// Lista de usuários para criar
const USERS = [
  { username: 'newuser2024', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'disney_lover', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'pin_collector', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'trading_pro', firstName: '<PERSON>', lastName: '<PERSON>', email: 'sofia.rod<PERSON>ue<PERSON>@testmail.com', password: 'TestPass123!' },
  { username: 'collector_pro', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'teste123', firstName: 'Maria', lastName: 'Garcia', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'pintrader2024', firstName: 'David', lastName: 'Brown', email: '<EMAIL>', password: 'TestPass123!' }
];

const PINPAL_URL = 'http://localhost:5773';
const SIGNUP_URL = `${PINPAL_URL}/signup`;

console.log('🚀 INICIANDO AUTOMAÇÃO DE SIGN UP');
console.log('═'.repeat(80));
console.log(`📝 Criando ${USERS.length} usuários automaticamente...`);
console.log(`🌐 URL: ${PINPAL_URL}`);
console.log('═'.repeat(80));

async function createUser(page, user, index, total) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🔨 CRIANDO USUÁRIO ${index + 1}/${total}: ${user.username}`);
  console.log(`${'='.repeat(60)}`);
  
  try {
    // 1. Navegar para signup
    console.log('📍 Navegando para página de signup...');
    await page.goto(SIGNUP_URL, { waitUntil: 'networkidle0' });
    
    // 2. Aguardar formulário carregar
    console.log('⏳ Aguardando formulário carregar...');
    await page.waitForSelector('input[name="firstName"]', { timeout: 10000 });
    
    // 3. Preencher campos
    console.log('📝 Preenchendo formulário...');
    
    await page.type('input[name="firstName"]', user.firstName);
    console.log(`✅ First Name: ${user.firstName}`);
    
    await page.type('input[name="lastName"]', user.lastName);
    console.log(`✅ Last Name: ${user.lastName}`);
    
    await page.type('input[name="username"]', user.username);
    console.log(`✅ Username: ${user.username}`);
    
    await page.type('input[name="email"]', user.email);
    console.log(`✅ Email: ${user.email}`);
    
    await page.type('input[name="password"]', user.password);
    console.log(`✅ Password: ${user.password}`);
    
    // 4. Submeter formulário
    console.log('🚀 Submetendo formulário...');
    await page.click('button[type="submit"]');
    
    // 5. Aguardar resultado
    console.log('⏳ Aguardando resultado...');
    
    try {
      // Aguardar redirecionamento (sucesso) ou mensagem de erro
      await Promise.race([
        page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 }),
        page.waitForSelector('.error-message, .text-red-500, [role="alert"]', { timeout: 30000 })
      ]);
      
      // Verificar se ainda está na página de signup (erro) ou foi redirecionado (sucesso)
      const currentUrl = page.url();
      
      if (currentUrl.includes('/signup')) {
        // Ainda na página de signup, verificar erro
        const errorElement = await page.$('.error-message, .text-red-500, [role="alert"]');
        if (errorElement) {
          const errorText = await page.evaluate(el => el.textContent, errorElement);
          console.log(`❌ Erro: ${errorText}`);
          return { success: false, username: user.username, error: errorText };
        } else {
          console.log('❌ Erro desconhecido - ainda na página de signup');
          return { success: false, username: user.username, error: 'Erro desconhecido' };
        }
      } else {
        // Redirecionado - sucesso!
        console.log(`✅ ${user.username} criado com sucesso!`);
        
        // 6. Fazer logout para próximo usuário
        console.log('🚪 Fazendo logout...');
        await page.evaluate(() => {
          localStorage.clear();
          sessionStorage.clear();
        });
        
        return { success: true, username: user.username };
      }
      
    } catch (timeoutError) {
      console.log('❌ Timeout - não foi possível determinar resultado');
      return { success: false, username: user.username, error: 'Timeout' };
    }
    
  } catch (error) {
    console.log(`❌ Erro inesperado: ${error.message}`);
    return { success: false, username: user.username, error: error.message };
  }
}

async function runAutomation() {
  let browser;
  const results = {
    successful: [],
    failed: [],
    startTime: new Date()
  };
  
  try {
    // Iniciar browser
    console.log('🌐 Iniciando navegador...');
    browser = await puppeteer.launch({
      headless: false, // Mostrar navegador para debug
      defaultViewport: { width: 1280, height: 720 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Criar cada usuário
    for (let i = 0; i < USERS.length; i++) {
      const user = USERS[i];
      const result = await createUser(page, user, i, USERS.length);
      
      if (result.success) {
        results.successful.push(result.username);
      } else {
        results.failed.push({ username: result.username, error: result.error });
      }
      
      // Pausa entre usuários
      if (i < USERS.length - 1) {
        console.log('⏸️ Pausando antes do próximo usuário...');
        await page.waitForTimeout(2000);
      }
    }
    
  } catch (error) {
    console.error('💥 Erro fatal:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
  
  // Relatório final
  const endTime = new Date();
  const duration = Math.round((endTime - results.startTime) / 1000);
  
  console.log(`\n${'='.repeat(80)}`);
  console.log('📊 RELATÓRIO FINAL');
  console.log(`${'='.repeat(80)}`);
  
  console.log(`\n⏰ Tempo total: ${Math.floor(duration / 60)}m ${duration % 60}s`);
  console.log(`🎯 Resultado: ${results.successful.length}/${USERS.length} usuários criados`);
  
  if (results.successful.length > 0) {
    console.log(`\n✅ CRIADOS COM SUCESSO (${results.successful.length}):`);
    results.successful.forEach(username => {
      console.log(`   👤 ${username}`);
    });
  }
  
  if (results.failed.length > 0) {
    console.log(`\n❌ FALHARAM (${results.failed.length}):`);
    results.failed.forEach(failure => {
      console.log(`   👤 ${failure.username}: ${failure.error}`);
    });
  }
  
  console.log(`\n🏁 Automação concluída!`);
  console.log(`📝 Usuários criados estão prontos para login com senha: TestPass123!`);
  
  // Salvar relatório
  const reportPath = path.join(__dirname, '..', 'signup-automation-report.json');
  fs.writeFileSync(reportPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    duration: duration,
    total: USERS.length,
    successful: results.successful,
    failed: results.failed,
    users: USERS.map(u => ({ username: u.username, firstName: u.firstName, lastName: u.lastName }))
  }, null, 2));
  
  console.log(`📄 Relatório salvo em: ${reportPath}`);
  
  return results;
}

// Verificar se Puppeteer está instalado
async function checkPuppeteer() {
  try {
    require.resolve('puppeteer');
    return true;
  } catch (error) {
    console.log('❌ Puppeteer não encontrado!');
    console.log('📦 Instalando Puppeteer...');
    
    const { execSync } = require('child_process');
    try {
      execSync('npm install puppeteer', { stdio: 'inherit' });
      console.log('✅ Puppeteer instalado com sucesso!');
      return true;
    } catch (installError) {
      console.log('❌ Falha ao instalar Puppeteer:', installError.message);
      return false;
    }
  }
}

// Função principal
async function main() {
  console.log('🔍 Verificando dependências...');
  
  const puppeteerOk = await checkPuppeteer();
  if (!puppeteerOk) {
    console.log('💥 Não foi possível instalar Puppeteer. Saindo...');
    process.exit(1);
  }
  
  console.log('✅ Dependências OK!');
  
  await runAutomation();
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runAutomation, USERS }; 