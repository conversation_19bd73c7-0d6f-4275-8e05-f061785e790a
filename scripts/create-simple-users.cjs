#!/usr/bin/env node

const { Pool } = require('pg');
const crypto = require('crypto');

// Configuração do PostgreSQL
const pool = new Pool({
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5433'),
  database: process.env.POSTGRES_DATABASE || 'postgres',
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'pinpal123',
  ssl: false,
});

// Lista de usuários para criar
const testUsers = [
  {
    username: 'newuser2024',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    bio: 'Disney pin collector and trader from Brazil'
  },
  {
    username: 'disney_lover',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: 'em<PERSON>.joh<PERSON>@testmail.com',
    bio: 'Passionate about Disney pins, especially classic characters'
  },
  {
    username: 'pin_collector',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    bio: 'Collector of rare Disney and Marvel pins'
  },
  {
    username: 'trading_pro',
    firstName: 'Sofia',
    lastName: 'Rodriguez',
    email: '<EMAIL>',
    bio: 'Professional pin trader with 5+ years experience'
  },
  {
    username: 'collector_pro',
    firstName: 'James',
    lastName: 'Wilson',
    email: '<EMAIL>',
    bio: 'Serious collector focusing on limited edition pins'
  },
  {
    username: 'teste123',
    firstName: 'Maria',
    lastName: 'Santos',
    email: '<EMAIL>',
    bio: 'New to pin collecting, excited to learn and trade'
  },
  {
    username: 'pintrader2024',
    firstName: 'Carlos',
    lastName: 'Oliveira',
    email: '<EMAIL>',
    bio: 'Experienced trader specializing in rare Disney pins'
  }
];

function generateUserId() {
  return crypto.randomUUID();
}

async function createUser(user) {
  const userId = generateUserId();
  const displayName = `${user.firstName} ${user.lastName}`;
  
  try {
    console.log(`🔨 Creating user: ${user.username} (${user.email})`);
    
    const query = `
      INSERT INTO "user" (
        id, username, display_name, first_name, last_name, email, bio,
        created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      RETURNING *
    `;
    
    const values = [
      userId,
      user.username,
      displayName,
      user.firstName,
      user.lastName,
      user.email,
      user.bio
    ];
    
    const result = await pool.query(query, values);
    console.log(`✅ User ${user.username} created successfully!`);
    
    return {
      success: true,
      user: {
        username: user.username,
        email: user.email,
        displayName: displayName,
        postgresId: userId
      }
    };
    
  } catch (error) {
    if (error.code === '23505') { // Unique violation
      console.log(`⚠️ User ${user.username} already exists, skipping...`);
      return {
        success: false,
        user: user.username,
        error: 'already_exists',
        skipped: true
      };
    } else {
      console.error(`❌ Error creating user ${user.username}:`, error.message);
      return {
        success: false,
        user: user.username,
        error: error.message
      };
    }
  }
}

async function createAllUsers() {
  console.log('🚀 SIMPLE USER CREATION SCRIPT');
  console.log('=' .repeat(80));
  console.log(`📝 Creating ${testUsers.length} test users in PostgreSQL...\n`);
  
  // Testar conexão
  try {
    await pool.query('SELECT 1');
    console.log('✅ PostgreSQL connection: OK\n');
  } catch (error) {
    console.error('❌ PostgreSQL connection failed:', error.message);
    return;
  }
  
  const results = [];
  const successful = [];
  const failed = [];
  const skipped = [];
  
  // Criar usuários
  for (const user of testUsers) {
    const result = await createUser(user);
    results.push(result);
    
    if (result.success) {
      successful.push(result.user);
    } else if (result.skipped) {
      skipped.push(result.user);
    } else {
      failed.push({ username: result.user, error: result.error });
    }
  }
  
  // Relatório final
  console.log('\n' + '=' .repeat(80));
  console.log('📊 FINAL REPORT');
  console.log('=' .repeat(80));
  
  console.log(`\n✅ CREATED (${successful.length}):`);
  successful.forEach(user => {
    console.log(`   👤 ${user.username} (${user.email})`);
    console.log(`      📝 Display Name: ${user.displayName}`);
    console.log(`      🗄️ Postgres ID: ${user.postgresId}`);
    console.log('');
  });
  
  if (skipped.length > 0) {
    console.log(`\n⚠️ ALREADY EXISTS (${skipped.length}):`);
    skipped.forEach(username => {
      console.log(`   👤 ${username} - já existe no banco`);
    });
  }
  
  if (failed.length > 0) {
    console.log(`\n❌ FAILED (${failed.length}):`);
    failed.forEach(failure => {
      console.log(`   👤 ${failure.username}: ${failure.error}`);
    });
  }
  
  console.log('\n🎯 COMO TESTAR AGORA:');
  console.log('1. Vá para http://localhost:5773');
  console.log('2. Clique em "Sign Up"');
  console.log('3. Use qualquer username da lista acima');
  console.log('4. Complete o cadastro - o username check agora funciona!');
  console.log('5. O sistema vai criar no Firebase e sincronizar');
  
  console.log('\n💡 USERNAMES DISPONÍVEIS GARANTIDOS:');
  successful.forEach(user => {
    console.log(`   ✅ ${user.username}`);
  });
  
  console.log('\n🔧 CORREÇÃO APLICADA:');
  console.log('- ✅ Username check corrigido (não mais "already taken")');
  console.log('- ✅ PostgreSQL como prioridade na verificação');
  console.log('- ✅ Firestore como fallback apenas');
  console.log('- ✅ Abordagem otimista em caso de erro');
  
  await pool.end();
}

// Executar script
createAllUsers()
  .then(() => {
    console.log('\n🏁 Script completed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n💥 Script failed:', error);
    process.exit(1);
  }); 