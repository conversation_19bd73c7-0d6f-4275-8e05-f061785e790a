// 📚 BOOKMARKLET PARA AUTOMAÇÃO DE SIGN UP
// Cole este código na barra de favoritos do seu navegador

javascript:(function(){
  const users = [
    { username: 'newuser2024', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
    { username: 'disney_lover', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
    { username: 'pin_collector', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
    { username: 'trading_pro', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
    { username: 'collector_pro', firstName: '<PERSON>', lastName: '<PERSON>', email: 'jam<PERSON>.wils<PERSON>@testmail.com', password: 'TestPass123!' },
    { username: 'teste123', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
    { username: 'pintrader2024', firstName: 'Carlos', lastName: 'Oliveira', email: '<EMAIL>', password: 'TestPass123!' }
  ];
  
  const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
  
  function fill(selector, value) {
    const input = document.querySelector(selector);
    if (input) {
      input.focus();
      input.value = value;
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
      input.blur();
      return true;
    }
    return false;
  }
  
  async function createUser(user) {
    console.log(`Creating: ${user.username}`);
    
    if (!window.location.pathname.includes('signup')) {
      window.location.href = '/signup';
      await wait(3000);
    }
    
    await wait(2000);
    
    fill('input[name="firstName"]', user.firstName);
    await wait(300);
    fill('input[name="lastName"]', user.lastName);
    await wait(300);
    fill('input[name="username"]', user.username);
    await wait(300);
    fill('input[name="email"]', user.email);
    await wait(300);
    fill('input[name="password"]', user.password);
    await wait(500);
    
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) submitBtn.click();
    
    for (let i = 0; i < 20; i++) {
      await wait(1000);
      if (!window.location.pathname.includes('signup')) {
        console.log(`✅ ${user.username} created!`);
        await wait(2000);
        localStorage.clear();
        sessionStorage.clear();
        window.location.href = '/';
        await wait(3000);
        return true;
      }
      const error = document.querySelector('.error-message, .text-red-500');
      if (error && error.textContent.trim()) {
        console.log(`❌ Error: ${error.textContent}`);
        return false;
      }
    }
    return false;
  }
  
  async function createAll() {
    console.log('🚀 Starting automation...');
    const results = { success: [], failed: [] };
    
    for (const user of users) {
      const success = await createUser(user);
      if (success) {
        results.success.push(user.username);
      } else {
        results.failed.push(user.username);
      }
      await wait(2000);
    }
    
    console.log(`✅ Success: ${results.success.length}/${users.length}`);
    console.log('Successful:', results.success);
    if (results.failed.length > 0) {
      console.log('Failed:', results.failed);
    }
    
    return results;
  }
  
  window.PinPalAuto = { createAll, users: users.map(u => u.username) };
  
  if (confirm('Start PinPal user creation automation?')) {
    createAll();
  } else {
    console.log('PinPal automation loaded. Run PinPalAuto.createAll() to start.');
  }
})(); 