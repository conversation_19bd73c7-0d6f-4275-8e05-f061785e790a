#!/usr/bin/env node

import { Pool } from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Database connection
const pool = new Pool({
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5433'),
  database: process.env.POSTGRES_DATABASE || 'postgres',
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'pinpal123',
  ssl: false,
});

// Simple test users
const SIMPLE_USERS = [
  {
    id: 'test-admin-001',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'Test',
    username: 'admin_test',
    displayName: 'Admin Test',
    bio: 'Test admin user for development',
    location: 'Test City',
    role: 'admin',
    emailVerified: true
  },
  {
    id: 'test-user-002',
    email: '<EMAIL>',
    firstName: 'User',
    lastName: 'Test',
    username: 'user_test',
    displayName: 'User Test',
    bio: 'Test regular user for development',
    location: 'Test City',
    role: 'user',
    emailVerified: true
  },
  {
    id: 'test-trader-003',
    email: '<EMAIL>',
    firstName: 'Trader',
    lastName: 'Test',
    username: 'trader_test',
    displayName: 'Trader Test',
    bio: 'Test trader user for development',
    location: 'Test City',
    role: 'user',
    emailVerified: true
  }
];

async function createSimpleUsers() {
  console.log('🚀 Creating Simple Test Users...');
  console.log('================================');
  
  for (const user of SIMPLE_USERS) {
    try {
      const query = `
        INSERT INTO "user" (
          id, email, first_name, last_name, username, 
          display_name, bio, location, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
        ON CONFLICT (id) DO UPDATE SET
          email = EXCLUDED.email,
          first_name = EXCLUDED.first_name,
          last_name = EXCLUDED.last_name,
          username = EXCLUDED.username,
          display_name = EXCLUDED.display_name,
          bio = EXCLUDED.bio,
          location = EXCLUDED.location,
          updated_at = NOW()
      `;
      
      await pool.query(query, [
        user.id,
        user.email,
        user.firstName,
        user.lastName,
        user.username,
        user.displayName,
        user.bio,
        user.location
      ]);
      
      console.log(`✅ Created/Updated: ${user.displayName} (${user.email})`);
    } catch (error) {
      console.log(`❌ Error creating ${user.displayName}: ${error.message}`);
    }
  }
}

async function listUsers() {
  console.log('\n📋 Current Users in Database:');
  console.log('=============================');
  
  try {
    const result = await pool.query(
      'SELECT id, email, first_name, last_name, display_name FROM "user" ORDER BY created_at DESC LIMIT 10'
    );
    
    if (result.rows.length === 0) {
      console.log('No users found in database');
    } else {
      result.rows.forEach((user, index) => {
        console.log(`${index + 1}. ${user.display_name} (${user.email}) - ID: ${user.id}`);
      });
    }
  } catch (error) {
    console.log(`❌ Error listing users: ${error.message}`);
  }
}

async function main() {
  try {
    await createSimpleUsers();
    await listUsers();
    
    console.log('\n🎉 Simple test users created successfully!');
    console.log('\n📧 Test Credentials:');
    console.log('===================');
    console.log('🔐 Since this uses Firebase Auth, you need to:');
    console.log('1. Go to http://localhost:5773');
    console.log('2. Click "Sign Up" to create a new account');
    console.log('3. Use any email/password combination');
    console.log('4. The system will automatically sync with PostgreSQL');
    console.log('\n💡 Suggested test emails:');
    SIMPLE_USERS.forEach(user => {
      console.log(`   - ${user.email} (${user.role})`);
    });
    
  } catch (error) {
    console.error('❌ Error during execution:', error);
  } finally {
    await pool.end();
  }
}

main(); 