#!/usr/bin/env node

// 🧪 TESTE COMPLETO DE USUÁRIO - Firebase Auth + PostgreSQL
const { Pool } = require('pg');

console.log('🧪 TESTE COMPLETO DE USUÁRIO PINPAL');
console.log('═'.repeat(60));

// Configuração do banco PostgreSQL
const pool = new Pool({
  host: 'localhost',
  port: 5433,
  database: 'postgres',
  user: 'postgres',
  password: 'pinpal123'
});

// Dados do usuário de teste
const TEST_USER = {
  username: 'newuser2024',
  email: '<EMAIL>',
  firstName: 'Alex',
  lastName: 'Silva'
};

async function testCompleteUser() {
  try {
    console.log('🔍 ETAPA 1: Verificando usuário no PostgreSQL...');
    
    const result = await pool.query(`
      SELECT 
        id, username, first_name, last_name, email, 
        created_at, is_active, email_verified, role, status
      FROM "user" 
      WHERE username = $1
    `, [TEST_USER.username]);
    
    if (result.rows.length === 0) {
      console.log('❌ Usuário não encontrado no PostgreSQL');
      return false;
    }
    
    const user = result.rows[0];
    console.log('✅ Usuário encontrado no PostgreSQL:');
    console.log(`   🆔 ID: ${user.id}`);
    console.log(`   👤 Username: ${user.username}`);
    console.log(`   📧 Email: ${user.email}`);
    console.log(`   🎭 Nome: ${user.first_name} ${user.last_name}`);
    console.log(`   📅 Criado: ${user.created_at}`);
    console.log(`   ✅ Ativo: ${user.is_active}`);
    console.log(`   📧 Email verificado: ${user.email_verified}`);
    console.log(`   🔐 Role: ${user.role}`);
    console.log(`   📊 Status: ${user.status}`);
    
    console.log('\n🔍 ETAPA 2: Verificando disponibilidade do username...');
    
    // Testar API de verificação de username
    const http = require('http');
    
    const checkUsernameAvailability = () => {
      return new Promise((resolve, reject) => {
        const options = {
          hostname: 'localhost',
          port: 3001,
          path: `/api/auth/username/check?username=${TEST_USER.username}`,
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        };
        
        const req = http.request(options, (res) => {
          let body = '';
          res.on('data', chunk => body += chunk);
          res.on('end', () => {
            try {
              const response = JSON.parse(body);
              resolve({ statusCode: res.statusCode, data: response });
            } catch (error) {
              resolve({ statusCode: res.statusCode, body, data: null });
            }
          });
        });
        
        req.on('error', reject);
        req.end();
      });
    };
    
    const usernameCheck = await checkUsernameAvailability();
    
    if (usernameCheck.statusCode === 200) {
      console.log('✅ API de username funcionando:');
      console.log(`   📊 Disponível: ${usernameCheck.data.available}`);
      console.log(`   💬 Mensagem: ${usernameCheck.data.message}`);
      
      if (!usernameCheck.data.available) {
        console.log('✅ Correto! Username já está em uso (como esperado)');
      }
    } else {
      console.log('❌ Erro na API de username:', usernameCheck.body);
    }
    
    console.log('\n🔍 ETAPA 3: Instruções para teste manual...');
    console.log('═'.repeat(60));
    console.log('🌐 TESTE MANUAL NO FRONTEND:');
    console.log('');
    console.log('1. 📂 Abra: http://localhost:5773');
    console.log('2. 🔑 Clique em "Sign In"');
    console.log('3. 📝 Use estas credenciais:');
    console.log(`   📧 Email: ${TEST_USER.email}`);
    console.log(`   🔑 Password: TestPass123!`);
    console.log('');
    console.log('📋 CENÁRIOS DE TESTE:');
    console.log('');
    console.log('🎯 CENÁRIO A - Usuário Existente:');
    console.log('   ✅ Se o login funcionar → Sistema OK');
    console.log('   ❌ Se falhar → Problema no Firebase Auth');
    console.log('');
    console.log('🎯 CENÁRIO B - Novo Usuário:');
    console.log('   📝 Clique em "Sign Up"');
    console.log('   👤 Tente criar conta com email diferente');
    console.log('   🔄 Sistema deve sincronizar PostgreSQL + Firebase');
    console.log('');
    console.log('🔧 TROUBLESHOOTING:');
    console.log('   • Se login falhar: Usuário não existe no Firebase Auth');
    console.log('   • Se signup falhar: Verificar console do browser');
    console.log('   • Se sincronização falhar: Verificar logs do backend');
    console.log('');
    console.log('💡 DICA: Abra DevTools (F12) para ver logs detalhados');
    console.log('═'.repeat(60));
    
    return true;
    
  } catch (error) {
    console.error('💥 Erro no teste:', error.message);
    return false;
  } finally {
    await pool.end();
  }
}

// Executar teste
if (require.main === module) {
  testCompleteUser().then(success => {
    if (success) {
      console.log('\n🎉 TESTE PREPARADO COM SUCESSO!');
      console.log('📱 Agora faça o teste manual no frontend');
    } else {
      console.log('\n❌ TESTE FALHOU');
      console.log('🔧 Verifique as configurações do sistema');
    }
  }).catch(console.error);
}

module.exports = { testCompleteUser, TEST_USER }; 