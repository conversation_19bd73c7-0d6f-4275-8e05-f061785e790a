import { collection, query, where, getDocs } from 'firebase/firestore';
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';

// Configuração do Firebase (mesma do projeto)
const firebaseConfig = {
  apiKey: "AIzaSyDr2dNLyGxZPJRp3HfKNxqMbFqjlOjGwxA",
  authDomain: "pinpal-project.firebaseapp.com",
  projectId: "pinpal-project",
  storageBucket: "pinpal-project.appspot.com",
  messagingSenderId: "1081932630476",
  appId: "1:1081932630476:web:7e5e3e5c8c9b8b8b8b8b8b",
  measurementId: "G-XXXXXXXXXX"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function debugUsernameCheck(username) {
  console.log(`🔍 DEBUGGING USERNAME CHECK: "${username}"`);
  console.log('=' .repeat(60));
  
  const cleanUsername = username.trim().toLowerCase();
  console.log(`📝 Clean username: "${cleanUsername}"`);
  
  // 1. Verificar Firestore
  console.log('\n🔥 STEP 1: Checking Firestore...');
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('username', '==', cleanUsername));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      console.log('✅ Firestore: Username available');
    } else {
      console.log('❌ Firestore: Username taken');
      querySnapshot.forEach((doc) => {
        const userData = doc.data();
        console.log(`   - Doc ID: ${doc.id}`);
        console.log(`   - User: ${userData.firstName} ${userData.lastName}`);
        console.log(`   - Email: ${userData.email}`);
        console.log(`   - Username: ${userData.username}`);
      });
      return { firestore: false, reason: 'taken_in_firestore' };
    }
  } catch (error) {
    console.log('⚠️ Firestore error:', error.message);
    console.log('   - This might cause the check to fail...');
  }
  
  // 2. Verificar PostgreSQL via API 1
  console.log('\n🐘 STEP 2: Checking PostgreSQL via /api/auth/username/check...');
  try {
    const response1 = await fetch(`http://localhost:3001/api/auth/username/check?username=${encodeURIComponent(cleanUsername)}`);
    const result1 = await response1.json();
    console.log(`   Status: ${response1.status}`);
    console.log(`   Response:`, result1);
    
    if (!result1.available) {
      return { firestore: true, postgresql_api1: false, reason: 'taken_in_postgresql_api1' };
    }
  } catch (error) {
    console.log('❌ API 1 error:', error.message);
  }
  
  // 3. Verificar PostgreSQL via API 2
  console.log('\n🐘 STEP 3: Checking PostgreSQL via /api/users/auth/username/check...');
  try {
    const response2 = await fetch(`http://localhost:3001/api/users/auth/username/check?username=${encodeURIComponent(cleanUsername)}`);
    const result2 = await response2.json();
    console.log(`   Status: ${response2.status}`);
    console.log(`   Response:`, result2);
    
    if (!result2.available) {
      return { firestore: true, postgresql_api1: true, postgresql_api2: false, reason: 'taken_in_postgresql_api2' };
    }
  } catch (error) {
    console.log('❌ API 2 error:', error.message);
  }
  
  console.log('\n✅ RESULT: Username should be available in all sources');
  return { firestore: true, postgresql_api1: true, postgresql_api2: true, available: true };
}

// Testar usernames específicos
const usernamesToTest = ['newuser2024', 'teste123', 'pintrader2024'];

async function runDebug() {
  for (const username of usernamesToTest) {
    const result = await debugUsernameCheck(username);
    console.log('\n📊 FINAL RESULT:', result);
    console.log('\n' + '='.repeat(80) + '\n');
  }
}

runDebug()
  .then(() => {
    console.log('🏁 Debug completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Debug failed:', error);
    process.exit(1);
  }); 