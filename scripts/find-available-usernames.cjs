#!/usr/bin/env node

const { Pool } = require('pg');

// Configuração do PostgreSQL
const pool = new Pool({
  user: process.env.POSTGRES_USER || 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  database: process.env.POSTGRES_DB || 'pinpal',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  port: process.env.POSTGRES_PORT || 5433,
});

// Lista de candidatos para testar
const candidates = [
  'newuser2024',
  'pintrader2024', 
  'collector2024',
  'disney_lover',
  'pin_collector',
  'trader_new',
  'pinpal_teste',
  'usuario_novo',
  'test_user_2024',
  'pin_enthusiast',
  'collector_pro',
  'trading_pro',
  'disney_pins',
  'marvel_fan',
  'pixar_lover',
  'unique_trader',
  'pin_master_2024',
  'collector_expert',
  'trading_expert',
  'pinpal_new',
  'teste123',
  'teste456',
  'teste789'
];

async function checkUsernameAvailability(username) {
  try {
    console.log(`  Verificando: ${username}...`);
    
    // Verificar no PostgreSQL
    const result = await pool.query(
      'SELECT username FROM "user" WHERE LOWER(username) = LOWER($1)',
      [username]
    );
    
    return result.rows.length === 0;
  } catch (error) {
    console.error(`  ❌ Erro ao verificar ${username}:`, error.message);
    return false;
  }
}

async function findAvailableUsernames() {
  console.log('🔍 Procurando usernames disponíveis...\n');
  
  // Primeiro testar conexão
  try {
    await pool.query('SELECT 1');
    console.log('✅ Conexão com PostgreSQL estabelecida\n');
  } catch (error) {
    console.error('❌ Erro de conexão:', error.message);
    return [];
  }
  
  const available = [];
  const taken = [];
  
  for (const username of candidates) {
    const isAvailable = await checkUsernameAvailability(username);
    
    if (isAvailable) {
      available.push(username);
      console.log(`✅ ${username} - DISPONÍVEL`);
    } else {
      taken.push(username);
      console.log(`❌ ${username} - ocupado`);
    }
  }
  
  console.log('\n📊 RESUMO:');
  console.log(`✅ Disponíveis (${available.length}):`, available);
  console.log(`❌ Ocupados (${taken.length}):`, taken);
  
  if (available.length > 0) {
    console.log('\n🎯 RECOMENDAÇÕES PARA TESTE:');
    available.slice(0, 5).forEach((username, index) => {
      console.log(`${index + 1}. ${username}`);
    });
  }
  
  await pool.end();
  return available;
}

findAvailableUsernames()
  .then(available => {
    if (available.length === 0) {
      console.log('\n⚠️ Nenhum username disponível encontrado. Tente criar variações próprias.');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro:', error);
    process.exit(1);
  }); 