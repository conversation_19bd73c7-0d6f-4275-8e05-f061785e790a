import { collection, query, where, getDocs } from 'firebase/firestore';
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';

// Configuração do Firebase (usando as mesmas configs do projeto)
const firebaseConfig = {
  apiKey: "AIzaSyDr2dNLyGxZPJRp3HfKNxqMbFqjlOjGwxA",
  authDomain: "pinpal-project.firebaseapp.com",
  projectId: "pinpal-project",
  storageBucket: "pinpal-project.appspot.com",
  messagingSenderId: "1081932630476",
  appId: "1:1081932630476:web:7e5e3e5c8c9b8b8b8b8b8b",
  measurementId: "G-XXXXXXXXXX"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function checkUsernameInFirestore(username) {
  try {
    console.log(`🔍 Verificando username "${username}" no Firestore...`);
    
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('username', '==', username.toLowerCase()));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      console.log(`✅ Username "${username}" está DISPONÍVEL no Firestore`);
      return { available: true, found: false };
    } else {
      console.log(`❌ Username "${username}" já está OCUPADO no Firestore`);
      querySnapshot.forEach((doc) => {
        const userData = doc.data();
        console.log(`   - Documento ID: ${doc.id}`);
        console.log(`   - Nome: ${userData.firstName} ${userData.lastName}`);
        console.log(`   - Email: ${userData.email}`);
      });
      return { available: false, found: true };
    }
  } catch (error) {
    console.error('❌ Erro ao verificar Firestore:', error);
    return { available: false, error: error.message };
  }
}

// Verificar o username específico
const username = process.argv[2] || 'teste123';
checkUsernameInFirestore(username)
  .then(result => {
    console.log('\n📊 Resultado final:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro:', error);
    process.exit(1);
  }); 