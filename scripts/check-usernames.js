#!/usr/bin/env node

import { Pool } from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Database connection
const pool = new Pool({
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5433'),
  database: process.env.POSTGRES_DATABASE || 'postgres',
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'pinpal123',
  ssl: false,
});

async function checkUsernames() {
  console.log('🔍 Checking Usernames in Database...');
  console.log('====================================');
  
  try {
    // List all existing usernames
    const result = await pool.query(
      'SELECT username, email, display_name FROM "user" WHERE username IS NOT NULL ORDER BY username'
    );
    
    console.log('📋 Usernames Already Taken:');
    console.log('---------------------------');
    if (result.rows.length === 0) {
      console.log('No usernames found in database');
    } else {
      result.rows.forEach((user, index) => {
        console.log(`${index + 1}. "${user.username}" - ${user.display_name} (${user.email})`);
      });
    }
    
    console.log('\n✅ Available Username Suggestions:');
    console.log('----------------------------------');
    const suggestions = [
      'teste123',
      'usuario2024',
      'pintrader',
      'collector99',
      'disney_fan',
      'pin_master',
      'trader_pro',
      'newuser123',
      'pinpal_user',
      'test_account'
    ];
    
    for (const username of suggestions) {
      try {
        const checkResult = await pool.query(
          'SELECT username FROM "user" WHERE LOWER(username) = LOWER($1)',
          [username]
        );
        
        if (checkResult.rows.length === 0) {
          console.log(`✅ "${username}" - AVAILABLE`);
        } else {
          console.log(`❌ "${username}" - TAKEN`);
        }
      } catch (error) {
        console.log(`⚠️ "${username}" - ERROR: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error checking usernames:', error);
  } finally {
    await pool.end();
  }
}

checkUsernames(); 