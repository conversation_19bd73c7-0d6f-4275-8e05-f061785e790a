// 🎯 SCRIPT SIMPLIFICADO PARA SIGN UP NO PINPAL
// Cole este código no Console do navegador (F12 → Console) na página do PinPal

const PINPAL_USERS = [
  { username: 'newuser2024', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'disney_lover', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'pin_collector', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'trading_pro', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'collector_pro', firstName: '<PERSON>', lastName: '<PERSON>', email: 'jam<PERSON>.<EMAIL>', password: 'TestPass123!' },
  { username: 'teste123', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'pintrader2024', firstName: 'David', lastName: 'Brown', email: '<EMAIL>', password: 'TestPass123!' }
];

// Função para aguardar
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Função para preencher campo e disparar eventos
function fillInput(selector, value) {
  const input = document.querySelector(selector);
  if (input) {
    input.focus();
    input.value = value;
    input.dispatchEvent(new Event('input', { bubbles: true }));
    input.dispatchEvent(new Event('change', { bubbles: true }));
    input.blur();
    console.log(`✅ ${selector}: ${value}`);
    return true;
  }
  console.log(`❌ Campo não encontrado: ${selector}`);
  return false;
}

// Função para clicar
function click(selector) {
  const element = document.querySelector(selector);
  if (element) {
    element.click();
    console.log(`✅ Clicado: ${selector}`);
    return true;
  }
  console.log(`❌ Elemento não encontrado: ${selector}`);
  return false;
}

// Função principal para criar usuário
async function createPinPalUser(user) {
  console.log(`\n🔨 Criando: ${user.username}`);
  console.log('─'.repeat(40));
  
  // 1. Ir para Sign Up (se não estiver lá)
  if (!window.location.pathname.includes('signup')) {
    console.log('📍 Navegando para /signup...');
    window.location.href = '/signup';
    await wait(3000);
  }
  
  // 2. Aguardar página carregar
  await wait(2000);
  
  // 3. Preencher formulário (seletores específicos do PinPal)
  console.log('📝 Preenchendo formulário...');
  
  fillInput('input[name="firstName"]', user.firstName);
  await wait(500);
  
  fillInput('input[name="lastName"]', user.lastName);
  await wait(500);
  
  fillInput('input[name="username"]', user.username);
  await wait(500);
  
  fillInput('input[name="email"]', user.email);
  await wait(500);
  
  fillInput('input[name="password"]', user.password);
  await wait(500);
  
  // 4. Submeter
  console.log('🚀 Submetendo...');
  const submitted = click('button[type="submit"]') || click('.submit-button') || click('button:last-of-type');
  
  if (!submitted) {
    console.log('❌ Botão submit não encontrado');
    return false;
  }
  
  // 5. Aguardar resultado (30 segundos)
  console.log('⏳ Aguardando resultado...');
  for (let i = 0; i < 30; i++) {
    await wait(1000);
    
    // Verificar se saiu da página de signup (sucesso)
    if (!window.location.pathname.includes('signup')) {
      console.log(`✅ ${user.username} criado com sucesso!`);
      
      // Fazer logout para próximo usuário
      await wait(2000);
      localStorage.clear();
      sessionStorage.clear();
      window.location.href = '/';
      await wait(3000);
      
      return true;
    }
    
    // Verificar erros
    const errorElement = document.querySelector('.error-message, .alert-error, [role="alert"], .text-red-500, .text-red-600');
    if (errorElement && errorElement.textContent.trim()) {
      console.log(`❌ Erro: ${errorElement.textContent}`);
      return false;
    }
  }
  
  console.log('❌ Timeout - não foi possível determinar resultado');
  return false;
}

// Função para criar todos os usuários
async function createAllPinPalUsers() {
  console.log('🚀 AUTOMAÇÃO PINPAL INICIADA');
  console.log('═'.repeat(50));
  
  const results = { success: [], failed: [] };
  
  for (const user of PINPAL_USERS) {
    const success = await createPinPalUser(user);
    
    if (success) {
      results.success.push(user.username);
    } else {
      results.failed.push(user.username);
    }
    
    await wait(2000);
  }
  
  // Relatório
  console.log('\n' + '═'.repeat(50));
  console.log('📊 RELATÓRIO FINAL');
  console.log('═'.repeat(50));
  
  console.log(`\n✅ SUCESSO (${results.success.length}):`);
  results.success.forEach(u => console.log(`   👤 ${u}`));
  
  if (results.failed.length > 0) {
    console.log(`\n❌ FALHARAM (${results.failed.length}):`);
    results.failed.forEach(u => console.log(`   👤 ${u}`));
  }
  
  console.log(`\n🎯 TOTAL: ${results.success.length}/${PINPAL_USERS.length} criados`);
  
  return results;
}

// Função para criar usuário específico
function createSpecificUser(username) {
  const user = PINPAL_USERS.find(u => u.username === username);
  if (user) {
    return createPinPalUser(user);
  } else {
    console.log(`❌ Usuário '${username}' não encontrado`);
    console.log('Disponíveis:', PINPAL_USERS.map(u => u.username).join(', '));
  }
}

// Expor globalmente
window.PinPal = {
  createAll: createAllPinPalUsers,
  createUser: createSpecificUser,
  users: PINPAL_USERS.map(u => u.username)
};

console.log('🎯 AUTOMAÇÃO PINPAL CARREGADA!');
console.log('\n📋 COMANDOS:');
console.log('• PinPal.createAll() - Criar todos os usuários');
console.log('• PinPal.createUser("username") - Criar usuário específico');
console.log('• PinPal.users - Ver lista de usuários');
console.log('\n🚀 EXECUTE: PinPal.createAll()'); 