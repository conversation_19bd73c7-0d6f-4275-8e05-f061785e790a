#!/usr/bin/env node

// 🧪 TESTE DE LOGIN - Verificar se usuários criados funcionam
const http = require('http');

const TEST_USER = {
  username: 'newuser2024',
  password: 'TestPass123!'
};

const API_BASE = 'http://localhost:3001';

console.log('🧪 TESTANDO LOGIN DO USUÁRIO');
console.log('═'.repeat(50));
console.log(`👤 Username: ${TEST_USER.username}`);
console.log(`🔑 Password: ${TEST_USER.password}`);
console.log('═'.repeat(50));

// Função para fazer requisição HTTP
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const response = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: body ? JSON.parse(body) : null
          };
          resolve(response);
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: null
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Testar login
async function testLogin() {
  console.log('🔍 Tentando fazer login...');
  
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  try {
    const response = await makeRequest(options, TEST_USER);
    
    console.log(`📊 Status Code: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      console.log('✅ LOGIN BEM-SUCEDIDO!');
      
      if (response.data) {
        console.log('\n📋 Dados do usuário:');
        console.log(`   🆔 ID: ${response.data.user?.id || 'N/A'}`);
        console.log(`   👤 Username: ${response.data.user?.username || 'N/A'}`);
        console.log(`   📧 Email: ${response.data.user?.email || 'N/A'}`);
        console.log(`   🎭 Nome: ${response.data.user?.firstName || 'N/A'} ${response.data.user?.lastName || 'N/A'}`);
        console.log(`   🔑 Token: ${response.data.token ? 'Recebido' : 'Não recebido'}`);
      }
      
      return true;
    } else {
      console.log('❌ FALHA NO LOGIN');
      console.log(`📄 Resposta: ${response.body}`);
      
      if (response.data?.message) {
        console.log(`💬 Mensagem: ${response.data.message}`);
      }
      
      return false;
    }
    
  } catch (error) {
    console.log('💥 ERRO NA REQUISIÇÃO:', error.message);
    return false;
  }
}

// Verificar se usuário existe
async function checkUserExists() {
  console.log('🔍 Verificando se usuário existe...');
  
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/auth/username/check',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  try {
    const response = await makeRequest(options, { username: TEST_USER.username });
    
    if (response.data) {
      if (response.data.available === false) {
        console.log('✅ Usuário existe no sistema');
        return true;
      } else {
        console.log('❌ Usuário NÃO existe no sistema');
        return false;
      }
    } else {
      console.log('⚠️ Resposta inválida do servidor');
      return false;
    }
    
  } catch (error) {
    console.log('💥 Erro ao verificar usuário:', error.message);
    return false;
  }
}

// Função principal
async function runTest() {
  console.log('🚀 Iniciando teste...\n');
  
  // 1. Verificar se usuário existe
  const userExists = await checkUserExists();
  if (!userExists) {
    console.log('\n❌ TESTE FALHOU: Usuário não existe');
    return;
  }
  
  console.log(''); // Nova linha
  
  // 2. Testar login
  const loginSuccess = await testLogin();
  
  console.log('\n' + '═'.repeat(50));
  if (loginSuccess) {
    console.log('🎉 TESTE CONCLUÍDO COM SUCESSO!');
    console.log('✅ Usuário existe e login funciona perfeitamente');
    console.log('\n💡 Você pode usar estas credenciais no frontend:');
    console.log(`   🌐 URL: http://localhost:5773`);
    console.log(`   👤 Username: ${TEST_USER.username}`);
    console.log(`   🔑 Password: ${TEST_USER.password}`);
  } else {
    console.log('❌ TESTE FALHOU!');
    console.log('⚠️ Usuário existe mas login não funciona');
  }
  console.log('═'.repeat(50));
}

// Executar teste
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = { runTest, TEST_USER }; 