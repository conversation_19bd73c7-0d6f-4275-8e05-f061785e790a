#!/usr/bin/env node

// 🚀 AUTOMAÇÃO DE SIGN UP VIA API DIRETA
// Executa automaticamente o sign up de todos os usuários de teste via API

const https = require('https');
const http = require('http');

// Lista de usuários para criar
const USERS = [
  { username: 'newuser2024', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'disney_lover', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'pin_collector', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'trading_pro', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'collector_pro', firstName: '<PERSON>', lastName: '<PERSON>', email: 'jam<PERSON>.<EMAIL>', password: 'TestPass123!' },
  { username: 'teste123', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
  { username: 'pintrader2024', firstName: 'David', lastName: 'Brown', email: '<EMAIL>', password: 'TestPass123!' }
];

const API_BASE = 'http://localhost:3001';

console.log('🚀 INICIANDO AUTOMAÇÃO DE SIGN UP VIA API');
console.log('═'.repeat(80));
console.log(`📝 Criando ${USERS.length} usuários automaticamente...`);
console.log(`🌐 API: ${API_BASE}`);
console.log('═'.repeat(80));

// Função para fazer requisição HTTP
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const response = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: body ? JSON.parse(body) : null
          };
          resolve(response);
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: null
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Verificar se username está disponível
async function checkUsername(username) {
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/auth/username/check',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  try {
    const response = await makeRequest(options, { username });
    return response.data;
  } catch (error) {
    console.log(`❌ Erro ao verificar username ${username}:`, error.message);
    return null;
  }
}

// Criar usuário via API
async function createUserViaAPI(user) {
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/auth/signup',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  try {
    const response = await makeRequest(options, user);
    return response;
  } catch (error) {
    console.log(`❌ Erro ao criar usuário ${user.username}:`, error.message);
    return { statusCode: 500, error: error.message };
  }
}

// Criar um usuário
async function createUser(user, index, total) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🔨 CRIANDO USUÁRIO ${index + 1}/${total}: ${user.username}`);
  console.log(`${'='.repeat(60)}`);
  
  try {
    // 1. Verificar se username está disponível
    console.log('🔍 Verificando disponibilidade do username...');
    const usernameCheck = await checkUsername(user.username);
    
    if (usernameCheck && !usernameCheck.available) {
      console.log(`⚠️ Username ${user.username} já existe - pulando...`);
      return { success: false, username: user.username, error: 'Username já existe', skipped: true };
    }
    
    console.log(`✅ Username ${user.username} disponível`);
    
    // 2. Criar usuário
    console.log('📝 Criando usuário via API...');
    const response = await createUserViaAPI(user);
    
    if (response.statusCode === 200 || response.statusCode === 201) {
      console.log(`✅ ${user.username} criado com sucesso!`);
      return { success: true, username: user.username };
    } else {
      const errorMsg = response.data?.message || response.body || 'Erro desconhecido';
      console.log(`❌ Falha ao criar ${user.username}: ${errorMsg}`);
      return { success: false, username: user.username, error: errorMsg };
    }
    
  } catch (error) {
    console.log(`❌ Erro inesperado: ${error.message}`);
    return { success: false, username: user.username, error: error.message };
  }
}

// Verificar se API está respondendo
async function checkAPI() {
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/utility/health',
    method: 'GET'
  };
  
  try {
    const response = await makeRequest(options);
    return response.statusCode === 200;
  } catch (error) {
    return false;
  }
}

async function runAutomation() {
  const results = {
    successful: [],
    failed: [],
    skipped: [],
    startTime: new Date()
  };
  
  // Verificar API
  console.log('🔍 Verificando API...');
  const apiOk = await checkAPI();
  if (!apiOk) {
    console.log('❌ API não está respondendo. Verifique se o backend está rodando.');
    return results;
  }
  console.log('✅ API respondendo!');
  
  // Criar cada usuário
  for (let i = 0; i < USERS.length; i++) {
    const user = USERS[i];
    const result = await createUser(user, i, USERS.length);
    
    if (result.success) {
      results.successful.push(result.username);
    } else if (result.skipped) {
      results.skipped.push(result.username);
    } else {
      results.failed.push({ username: result.username, error: result.error });
    }
    
    // Pausa entre usuários
    if (i < USERS.length - 1) {
      console.log('⏸️ Pausando antes do próximo usuário...');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  // Relatório final
  const endTime = new Date();
  const duration = Math.round((endTime - results.startTime) / 1000);
  
  console.log(`\n${'='.repeat(80)}`);
  console.log('📊 RELATÓRIO FINAL');
  console.log(`${'='.repeat(80)}`);
  
  console.log(`\n⏰ Tempo total: ${Math.floor(duration / 60)}m ${duration % 60}s`);
  console.log(`🎯 Resultado: ${results.successful.length}/${USERS.length} usuários criados`);
  
  if (results.successful.length > 0) {
    console.log(`\n✅ CRIADOS COM SUCESSO (${results.successful.length}):`);
    results.successful.forEach(username => {
      console.log(`   👤 ${username}`);
    });
  }
  
  if (results.skipped.length > 0) {
    console.log(`\n⏭️ PULADOS - JÁ EXISTIAM (${results.skipped.length}):`);
    results.skipped.forEach(username => {
      console.log(`   👤 ${username}`);
    });
  }
  
  if (results.failed.length > 0) {
    console.log(`\n❌ FALHARAM (${results.failed.length}):`);
    results.failed.forEach(failure => {
      console.log(`   👤 ${failure.username}: ${failure.error}`);
    });
  }
  
  console.log(`\n🏁 Automação concluída!`);
  console.log(`📝 Usuários criados estão prontos para login com senha: TestPass123!`);
  
  return results;
}

// Executar se chamado diretamente
if (require.main === module) {
  runAutomation().catch(console.error);
}

module.exports = { runAutomation, USERS }; 