# FCM REST API v1 Setup Guide

## 📚 Documentação Oficial
https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages

## 🔑 1. Configurar Service Account

### Passo 1: Criar Service Account
1. Vá para: https://console.firebase.google.com/project/iconpal-cf925/settings/serviceaccounts/adminsdk
2. Clique em "Generate new private key"
3. Baixe o arquivo JSON (ex: `iconpal-cf925-firebase-adminsdk-xxxxx.json`)
4. Salve como `firebase-service-account.json` na raiz do projeto

### Passo 2: Configurar Permissões
O Service Account precisa da role: **Firebase Cloud Messaging Admin**

### Passo 3: Adicionar ao .env
```env
FIREBASE_SERVICE_ACCOUNT_PATH=./firebase-service-account.json
```

## 🚀 2. Endpoint da API v1

### URL Base:
```
https://fcm.googleapis.com/v1/projects/iconpal-cf925/messages:send
```

### Headers Necessários:
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

## 📱 3. Formato da Mensagem

### Estrutura Básica:
```json
{
  "message": {
    "token": "FCM_REGISTRATION_TOKEN",
    "notification": {
      "title": "Título da Notificação",
      "body": "Corpo da mensagem"
    },
    "data": {
      "key1": "value1",
      "key2": "value2"
    },
    "webpush": {
      "headers": {
        "TTL": "86400"
      },
      "notification": {
        "icon": "/icon.png",
        "badge": "/badge.png",
        "actions": [
          {
            "action": "view",
            "title": "Ver"
          }
        ]
      }
    }
  }
}
```

## 🔐 4. Autenticação OAuth 2.0

### Scopes Necessários:
```
https://www.googleapis.com/auth/firebase.messaging
```

### Gerar Access Token:
```bash
# Usando Google Auth Library
gcloud auth application-default print-access-token
```

## 📋 5. Próximos Passos

1. ✅ Baixar Service Account JSON
2. ✅ Configurar autenticação
3. ✅ Implementar envio de mensagens
4. ✅ Testar com token FCM real
