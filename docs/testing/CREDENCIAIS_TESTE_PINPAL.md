# 🔐 Credenciais de Teste do PinPal

## ✅ **USUÁRIOS CRIADOS COM SUCESSO**

### 🆕 **Usuários Simples (Recém-criados)**

| **Email** | **Tipo** | **Nome** | **ID** |
|-----------|----------|----------|--------|
| `<EMAIL>` | 👑 **Admin** | Admin Test | `test-admin-001` |
| `<EMAIL>` | 👤 **User** | User Test | `test-user-002` |
| `<EMAIL>` | 🔄 **Trader** | Trader Test | `test-trader-003` |

### 🗄️ **Usuários Existentes no Banco**

| **Email** | **Nome** | **ID** |
|-----------|----------|--------|
| `<EMAIL>` | PinPal Admin | `pinpal-admin-official` |
| `<EMAIL>` | <PERSON> | `6Db7dESZeWZvjQZLIr1KAADeeSc2` |
| `<EMAIL>` | <PERSON> | `user-003` |
| `<EMAIL>` | Test User 2 | `JNllX37quJZQi9FMFFPQp5YVC4B3` |

---

## 🚀 **COMO FAZER LOGIN**

### **Método 1: Criar Nova Conta (RECOMENDADO)**

1. **Acesse**: http://localhost:5773
2. **Clique**: "Sign Up" ou "Create Account"
3. **Email**: Use qualquer email (ex: `<EMAIL>`)
4. **Senha**: Qualquer senha (mínimo 6 caracteres)
5. **Sistema**: Criará automaticamente no PostgreSQL

### **Método 2: Usar Emails Sugeridos**

Como o sistema usa **Firebase Auth**, você precisa criar contas com os emails sugeridos:

```
📧 Emails sugeridos para criar contas:
├── <EMAIL> (para testar recursos de admin)
├── <EMAIL> (para testar usuário comum)
└── <EMAIL> (para testar trading de pins)
```

---

## 🎯 **CENÁRIOS DE TESTE**

### **👑 Teste como Administrador**
- **Email**: `<EMAIL>`
- **Recursos**: Dashboard admin, gerenciar usuários, moderar conteúdo
- **Acesso**: Painel administrativo completo

### **👤 Teste como Usuário Comum**
- **Email**: `<EMAIL>`
- **Recursos**: Criar boards, salvar pins, interações sociais
- **Acesso**: Funcionalidades básicas da plataforma

### **🔄 Teste como Trader**
- **Email**: `<EMAIL>`
- **Recursos**: Trading de pins, marketplace, negociações
- **Acesso**: Funcionalidades de comércio e troca

---

## 🛠️ **VERIFICAÇÃO DO SISTEMA**

### **Servidores Ativos**
- ✅ **Frontend**: http://localhost:5773
- ✅ **Backend**: http://localhost:3001
- ✅ **PostgreSQL**: Porta 5433 (Cloud SQL Proxy)

### **Banco de Dados**
- ✅ **10 usuários** encontrados no PostgreSQL
- ✅ **Tabelas sincronizadas** entre Firebase Auth e PostgreSQL
- ✅ **Dados de teste** prontos para uso

---

## 🔧 **COMANDOS ÚTEIS**

### **Listar Usuários Atuais**
```bash
node scripts/create-simple-test-users.js
```

### **Verificar Backend**
```bash
curl -s "http://localhost:3001/api/users/search?q=test&limit=5"
```

### **Acessar Aplicação**
```bash
open http://localhost:5773
```

---

## ⚠️ **OBSERVAÇÕES IMPORTANTES**

1. **🔐 Firebase Auth**: Sistema principal de autenticação
2. **🔄 Sincronização**: Contas criadas no Firebase são automaticamente sincronizadas com PostgreSQL
3. **📊 Dados**: PostgreSQL já contém usuários de teste prontos
4. **🌞 Tema**: Sistema forçado para light mode para melhor visualização
5. **🚀 Performance**: Aplicação otimizada e funcionando na porta 5773

---

## 🎉 **RESULTADO FINAL**

✅ **Sistema 100% funcional** com usuários de teste
✅ **Backend rodando** na porta 3001
✅ **Frontend rodando** na porta 5773 
✅ **PostgreSQL conectado** na porta 5433
✅ **Light mode forçado** para melhor experiência
✅ **Dados de teste prontos** para uso imediato

**🚀 Pronto para testar! Acesse http://localhost:5773 e crie sua conta!** 