# 🚀 Automação de Sign Up no Navegador - PinPal

Este guia explica como usar os scripts de automação para criar usuários de teste diretamente no navegador, sem precisar fazer sign up manual.

## 📋 Usuários que Serão Criados

Os scripts criarão automaticamente 7 usuários de teste:

| Username | Nome | Email | Senha |
|----------|------|-------|-------|
| `newuser2024` | <PERSON> | <EMAIL> | TestPass123! |
| `disney_lover` | <PERSON> | <EMAIL> | TestPass123! |
| `pin_collector` | <PERSON> | <EMAIL> | TestPass123! |
| `trading_pro` | <PERSON> | <EMAIL> | TestPass123! |
| `collector_pro` | <PERSON> | <EMAIL> | TestPass123! |
| `teste123` | <PERSON> | <EMAIL> | TestPass123! |
| `pintrader2024` | <PERSON> | <EMAIL> | TestPass123! |

**Senha para todos**: `TestPass123!`

## 🎯 Método 1: Script no Console (Recomendado)

### Passo a Passo:

1. **Abra o PinPal** no navegador: `http://localhost:5773`

2. **Abra o Console do Navegador**:
   - Chrome/Edge: `F12` → aba "Console"
   - Firefox: `F12` → aba "Console"
   - Safari: `Cmd+Option+I` → aba "Console"

3. **Cole o Script**:
   ```javascript
   // Cole todo o conteúdo do arquivo: scripts/pinpal-browser-signup.js
   ```

4. **Execute a Automação**:
   ```javascript
   PinPal.createAll()
   ```

5. **Aguarde**: O script criará todos os usuários automaticamente (cerca de 5-10 minutos)

### Comandos Disponíveis:

```javascript
// Criar todos os usuários
PinPal.createAll()

// Criar usuário específico
PinPal.createUser("newuser2024")

// Ver lista de usuários disponíveis
PinPal.users
```

## 🔖 Método 2: Bookmarklet (Mais Rápido)

### Como Criar o Bookmarklet:

1. **Copie o código** do arquivo `scripts/pinpal-bookmarklet.js`

2. **Crie um novo favorito** no navegador:
   - Nome: `PinPal Auto Signup`
   - URL: Cole o código completo (começando com `javascript:`)

3. **Use o Bookmarklet**:
   - Vá para `http://localhost:5773`
   - Clique no favorito "PinPal Auto Signup"
   - Confirme quando perguntado
   - Aguarde a automação completar

## ⚙️ Como Funciona

### Fluxo do Script:

1. **Navegação**: Vai para `/signup` automaticamente
2. **Preenchimento**: Preenche todos os campos do formulário
3. **Submissão**: Clica no botão de submit
4. **Verificação**: Aguarda sucesso ou detecta erros
5. **Logout**: Limpa sessão para próximo usuário
6. **Repetição**: Repete para todos os usuários

### Seletores Utilizados:

```javascript
// Campos do formulário
input[name="firstName"]
input[name="lastName"] 
input[name="username"]
input[name="email"]
input[name="password"]

// Botão de submit
button[type="submit"]

// Detecção de erros
.error-message, .text-red-500, [role="alert"]
```

## 🐛 Solução de Problemas

### Script Não Encontra Campos:

```javascript
// Debug: verificar se campos existem
document.querySelector('input[name="firstName"]')
document.querySelector('input[name="username"]')
document.querySelector('button[type="submit"]')
```

### Erros Durante Execução:

1. **"Campo não encontrado"**: Verifique se está na página de signup
2. **"Botão submit não encontrado"**: Aguarde página carregar completamente
3. **"Timeout"**: Pode haver erro de rede, verifique backend

### Verificar Resultados:

```javascript
// Ver usuários criados no console
console.log('Usuários criados com sucesso')

// Verificar no backend
fetch('/api/users/check-username/newuser2024')
  .then(r => r.json())
  .then(console.log)
```

## 📊 Monitoramento

### Logs no Console:

```
🚀 AUTOMAÇÃO PINPAL INICIADA
═══════════════════════════════════════════════════════

🔨 Criando: newuser2024
────────────────────────────────────────────────────────
📍 Navegando para /signup...
📝 Preenchendo formulário...
✅ input[name="firstName"]: Alex
✅ input[name="lastName"]: Silva
✅ input[name="username"]: newuser2024
✅ input[name="email"]: <EMAIL>
✅ input[name="password"]: TestPass123!
🚀 Submetendo...
✅ Clicado: button[type="submit"]
⏳ Aguardando resultado...
✅ newuser2024 criado com sucesso!

[... repete para cada usuário ...]

═══════════════════════════════════════════════════════
📊 RELATÓRIO FINAL
═══════════════════════════════════════════════════════

✅ SUCESSO (7):
   👤 newuser2024
   👤 disney_lover
   👤 pin_collector
   👤 trading_pro
   👤 collector_pro
   👤 teste123
   👤 pintrader2024

🎯 TOTAL: 7/7 criados
```

## 🔄 Executar Novamente

Se precisar executar novamente:

1. **Limpe os dados** primeiro (opcional):
   ```bash
   # No backend, limpar usuários de teste
   npm run clean-test-users
   ```

2. **Execute o script** novamente
3. **Verifique** se não há conflitos de username

## 📝 Customização

### Adicionar Novos Usuários:

```javascript
// Edite a constante PINPAL_USERS no script
const PINPAL_USERS = [
  // ... usuários existentes ...
  {
    username: 'novo_usuario',
    firstName: 'Novo',
    lastName: 'Usuario',
    email: '<EMAIL>',
    password: 'TestPass123!'
  }
];
```

### Alterar Configurações:

```javascript
// Tempo de espera entre ações
await wait(500); // 500ms

// Timeout para aguardar resultado
for (let i = 0; i < 30; i++) // 30 segundos
```

## ✅ Verificação Final

Após a automação, verifique:

1. **Console**: Relatório final mostra sucessos/falhas
2. **Backend**: Usuários criados no PostgreSQL
3. **Frontend**: Pode fazer login com qualquer usuário criado

### Teste Rápido:

```javascript
// Verificar se usuário existe
fetch('/api/auth/username/check', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username: 'newuser2024' })
})
.then(r => r.json())
.then(console.log); // Deve retornar { available: false }
```

## 🎯 Resultado Esperado

Após execução bem-sucedida:
- ✅ 7 usuários criados automaticamente
- ✅ Todos com dados válidos e senhas funcionais
- ✅ Prontos para login e testes
- ✅ Dados persistidos no PostgreSQL
- ✅ Compatíveis com Firebase Auth

**Tempo total estimado**: 5-10 minutos para todos os usuários. 