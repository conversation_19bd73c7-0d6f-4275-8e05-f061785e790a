<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple FCM Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 18px;
        }
        button:hover { background: #45a049; }
        .result {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #51cf66; }
        .error { color: #ff6b6b; }
    </style>
</head>
<body>
    <h1>🔥 Simple FCM Token Test</h1>
    
    <button onclick="testFCM()">🚀 Test FCM Token Generation</button>
    
    <div id="result" class="result">Click the button to test...</div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>

    <script>
        const result = document.getElementById('result');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            result.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }

        async function testFCM() {
            result.innerHTML = 'Starting FCM test...\n';
            
            try {
                // Check permission
                log('🔔 Checking notification permission...');
                if (Notification.permission !== 'granted') {
                    log('📋 Requesting permission...');
                    const permission = await Notification.requestPermission();
                    if (permission !== 'granted') {
                        log('❌ Permission denied', 'error');
                        return;
                    }
                }
                log('✅ Permission granted', 'success');

                // Firebase config
                const firebaseConfig = {
                    apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
                    authDomain: "iconpal-cf925.firebaseapp.com",
                    projectId: "iconpal-cf925",
                    storageBucket: "iconpal-cf925.appspot.com",
                    messagingSenderId: "887109976546",
                    appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
                };

                const vapidKey = "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";

                // Initialize Firebase
                log('🔥 Initializing Firebase...');
                const app = firebase.initializeApp(firebaseConfig);
                const messaging = firebase.messaging();
                log('✅ Firebase initialized', 'success');

                // Get token with timeout
                log('🎫 Getting FCM token...');
                log('🔑 Using VAPID key...');
                
                const tokenPromise = messaging.getToken({ vapidKey: vapidKey });
                const timeoutPromise = new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Timeout after 10 seconds')), 10000)
                );

                const token = await Promise.race([tokenPromise, timeoutPromise]);

                if (token) {
                    log('🎉 SUCCESS! FCM Token generated:', 'success');
                    log(`📋 Token: ${token.substring(0, 50)}...`, 'success');
                    log(`📏 Length: ${token.length} characters`, 'success');
                    
                    // Copy to clipboard
                    try {
                        await navigator.clipboard.writeText(token);
                        log('📋 Token copied to clipboard!', 'success');
                    } catch (e) {
                        log('⚠️ Could not copy to clipboard');
                    }
                } else {
                    log('❌ No token received', 'error');
                }

            } catch (error) {
                log(`❌ ERROR: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }
    </script>
</body>
</html>
