<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Firebase Connection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover { background: #45a049; }
        .result {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #51cf66; }
        .error { color: #ff6b6b; }
        .warning { color: #ffd43b; }
        .info { color: #74c0fc; }
    </style>
</head>
<body>
    <h1>🔍 Firebase Connection Diagnostics</h1>
    
    <div>
        <button onclick="testFirebaseAPI()">1. Test Firebase API</button>
        <button onclick="testProjectConfig()">2. Test Project Config</button>
        <button onclick="testAlternativeMethod()">3. Alternative Method</button>
        <button onclick="clearResults()">Clear</button>
    </div>
    
    <div id="results" class="result">Click buttons to run diagnostics...</div>

    <script>
        const results = document.getElementById('results');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            results.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }

        function clearResults() {
            results.innerHTML = 'Results cleared...\n';
        }

        async function testFirebaseAPI() {
            log('🔍 Testing Firebase API connectivity...', 'info');
            
            try {
                // Test Firebase CDN
                log('📡 Testing Firebase CDN...', 'info');
                const cdnResponse = await fetch('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js');
                if (cdnResponse.ok) {
                    log('✅ Firebase CDN accessible', 'success');
                } else {
                    log(`❌ Firebase CDN error: ${cdnResponse.status}`, 'error');
                }

                // Test Firebase project endpoint
                log('📡 Testing Firebase project endpoint...', 'info');
                const projectUrl = 'https://iconpal-cf925-default-rtdb.firebaseio.com/.json';
                const projectResponse = await fetch(projectUrl);
                log(`📋 Project endpoint status: ${projectResponse.status}`, projectResponse.ok ? 'success' : 'warning');

                // Test FCM endpoint
                log('📡 Testing FCM endpoint...', 'info');
                const fcmUrl = 'https://fcm.googleapis.com/fcm/send';
                const fcmResponse = await fetch(fcmUrl, { method: 'HEAD' });
                log(`📋 FCM endpoint status: ${fcmResponse.status}`, 'info');

            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function testProjectConfig() {
            log('🔧 Testing project configuration...', 'info');
            
            const config = {
                apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
                authDomain: "iconpal-cf925.firebaseapp.com",
                projectId: "iconpal-cf925",
                storageBucket: "iconpal-cf925.appspot.com",
                messagingSenderId: "887109976546",
                appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
            };

            // Validate config
            log('📋 Validating configuration...', 'info');
            for (const [key, value] of Object.entries(config)) {
                if (!value || value.includes('XXX')) {
                    log(`❌ Invalid ${key}: ${value}`, 'error');
                } else {
                    log(`✅ ${key}: OK`, 'success');
                }
            }

            // Test auth domain
            try {
                log('📡 Testing auth domain...', 'info');
                const authResponse = await fetch(`https://${config.authDomain}`);
                log(`📋 Auth domain status: ${authResponse.status}`, authResponse.ok ? 'success' : 'warning');
            } catch (error) {
                log(`❌ Auth domain error: ${error.message}`, 'error');
            }
        }

        async function testAlternativeMethod() {
            log('🚀 Testing alternative FCM method...', 'info');
            
            try {
                // Load Firebase dynamically
                log('📦 Loading Firebase SDK...', 'info');
                
                if (typeof firebase === 'undefined') {
                    log('❌ Firebase SDK not loaded', 'error');
                    return;
                }

                const config = {
                    apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
                    authDomain: "iconpal-cf925.firebaseapp.com",
                    projectId: "iconpal-cf925",
                    storageBucket: "iconpal-cf925.appspot.com",
                    messagingSenderId: "887109976546",
                    appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
                };

                // Initialize without messaging first
                log('🔥 Initializing Firebase app only...', 'info');
                const app = firebase.initializeApp(config, 'test-app-' + Date.now());
                log('✅ Firebase app initialized', 'success');

                // Check if messaging is supported
                log('🔍 Checking messaging support...', 'info');
                if (!firebase.messaging.isSupported()) {
                    log('❌ Firebase Messaging not supported in this browser', 'error');
                    return;
                }
                log('✅ Firebase Messaging supported', 'success');

                // Try to initialize messaging
                log('📱 Initializing messaging...', 'info');
                const messaging = firebase.messaging(app);
                log('✅ Messaging initialized', 'success');

                // Try to get token without VAPID first
                log('🎫 Trying to get token without VAPID...', 'info');
                try {
                    const tokenWithoutVapid = await Promise.race([
                        messaging.getToken(),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))
                    ]);
                    
                    if (tokenWithoutVapid) {
                        log('🎉 SUCCESS! Token without VAPID: ' + tokenWithoutVapid.substring(0, 30) + '...', 'success');
                    }
                } catch (error) {
                    log(`⚠️ Token without VAPID failed: ${error.message}`, 'warning');
                }

                // Now try with VAPID
                log('🔑 Trying with VAPID key...', 'info');
                const vapidKey = "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";
                
                try {
                    const tokenWithVapid = await Promise.race([
                        messaging.getToken({ vapidKey }),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))
                    ]);
                    
                    if (tokenWithVapid) {
                        log('🎉 SUCCESS! Token with VAPID: ' + tokenWithVapid.substring(0, 30) + '...', 'success');
                    }
                } catch (error) {
                    log(`❌ Token with VAPID failed: ${error.message}`, 'error');
                }

            } catch (error) {
                log(`❌ Alternative method error: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }
    </script>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>
</body>
</html>
