<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test with Demo Firebase Project</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 18px;
        }
        button:hover { background: #45a049; }
        .result {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #51cf66; }
        .error { color: #ff6b6b; }
        .warning { color: #ffd43b; }
        .info { color: #74c0fc; }
    </style>
</head>
<body>
    <h1>🧪 Test with Demo Firebase Project</h1>
    
    <p>This test uses Firebase's demo project to verify if FCM token generation works in general.</p>
    
    <button onclick="testWithDemoProject()">🚀 Test with Demo Project</button>
    <button onclick="testCreateNewProject()">🆕 Guide to Create New Project</button>
    
    <div id="result" class="result">Click button to test...</div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>

    <script>
        const result = document.getElementById('result');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            result.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }

        async function testWithDemoProject() {
            result.innerHTML = 'Testing with demo configuration...\n';
            
            try {
                // Check permission first
                log('🔔 Checking notification permission...', 'info');
                if (Notification.permission !== 'granted') {
                    log('📋 Requesting permission...', 'info');
                    const permission = await Notification.requestPermission();
                    if (permission !== 'granted') {
                        log('❌ Permission denied', 'error');
                        return;
                    }
                }
                log('✅ Permission granted', 'success');

                // Use a demo/test Firebase configuration
                // Note: This is a public demo config - replace with your real project
                const demoConfig = {
                    apiKey: "AIzaSyBdVl-cTICSwYKrjn57-6X-w0urjNMzSz4",
                    authDomain: "fir-demo-project.firebaseapp.com",
                    projectId: "fir-demo-project",
                    storageBucket: "fir-demo-project.appspot.com",
                    messagingSenderId: "448618578101",
                    appId: "1:448618578101:web:0b650a0a4db33c63"
                };

                log('🔥 Testing with demo Firebase project...', 'info');
                log('📋 Project ID: fir-demo-project', 'info');

                // Initialize Firebase
                const app = firebase.initializeApp(demoConfig, 'demo-test-' + Date.now());
                log('✅ Firebase app initialized', 'success');

                // Check messaging support
                if (!firebase.messaging.isSupported()) {
                    log('❌ Firebase Messaging not supported', 'error');
                    return;
                }

                const messaging = firebase.messaging(app);
                log('✅ Firebase Messaging initialized', 'success');

                // Try to get token (without VAPID for demo)
                log('🎫 Attempting to get FCM token...', 'info');
                
                const tokenPromise = messaging.getToken();
                const timeoutPromise = new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Timeout after 8 seconds')), 8000)
                );

                const token = await Promise.race([tokenPromise, timeoutPromise]);

                if (token) {
                    log('🎉 SUCCESS! Demo project works!', 'success');
                    log(`📋 Demo token: ${token.substring(0, 40)}...`, 'success');
                    log('', 'info');
                    log('✅ This proves FCM token generation works in your browser!', 'success');
                    log('❌ The issue is with your project configuration.', 'error');
                    log('', 'info');
                    log('🔧 Next steps:', 'info');
                    log('1. Create a new Firebase project', 'info');
                    log('2. Enable Cloud Messaging', 'info');
                    log('3. Generate new VAPID keys', 'info');
                    log('4. Update your project configuration', 'info');
                } else {
                    log('❌ No token received from demo project', 'error');
                }

            } catch (error) {
                log(`❌ Demo test failed: ${error.message}`, 'error');
                
                if (error.message.includes('Timeout')) {
                    log('', 'info');
                    log('🔍 Timeout suggests network/firewall issues:', 'warning');
                    log('- Corporate firewall blocking FCM?', 'warning');
                    log('- VPN interfering?', 'warning');
                    log('- Browser extensions blocking?', 'warning');
                } else {
                    log('', 'info');
                    log('🔍 Other possible issues:', 'warning');
                    log('- Browser compatibility', 'warning');
                    log('- Security settings', 'warning');
                    log('- Network restrictions', 'warning');
                }
            }
        }

        function testCreateNewProject() {
            result.innerHTML = '';
            log('🆕 Guide to Create New Firebase Project:', 'info');
            log('', 'info');
            log('1. Go to: https://console.firebase.google.com/', 'info');
            log('2. Click "Add project" or "Create a project"', 'info');
            log('3. Enter project name: "pinpal-project"', 'info');
            log('4. Choose whether to enable Google Analytics', 'info');
            log('5. Click "Create project"', 'info');
            log('', 'info');
            log('6. After creation, go to Project Settings', 'info');
            log('7. Scroll down to "Your apps" section', 'info');
            log('8. Click "Add app" → Web app icon', 'info');
            log('9. Enter app nickname: "PinPal Web"', 'info');
            log('10. Copy the configuration object', 'info');
            log('', 'info');
            log('11. Go to Cloud Messaging tab', 'info');
            log('12. Generate Web Push certificates (VAPID key)', 'info');
            log('', 'info');
            log('🔧 Then update your .env file with the new config!', 'success');
            log('', 'info');
            log('📋 Would you like me to help you create a new project?', 'info');
        }
    </script>
</body>
</html>
