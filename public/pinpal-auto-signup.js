// 🎯 PINPAL AUTO SIGNUP - Acesse via: http://localhost:5773/pinpal-auto-signup.js
// Cole este script no console do navegador ou use como script externo

(function() {
  'use strict';
  
  // 📋 Usuários para criar automaticamente
  const USERS = [
    { username: 'newuser2024', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
    { username: 'disney_lover', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
    { username: 'pin_collector', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
    { username: 'trading_pro', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
    { username: 'collector_pro', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
    { username: 'teste123', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', password: 'TestPass123!' },
    { username: 'pintrader2024', firstName: 'David', lastName: 'Brown', email: '<EMAIL>', password: 'TestPass123!' }
  ];
  
  console.log('🎯 PinPal Auto Signup Loaded!');
  console.log(`📝 Ready to create ${USERS.length} test users`);
  console.log('🚀 Run: startAutoSignup() to begin');
  
  // Utilitários
  const sleep = ms => new Promise(resolve => setTimeout(resolve, ms));
  
  function logStep(message, emoji = '📝') {
    console.log(`${emoji} ${message}`);
  }
  
  function logSuccess(message) {
    console.log(`✅ ${message}`);
  }
  
  function logError(message) {
    console.log(`❌ ${message}`);
  }
  
  // Função para preencher campo
  function fillField(selector, value, fieldName) {
    const input = document.querySelector(selector);
    if (input) {
      input.focus();
      input.value = value;
      
      // Disparar eventos para React
      const inputEvent = new Event('input', { bubbles: true });
      const changeEvent = new Event('change', { bubbles: true });
      
      input.dispatchEvent(inputEvent);
      input.dispatchEvent(changeEvent);
      input.blur();
      
      logSuccess(`${fieldName}: ${value}`);
      return true;
    } else {
      logError(`Campo ${fieldName} não encontrado (${selector})`);
      return false;
    }
  }
  
  // Função para clicar
  function clickButton(selector, buttonName) {
    const button = document.querySelector(selector);
    if (button) {
      button.click();
      logSuccess(`Clicado: ${buttonName}`);
      return true;
    } else {
      logError(`Botão ${buttonName} não encontrado (${selector})`);
      return false;
    }
  }
  
  // Navegar para signup
  async function goToSignup() {
    if (!window.location.pathname.includes('signup')) {
      logStep('Navegando para página de signup...');
      window.location.href = '/signup';
      await sleep(3000);
    }
    logStep('Na página de signup');
  }
  
  // Preencher formulário
  async function fillForm(user) {
    logStep(`Preenchendo dados para: ${user.username}`, '📝');
    
    await sleep(1000); // Aguardar página carregar
    
    const fields = [
      { selector: 'input[name="firstName"]', value: user.firstName, name: 'First Name' },
      { selector: 'input[name="lastName"]', value: user.lastName, name: 'Last Name' },
      { selector: 'input[name="username"]', value: user.username, name: 'Username' },
      { selector: 'input[name="email"]', value: user.email, name: 'Email' },
      { selector: 'input[name="password"]', value: user.password, name: 'Password' }
    ];
    
    for (const field of fields) {
      fillField(field.selector, field.value, field.name);
      await sleep(300);
    }
    
    logStep('Formulário preenchido!', '📋');
  }
  
  // Submeter formulário
  async function submitForm() {
    logStep('Submetendo formulário...', '🚀');
    
    const submitSelectors = [
      'button[type="submit"]',
      'button:contains("Sign Up")',
      'button:contains("Create Account")',
      '.submit-button'
    ];
    
    for (const selector of submitSelectors) {
      if (clickButton(selector, 'Submit')) {
        return true;
      }
    }
    
    // Fallback: tentar submeter via form
    const form = document.querySelector('form');
    if (form) {
      form.dispatchEvent(new Event('submit', { bubbles: true }));
      logSuccess('Formulário submetido via evento');
      return true;
    }
    
    logError('Não foi possível submeter o formulário');
    return false;
  }
  
  // Aguardar resultado
  async function waitForResult() {
    logStep('Aguardando resultado...', '⏳');
    
    for (let i = 0; i < 30; i++) {
      await sleep(1000);
      
      // Sucesso: saiu da página de signup
      if (!window.location.pathname.includes('signup')) {
        logSuccess('Usuário criado com sucesso!');
        return { success: true };
      }
      
      // Erro: procurar mensagens de erro
      const errorSelectors = [
        '.error-message',
        '.alert-error',
        '[role="alert"]',
        '.text-red-500',
        '.text-red-600',
        '.text-red-400'
      ];
      
      for (const selector of errorSelectors) {
        const errorElement = document.querySelector(selector);
        if (errorElement && errorElement.textContent.trim()) {
          const errorText = errorElement.textContent.trim();
          logError(`Erro encontrado: ${errorText}`);
          return { success: false, error: errorText };
        }
      }
    }
    
    logError('Timeout - resultado indeterminado');
    return { success: false, error: 'Timeout' };
  }
  
  // Fazer logout
  async function logout() {
    logStep('Fazendo logout...', '🚪');
    
    // Limpar dados
    localStorage.clear();
    sessionStorage.clear();
    
    // Voltar para home
    window.location.href = '/';
    await sleep(3000);
    
    logStep('Logout concluído');
  }
  
  // Criar um usuário
  async function createUser(user, index, total) {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`🔨 CRIANDO USUÁRIO ${index + 1}/${total}: ${user.username}`);
    console.log(`${'='.repeat(60)}`);
    
    try {
      // 1. Ir para signup
      await goToSignup();
      
      // 2. Preencher formulário
      await fillForm(user);
      
      // 3. Submeter
      const submitted = await submitForm();
      if (!submitted) {
        return { success: false, username: user.username, error: 'Falha ao submeter' };
      }
      
      // 4. Aguardar resultado
      const result = await waitForResult();
      
      if (result.success) {
        logSuccess(`✨ ${user.username} criado com sucesso!`);
        
        // 5. Logout para próximo usuário
        await logout();
        
        return { success: true, username: user.username };
      } else {
        logError(`💥 Falha ao criar ${user.username}: ${result.error}`);
        return { success: false, username: user.username, error: result.error };
      }
      
    } catch (error) {
      logError(`💥 Erro inesperado: ${error.message}`);
      return { success: false, username: user.username, error: error.message };
    }
  }
  
  // Função principal
  async function startAutoSignup() {
    console.clear();
    console.log('🚀 PINPAL AUTO SIGNUP INICIADO');
    console.log(`${'='.repeat(80)}`);
    console.log(`📝 Criando ${USERS.length} usuários automaticamente...`);
    console.log(`⏰ Tempo estimado: ${Math.ceil(USERS.length * 1.5)} minutos`);
    console.log(`${'='.repeat(80)}\n`);
    
    const results = {
      successful: [],
      failed: [],
      startTime: new Date()
    };
    
    // Criar cada usuário
    for (let i = 0; i < USERS.length; i++) {
      const user = USERS[i];
      const result = await createUser(user, i, USERS.length);
      
      if (result.success) {
        results.successful.push(result.username);
      } else {
        results.failed.push({ username: result.username, error: result.error });
      }
      
      // Pausa entre usuários
      if (i < USERS.length - 1) {
        logStep('Pausando antes do próximo usuário...', '⏸️');
        await sleep(2000);
      }
    }
    
    // Relatório final
    const endTime = new Date();
    const duration = Math.round((endTime - results.startTime) / 1000);
    
    console.log(`\n${'='.repeat(80)}`);
    console.log('📊 RELATÓRIO FINAL');
    console.log(`${'='.repeat(80)}`);
    
    console.log(`\n⏰ Tempo total: ${Math.floor(duration / 60)}m ${duration % 60}s`);
    console.log(`🎯 Resultado: ${results.successful.length}/${USERS.length} usuários criados`);
    
    if (results.successful.length > 0) {
      console.log(`\n✅ CRIADOS COM SUCESSO (${results.successful.length}):`);
      results.successful.forEach(username => {
        console.log(`   👤 ${username}`);
      });
    }
    
    if (results.failed.length > 0) {
      console.log(`\n❌ FALHARAM (${results.failed.length}):`);
      results.failed.forEach(failure => {
        console.log(`   👤 ${failure.username}: ${failure.error}`);
      });
    }
    
    console.log(`\n🏁 Automação concluída!`);
    console.log(`📝 Usuários criados estão prontos para login com senha: TestPass123!`);
    
    return results;
  }
  
  // Criar usuário específico
  function createSpecificUser(username) {
    const user = USERS.find(u => u.username === username);
    if (user) {
      console.log(`🎯 Criando usuário específico: ${username}`);
      return createUser(user, 0, 1);
    } else {
      logError(`Usuário '${username}' não encontrado`);
      console.log('Usuários disponíveis:', USERS.map(u => u.username).join(', '));
    }
  }
  
  // Expor funções globalmente
  window.startAutoSignup = startAutoSignup;
  window.createSpecificUser = createSpecificUser;
  window.listUsers = () => {
    console.log('📋 Usuários disponíveis:');
    USERS.forEach((user, index) => {
      console.log(`${index + 1}. ${user.username} (${user.firstName} ${user.lastName})`);
    });
  };
  
  // Mostrar instruções
  console.log('\n📋 COMANDOS DISPONÍVEIS:');
  console.log('• startAutoSignup() - Criar todos os usuários');
  console.log('• createSpecificUser("username") - Criar usuário específico');
  console.log('• listUsers() - Listar usuários disponíveis');
  console.log('\n🚀 PARA COMEÇAR: startAutoSignup()');
  
})(); 