// Simple Service Worker for FCM Token Generation
// This is a minimal service worker just to enable FCM token generation

console.log('Simple Service Worker loaded');

// Basic service worker installation
self.addEventListener('install', function(event) {
  console.log('Service Worker installing...');
  self.skipWaiting();
});

self.addEventListener('activate', function(event) {
  console.log('Service Worker activating...');
  event.waitUntil(self.clients.claim());
});

// Basic message handling
self.addEventListener('message', function(event) {
  console.log('Service Worker received message:', event.data);
});

console.log('Simple Service Worker setup complete');
