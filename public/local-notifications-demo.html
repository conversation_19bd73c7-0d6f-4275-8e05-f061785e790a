<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Local Notifications Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 18px;
        }
        button:hover { background: #45a049; }
        .demo-section {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success { color: #51cf66; }
        .error { color: #ff6b6b; }
        .info { color: #74c0fc; }
    </style>
</head>
<body>
    <h1>🔔 Local Notifications Demo</h1>
    
    <div class="demo-section">
        <h2>✅ Working Alternative</h2>
        <p>Since FCM is blocked by your network, here's a working local notification system:</p>
        
        <button onclick="requestPermission()">1. Request Permission</button>
        <button onclick="sendLocalNotification()">2. Send Local Notification</button>
        <button onclick="scheduleNotification()">3. Schedule Notification (5s)</button>
    </div>

    <div class="demo-section">
        <h2>🎯 For Your PinPal Project</h2>
        <p>You can implement notifications using:</p>
        <ul>
            <li><strong>Local Browser Notifications</strong> - Works offline</li>
            <li><strong>WebSocket Real-time Updates</strong> - For live notifications</li>
            <li><strong>Server-Sent Events (SSE)</strong> - For push-like updates</li>
            <li><strong>Polling</strong> - Check for updates periodically</li>
        </ul>
        
        <button onclick="showImplementationGuide()">Show Implementation Guide</button>
    </div>

    <div id="guide" class="demo-section" style="display: none;">
        <h2>🔧 Implementation Guide</h2>
        <div id="guideContent"></div>
    </div>

    <div class="demo-section">
        <h2>🌐 Network Troubleshooting</h2>
        <p>To fix FCM (Firebase Cloud Messaging):</p>
        <ol>
            <li><strong>Try different network</strong> - Use mobile hotspot</li>
            <li><strong>Disable VPN</strong> - If you're using one</li>
            <li><strong>Test incognito mode</strong> - Disable extensions</li>
            <li><strong>Check firewall</strong> - Allow *.googleapis.com</li>
        </ol>
    </div>

    <script>
        async function requestPermission() {
            try {
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    alert('✅ Permission granted! You can now receive notifications.');
                } else {
                    alert('❌ Permission denied. Please enable in browser settings.');
                }
            } catch (error) {
                alert('❌ Error requesting permission: ' + error.message);
            }
        }

        function sendLocalNotification() {
            if (Notification.permission !== 'granted') {
                alert('❌ Please request permission first!');
                return;
            }

            const notification = new Notification('🎉 PinPal Notification', {
                body: 'This is a local notification that works without FCM!',
                icon: '/pinpal-logo-icon.png',
                badge: '/pinpal-logo-icon.png',
                tag: 'pinpal-demo',
                requireInteraction: true,
                actions: [
                    { action: 'view', title: 'View' },
                    { action: 'dismiss', title: 'Dismiss' }
                ]
            });

            notification.onclick = function() {
                console.log('Notification clicked!');
                window.focus();
                notification.close();
            };

            setTimeout(() => {
                notification.close();
            }, 5000);
        }

        function scheduleNotification() {
            if (Notification.permission !== 'granted') {
                alert('❌ Please request permission first!');
                return;
            }

            alert('⏰ Notification scheduled for 5 seconds...');
            
            setTimeout(() => {
                const notification = new Notification('⏰ Scheduled Notification', {
                    body: 'This notification was scheduled 5 seconds ago!',
                    icon: '/pinpal-logo-icon.png',
                    tag: 'pinpal-scheduled'
                });

                notification.onclick = function() {
                    window.focus();
                    notification.close();
                };
            }, 5000);
        }

        function showImplementationGuide() {
            const guide = document.getElementById('guide');
            const content = document.getElementById('guideContent');
            
            content.innerHTML = `
                <h3>🔔 Local Notifications (Immediate)</h3>
                <pre><code>// Simple local notification
const notification = new Notification('Title', {
    body: 'Message',
    icon: '/icon.png',
    tag: 'unique-id'
});

notification.onclick = () => {
    window.focus();
    notification.close();
};</code></pre>

                <h3>🌐 WebSocket Real-time (Recommended)</h3>
                <pre><code>// Real-time notifications via WebSocket
const ws = new WebSocket('ws://localhost:3001');

ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    if (data.type === 'notification') {
        new Notification(data.title, {
            body: data.body,
            icon: data.icon
        });
    }
};</code></pre>

                <h3>📡 Server-Sent Events</h3>
                <pre><code>// Push-like updates with SSE
const eventSource = new EventSource('/api/notifications/stream');

eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    new Notification(data.title, {
        body: data.body
    });
};</code></pre>

                <h3>⏰ Polling (Fallback)</h3>
                <pre><code>// Check for new notifications periodically
setInterval(async () => {
    const response = await fetch('/api/notifications/check');
    const notifications = await response.json();
    
    notifications.forEach(notif => {
        new Notification(notif.title, {
            body: notif.body
        });
    });
}, 30000); // Check every 30 seconds</code></pre>

                <p class="success">💡 <strong>Recommendation:</strong> Use WebSocket for real-time notifications in your PinPal project!</p>
            `;
            
            guide.style.display = 'block';
        }

        // Auto-check permission on load
        window.addEventListener('load', () => {
            const permission = Notification.permission;
            if (permission === 'default') {
                console.log('Notification permission not requested yet');
            } else if (permission === 'granted') {
                console.log('✅ Notification permission already granted');
            } else {
                console.log('❌ Notification permission denied');
            }
        });
    </script>
</body>
</html>
