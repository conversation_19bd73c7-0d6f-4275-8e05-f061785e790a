<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validate Firebase API Key</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 18px;
        }
        button:hover { background: #45a049; }
        .result {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #51cf66; }
        .error { color: #ff6b6b; }
        .warning { color: #ffd43b; }
        .info { color: #74c0fc; }
        .api-key {
            background: #000;
            padding: 10px;
            border-radius: 4px;
            color: #00ff00;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>🔑 Validate Firebase API Key</h1>
    
    <div class="result">
        <h3>Your API Key:</h3>
        <div class="api-key">AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc</div>
    </div>
    
    <button onclick="validateApiKey()">🔍 Validate API Key</button>
    <button onclick="testFirebaseEndpoints()">🌐 Test Firebase Endpoints</button>
    
    <div id="result" class="result">Click button to validate your API key...</div>

    <script>
        const result = document.getElementById('result');
        const apiKey = "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc";
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            result.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }

        async function validateApiKey() {
            result.innerHTML = 'Validating API key...\n';
            
            // Format validation
            log('🔍 Checking API key format...', 'info');
            log(`📋 API Key: ${apiKey}`, 'info');
            log(`📏 Length: ${apiKey.length} characters`, 'info');
            
            if (apiKey.startsWith('AIzaSy')) {
                log('✅ Format: Valid Firebase API key format', 'success');
            } else {
                log('❌ Format: Invalid Firebase API key format', 'error');
                return;
            }

            // Test with Firebase Auth endpoint (most basic test)
            log('', 'info');
            log('🌐 Testing API key with Firebase Auth...', 'info');
            
            try {
                const authUrl = `https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=${apiKey}`;
                const response = await fetch(authUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'testpassword',
                        returnSecureToken: true
                    })
                });

                log(`📡 Auth endpoint response: ${response.status}`, 'info');
                
                if (response.status === 400) {
                    const errorData = await response.json();
                    if (errorData.error && errorData.error.message === 'API key not valid. Please pass a valid API key.') {
                        log('❌ API Key is INVALID', 'error');
                        log('🔧 The API key is not recognized by Firebase', 'error');
                    } else {
                        log('✅ API Key is VALID', 'success');
                        log('📋 Auth endpoint recognizes the key', 'success');
                    }
                } else if (response.status === 200) {
                    log('✅ API Key is VALID', 'success');
                    log('📋 Auth endpoint accepts the key', 'success');
                } else {
                    log(`⚠️ Unexpected response: ${response.status}`, 'warning');
                    const responseText = await response.text();
                    log(`📋 Response: ${responseText.substring(0, 200)}...`, 'info');
                }

            } catch (error) {
                log(`❌ Network error testing API key: ${error.message}`, 'error');
            }

            // Test with Firebase project endpoint
            log('', 'info');
            log('🌐 Testing project-specific endpoint...', 'info');
            
            try {
                const projectUrl = `https://firebase.googleapis.com/v1beta1/projects/iconpal-cf925?key=${apiKey}`;
                const projectResponse = await fetch(projectUrl);
                
                log(`📡 Project endpoint response: ${projectResponse.status}`, 'info');
                
                if (projectResponse.status === 200) {
                    log('✅ Project exists and API key has access', 'success');
                    const projectData = await projectResponse.json();
                    log(`📋 Project name: ${projectData.displayName || 'N/A'}`, 'info');
                } else if (projectResponse.status === 403) {
                    log('⚠️ API key valid but no access to project', 'warning');
                } else if (projectResponse.status === 404) {
                    log('❌ Project not found', 'error');
                } else {
                    log(`⚠️ Project endpoint status: ${projectResponse.status}`, 'warning');
                }

            } catch (error) {
                log(`❌ Error testing project endpoint: ${error.message}`, 'error');
            }
        }

        async function testFirebaseEndpoints() {
            result.innerHTML = 'Testing Firebase service endpoints...\n';
            
            const endpoints = [
                {
                    name: 'Firebase Auth',
                    url: `https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=${apiKey}`,
                    method: 'POST'
                },
                {
                    name: 'Firebase Messaging',
                    url: `https://fcm.googleapis.com/v1/projects/iconpal-cf925/messages:send`,
                    method: 'POST'
                },
                {
                    name: 'Firebase Project',
                    url: `https://firebase.googleapis.com/v1beta1/projects/iconpal-cf925?key=${apiKey}`,
                    method: 'GET'
                }
            ];

            for (const endpoint of endpoints) {
                log(`🔍 Testing ${endpoint.name}...`, 'info');
                
                try {
                    const response = await fetch(endpoint.url, {
                        method: endpoint.method,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: endpoint.method === 'POST' ? JSON.stringify({}) : undefined
                    });

                    log(`📡 ${endpoint.name}: ${response.status}`, 
                        response.status < 400 ? 'success' : 
                        response.status === 401 || response.status === 403 ? 'warning' : 'error');

                    if (response.status === 401) {
                        log('   → API key authentication failed', 'error');
                    } else if (response.status === 403) {
                        log('   → API key valid but insufficient permissions', 'warning');
                    } else if (response.status === 404) {
                        log('   → Resource not found', 'warning');
                    } else if (response.status < 300) {
                        log('   → Endpoint accessible', 'success');
                    }

                } catch (error) {
                    log(`❌ ${endpoint.name}: ${error.message}`, 'error');
                }
            }

            log('', 'info');
            log('🎯 Summary:', 'info');
            log('- If Auth shows 200/400: API key is valid', 'info');
            log('- If Auth shows 401: API key is invalid', 'info');
            log('- If Project shows 404: Project does not exist', 'info');
            log('- If Messaging shows 403: Normal (needs auth token)', 'info');
        }
    </script>
</body>
</html>
