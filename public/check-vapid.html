<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check VAPID Configuration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #065f46; color: #10b981; }
        .error { background: #7f1d1d; color: #f87171; }
        .warning { background: #78350f; color: #fbbf24; }
        .info { background: #1e3a8a; color: #60a5fa; }
        code {
            background: #000;
            color: #00ff00;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>🔑 VAPID Key Configuration Check</h1>
    
    <div class="container">
        <h2>Current Configuration</h2>
        <div id="status">Checking...</div>
    </div>

    <div class="container">
        <h2>Instructions</h2>
        <div id="instructions"></div>
    </div>

    <script>
        const status = document.getElementById('status');
        const instructions = document.getElementById('instructions');

        function checkVapidConfig() {
            // Check if we can access environment variables (this won't work in browser, but shows the concept)
            const currentVapidKey = "SUA_CHAVE_VAPID_AQUI"; // This would come from your .env
            
            if (currentVapidKey === "SUA_CHAVE_VAPID_AQUI" || currentVapidKey.includes('XXX')) {
                status.innerHTML = `
                    <div class="status error">
                        ❌ VAPID Key Not Configured
                    </div>
                    <p>Current value: <code>${currentVapidKey}</code></p>
                `;
                
                instructions.innerHTML = `
                    <div class="status warning">
                        🔧 Configuration Needed
                    </div>
                    <h3>Steps to Configure VAPID Key:</h3>
                    <ol>
                        <li><strong>Go to Firebase Console:</strong><br>
                            <a href="https://console.firebase.google.com/project/iconpal-cf925/settings/cloudmessaging" target="_blank" style="color: #60a5fa;">
                                https://console.firebase.google.com/project/iconpal-cf925/settings/cloudmessaging
                            </a>
                        </li>
                        <li><strong>Find "Web Push certificates" section</strong></li>
                        <li><strong>Generate key pair</strong> (if none exists)</li>
                        <li><strong>Copy the key</strong> (starts with 'B', ~88 characters)</li>
                        <li><strong>Update .env file:</strong><br>
                            <code>VITE_FIREBASE_VAPID_KEY=YOUR_ACTUAL_KEY_HERE</code>
                        </li>
                        <li><strong>Restart the development server:</strong><br>
                            <code>npm run dev</code>
                        </li>
                    </ol>
                `;
            } else if (currentVapidKey.startsWith('B') && currentVapidKey.length > 80) {
                status.innerHTML = `
                    <div class="status success">
                        ✅ VAPID Key Configured
                    </div>
                    <p>Key: <code>${currentVapidKey.substring(0, 20)}...</code></p>
                `;
                
                instructions.innerHTML = `
                    <div class="status success">
                        🎉 Configuration Complete!
                    </div>
                    <p>Your VAPID key is properly configured. You can now test push notifications.</p>
                    <p><a href="/test-push-notifications.html" style="color: #60a5fa;">Test Push Notifications →</a></p>
                `;
            } else {
                status.innerHTML = `
                    <div class="status warning">
                        ⚠️ Invalid VAPID Key Format
                    </div>
                    <p>Current value: <code>${currentVapidKey}</code></p>
                `;
                
                instructions.innerHTML = `
                    <div class="status error">
                        🔧 Invalid Key Format
                    </div>
                    <p>VAPID keys should:</p>
                    <ul>
                        <li>Start with the letter 'B'</li>
                        <li>Be approximately 88 characters long</li>
                        <li>Contain only alphanumeric characters and some symbols</li>
                    </ul>
                    <p>Please get a new key from Firebase Console.</p>
                `;
            }
        }

        // Run check on page load
        checkVapidConfig();
    </script>
</body>
</html>
