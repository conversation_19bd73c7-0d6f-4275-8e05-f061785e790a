<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Push Notifications</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }

        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #2563eb;
        }

        button:disabled {
            background: #6b7280;
            cursor: not-allowed;
        }

        .log {
            background: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }

        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .success {
            background: #065f46;
        }

        .error {
            background: #7f1d1d;
        }

        .warning {
            background: #78350f;
        }
    </style>
</head>

<body>
    <h1>🔔 Push Notifications Test</h1>

    <div class="container">
        <h2>Current Status</h2>
        <div id="status" class="status">Checking...</div>
    </div>

    <div class="container">
        <h2>Actions</h2>
        <button onclick="checkPermission()">Check Permission</button>
        <button onclick="requestPermission()">Request Permission</button>
        <button onclick="testFirebaseInit()">Test Firebase Init</button>
        <button onclick="getToken()">Get FCM Token</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="container">
        <h2>Debug Log</h2>
        <div id="log" class="log"></div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            log.textContent += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'warning') {
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
            authDomain: "iconpal-cf925.firebaseapp.com",
            projectId: "iconpal-cf925",
            storageBucket: "iconpal-cf925.appspot.com",
            messagingSenderId: "887109976546",
            appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
        };

        // VAPID key - Obtida do Firebase Console
        const vapidKey = "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";

        let app, messaging;

        function initFirebase() {
            try {
                addLog("🔥 Initializing Firebase...");
                app = firebase.initializeApp(firebaseConfig);
                messaging = firebase.messaging();
                addLog("✅ Firebase initialized successfully");
                return true;
            } catch (error) {
                addLog("❌ Firebase initialization failed: " + error.message);
                return false;
            }
        }

        function checkPermission() {
            addLog("🔍 Checking notification permission...");

            if (!('Notification' in window)) {
                addLog("❌ This browser does not support notifications");
                updateStatus("Browser does not support notifications", "error");
                return;
            }

            const permission = Notification.permission;
            addLog(`📋 Current permission: ${permission}`);

            switch (permission) {
                case 'granted':
                    updateStatus("Notifications are enabled", "success");
                    break;
                case 'denied':
                    updateStatus("Notifications are blocked", "error");
                    break;
                case 'default':
                    updateStatus("Notifications permission not requested", "warning");
                    break;
            }
        }

        async function requestPermission() {
            addLog("🔔 Requesting notification permission...");

            try {
                const permission = await Notification.requestPermission();
                addLog(`📋 Permission result: ${permission}`);

                if (permission === 'granted') {
                    updateStatus("Permission granted!", "success");
                } else {
                    updateStatus("Permission denied", "error");
                }
            } catch (error) {
                addLog("❌ Error requesting permission: " + error.message);
                updateStatus("Error requesting permission", "error");
            }
        }

        function testFirebaseInit() {
            addLog("🧪 Testing Firebase initialization...");
            const success = initFirebase();

            if (success) {
                updateStatus("Firebase initialized successfully", "success");
            } else {
                updateStatus("Firebase initialization failed", "error");
            }
        }

        async function getToken() {
            addLog("🎫 Getting FCM token...");

            if (!messaging) {
                addLog("❌ Firebase Messaging not initialized");
                updateStatus("Firebase not initialized", "error");
                return;
            }

            try {
                const tokenOptions = {};
                if (vapidKey && !vapidKey.includes('XXX')) {
                    tokenOptions.vapidKey = vapidKey;
                    addLog("🔑 Using VAPID key");
                } else {
                    addLog("⚠️ No valid VAPID key - using default");
                }

                const token = await messaging.getToken(tokenOptions);

                if (token) {
                    addLog("✅ FCM Token received: " + token.substring(0, 20) + "...");
                    updateStatus("FCM token generated successfully", "success");
                } else {
                    addLog("❌ No FCM token available");
                    updateStatus("Failed to get FCM token", "error");
                }
            } catch (error) {
                addLog("❌ Error getting FCM token: " + error.message);
                updateStatus("Error getting FCM token", "error");
            }
        }

        function clearLog() {
            log.textContent = '';
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            addLog("🚀 Page loaded, starting tests...");
            checkPermission();
            testFirebaseInit();
        });
    </script>
</body>

</html>