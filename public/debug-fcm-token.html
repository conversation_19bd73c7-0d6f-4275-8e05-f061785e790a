<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug FCM Token Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        .log {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .error { color: #ff6b6b; }
        .success { color: #51cf66; }
        .warning { color: #ffd43b; }
        .info { color: #74c0fc; }
    </style>
</head>
<body>
    <h1>🔍 Debug FCM Token Generation</h1>
    
    <div class="container">
        <h2>Step-by-Step Debugging</h2>
        <button onclick="step1()">1. Check Browser Support</button>
        <button onclick="step2()">2. Request Permission</button>
        <button onclick="step3()">3. Initialize Firebase</button>
        <button onclick="step4()">4. Get FCM Token (Debug)</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="container">
        <h2>Debug Log</h2>
        <div id="log" class="log">Ready for debugging...\n</div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>

    <script>
        const log = document.getElementById('log');
        let app, messaging;

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            log.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            log.scrollTop = log.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            log.innerHTML = 'Log cleared...\n';
        }

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
            authDomain: "iconpal-cf925.firebaseapp.com",
            projectId: "iconpal-cf925",
            storageBucket: "iconpal-cf925.appspot.com",
            messagingSenderId: "887109976546",
            appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
        };

        // VAPID key
        const vapidKey = "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";

        function step1() {
            addLog("🔍 Step 1: Checking browser support...");
            
            // Check if browser supports notifications
            if (!('Notification' in window)) {
                addLog("❌ This browser does not support notifications", 'error');
                return;
            }
            addLog("✅ Browser supports notifications", 'success');

            // Check if browser supports service workers
            if (!('serviceWorker' in navigator)) {
                addLog("❌ This browser does not support service workers", 'error');
                return;
            }
            addLog("✅ Browser supports service workers", 'success');

            // Check if Firebase is loaded
            if (typeof firebase === 'undefined') {
                addLog("❌ Firebase SDK not loaded", 'error');
                return;
            }
            addLog("✅ Firebase SDK loaded", 'success');

            // Check current permission status
            addLog(`📋 Current notification permission: ${Notification.permission}`, 'info');
            
            addLog("🎉 Step 1 complete - Browser is compatible!", 'success');
        }

        function step2() {
            addLog("🔔 Step 2: Requesting notification permission...");
            
            if (Notification.permission === 'granted') {
                addLog("✅ Permission already granted", 'success');
                return;
            }

            if (Notification.permission === 'denied') {
                addLog("❌ Permission denied - please enable in browser settings", 'error');
                return;
            }

            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    addLog("✅ Permission granted!", 'success');
                } else {
                    addLog("❌ Permission denied", 'error');
                }
            }).catch(error => {
                addLog(`❌ Error requesting permission: ${error.message}`, 'error');
            });
        }

        function step3() {
            addLog("🔥 Step 3: Initializing Firebase...");
            
            try {
                // Initialize Firebase
                if (!app) {
                    app = firebase.initializeApp(firebaseConfig);
                    addLog("✅ Firebase app initialized", 'success');
                } else {
                    addLog("✅ Firebase app already initialized", 'success');
                }

                // Initialize messaging
                if (!messaging) {
                    messaging = firebase.messaging();
                    addLog("✅ Firebase Messaging initialized", 'success');
                } else {
                    addLog("✅ Firebase Messaging already initialized", 'success');
                }

                // Check VAPID key
                if (vapidKey && vapidKey.length > 80) {
                    addLog(`✅ VAPID key configured (${vapidKey.length} chars)`, 'success');
                } else {
                    addLog("⚠️ VAPID key missing or invalid", 'warning');
                }

                addLog("🎉 Step 3 complete - Firebase ready!", 'success');
                
            } catch (error) {
                addLog(`❌ Firebase initialization error: ${error.message}`, 'error');
                console.error("Firebase init error:", error);
            }
        }

        async function step4() {
            addLog("🎫 Step 4: Getting FCM token with detailed debugging...");
            
            if (!messaging) {
                addLog("❌ Firebase Messaging not initialized - run Step 3 first", 'error');
                return;
            }

            if (Notification.permission !== 'granted') {
                addLog("❌ Notification permission not granted - run Step 2 first", 'error');
                return;
            }

            try {
                addLog("🔧 Preparing token request...");
                
                const tokenOptions = {};
                if (vapidKey) {
                    tokenOptions.vapidKey = vapidKey;
                    addLog("🔑 Using VAPID key for token request", 'info');
                } else {
                    addLog("⚠️ No VAPID key - using default registration", 'warning');
                }

                addLog("📡 Requesting token from Firebase...");
                
                // Add timeout to prevent hanging
                const tokenPromise = messaging.getToken(tokenOptions);
                const timeoutPromise = new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Token request timeout after 15 seconds')), 15000)
                );

                const token = await Promise.race([tokenPromise, timeoutPromise]);

                if (token) {
                    addLog(`✅ FCM Token received successfully!`, 'success');
                    addLog(`📋 Token: ${token.substring(0, 50)}...`, 'info');
                    addLog(`📏 Token length: ${token.length} characters`, 'info');
                    
                    // Copy to clipboard if possible
                    if (navigator.clipboard) {
                        try {
                            await navigator.clipboard.writeText(token);
                            addLog("📋 Token copied to clipboard!", 'success');
                        } catch (e) {
                            addLog("⚠️ Could not copy to clipboard", 'warning');
                        }
                    }
                    
                } else {
                    addLog("❌ No token received from Firebase", 'error');
                }
                
            } catch (error) {
                addLog(`❌ Error getting FCM token: ${error.message}`, 'error');
                console.error("FCM Token Error Details:", error);
                
                // Additional debugging
                addLog("🔍 Debug information:", 'info');
                addLog(`- Error type: ${error.constructor.name}`, 'info');
                addLog(`- Error code: ${error.code || 'N/A'}`, 'info');
                addLog(`- Firebase app: ${app ? 'OK' : 'Missing'}`, 'info');
                addLog(`- Messaging service: ${messaging ? 'OK' : 'Missing'}`, 'info');
                addLog(`- VAPID key: ${vapidKey ? 'Present' : 'Missing'}`, 'info');
                addLog(`- Permission: ${Notification.permission}`, 'info');
            }
        }

        // Auto-run step 1 on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addLog("🚀 Debug page loaded - click buttons to run tests");
                step1();
            }, 500);
        });
    </script>
</body>
</html>
