<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test FCM with Valid API Key</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 18px;
        }
        button:hover { background: #45a049; }
        .result {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #51cf66; }
        .error { color: #ff6b6b; }
        .warning { color: #ffd43b; }
        .info { color: #74c0fc; }
    </style>
</head>
<body>
    <h1>🚀 Test FCM with Valid API Key</h1>
    
    <div class="result">
        <span class="success">✅ API Key confirmed as VALID</span>
        <span class="info">Now let's test FCM token generation...</span>
    </div>
    
    <button onclick="testFCMToken()">🎫 Generate FCM Token</button>
    <button onclick="testWithoutVapid()">🔓 Try Without VAPID</button>
    
    <div id="result" class="result">Ready to test FCM with your valid API key...</div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>

    <script>
        const result = document.getElementById('result');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            result.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }

        async function testFCMToken() {
            result.innerHTML = 'Testing FCM token generation with valid API key...\n';
            
            try {
                // Check permission
                log('🔔 Checking notification permission...', 'info');
                if (Notification.permission !== 'granted') {
                    log('📋 Requesting permission...', 'info');
                    const permission = await Notification.requestPermission();
                    if (permission !== 'granted') {
                        log('❌ Permission denied', 'error');
                        return;
                    }
                }
                log('✅ Permission granted', 'success');

                // Your Firebase configuration (we know the API key is valid)
                const firebaseConfig = {
                    apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
                    authDomain: "iconpal-cf925.firebaseapp.com",
                    projectId: "iconpal-cf925",
                    storageBucket: "iconpal-cf925.appspot.com",
                    messagingSenderId: "887109976546",
                    appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
                };

                const vapidKey = "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";

                log('🔥 Initializing Firebase with valid API key...', 'info');
                
                // Initialize Firebase
                const app = firebase.initializeApp(firebaseConfig, 'valid-test-' + Date.now());
                log('✅ Firebase app initialized', 'success');

                // Check messaging support
                if (!firebase.messaging.isSupported()) {
                    log('❌ Firebase Messaging not supported', 'error');
                    return;
                }

                const messaging = firebase.messaging(app);
                log('✅ Firebase Messaging initialized', 'success');

                // Get token with VAPID
                log('🎫 Generating FCM token with VAPID key...', 'info');
                log('🔑 Using your VAPID key...', 'info');
                
                const token = await messaging.getToken({ vapidKey: vapidKey });

                if (token) {
                    log('🎉 SUCCESS! FCM Token generated!', 'success');
                    log(`📋 Token: ${token.substring(0, 50)}...`, 'success');
                    log(`📏 Length: ${token.length} characters`, 'success');
                    log('', 'info');
                    log('✅ Everything is working perfectly!', 'success');
                    log('✅ API Key: Valid', 'success');
                    log('✅ VAPID Key: Valid', 'success');
                    log('✅ Firebase Project: Accessible', 'success');
                    log('✅ Network: No blocks', 'success');
                    log('', 'info');
                    log('🎯 Your push notification system is ready!', 'info');
                    
                    // Copy to clipboard
                    try {
                        await navigator.clipboard.writeText(token);
                        log('📋 Token copied to clipboard!', 'success');
                    } catch (e) {
                        log('⚠️ Could not copy to clipboard', 'warning');
                    }
                    
                } else {
                    log('❌ No token received', 'error');
                    log('🔍 This might indicate a project configuration issue', 'warning');
                }

            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                console.error('Full error:', error);
                
                // Detailed error analysis
                if (error.message.includes('API key not valid')) {
                    log('🔍 API key issue (unexpected!)', 'error');
                } else if (error.message.includes('project not found')) {
                    log('🔍 Project iconpal-cf925 not found', 'error');
                } else if (error.message.includes('messaging/registration-token-not-registered')) {
                    log('🔍 Registration issue - try without VAPID', 'warning');
                } else if (error.message.includes('installations')) {
                    log('🔍 Installation service issue', 'warning');
                    log('   This might be a project configuration problem', 'warning');
                } else {
                    log(`🔍 Unexpected error: ${error.constructor.name}`, 'warning');
                    log(`   Code: ${error.code || 'N/A'}`, 'warning');
                }
            }
        }

        async function testWithoutVapid() {
            result.innerHTML = 'Testing FCM without VAPID key...\n';
            
            try {
                log('🔔 Checking permission...', 'info');
                if (Notification.permission !== 'granted') {
                    const permission = await Notification.requestPermission();
                    if (permission !== 'granted') {
                        log('❌ Permission denied', 'error');
                        return;
                    }
                }
                log('✅ Permission granted', 'success');

                const firebaseConfig = {
                    apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
                    authDomain: "iconpal-cf925.firebaseapp.com",
                    projectId: "iconpal-cf925",
                    storageBucket: "iconpal-cf925.appspot.com",
                    messagingSenderId: "887109976546",
                    appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
                };

                log('🔥 Initializing Firebase...', 'info');
                const app = firebase.initializeApp(firebaseConfig, 'no-vapid-test-' + Date.now());
                const messaging = firebase.messaging(app);
                log('✅ Firebase initialized', 'success');

                log('🎫 Generating token WITHOUT VAPID key...', 'info');
                const token = await messaging.getToken();

                if (token) {
                    log('🎉 SUCCESS! Token generated without VAPID!', 'success');
                    log(`📋 Token: ${token.substring(0, 50)}...`, 'success');
                    log('', 'info');
                    log('✅ Your Firebase project is working!', 'success');
                    log('⚠️ VAPID key might have an issue', 'warning');
                } else {
                    log('❌ No token received even without VAPID', 'error');
                }

            } catch (error) {
                log(`❌ Error without VAPID: ${error.message}`, 'error');
                console.error('Error without VAPID:', error);
            }
        }
    </script>
</body>
</html>
