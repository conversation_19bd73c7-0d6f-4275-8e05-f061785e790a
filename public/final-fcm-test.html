<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final FCM Test - Your Project</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 18px;
        }
        button:hover { background: #45a049; }
        .result {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #51cf66; }
        .error { color: #ff6b6b; }
        .warning { color: #ffd43b; }
        .info { color: #74c0fc; }
    </style>
</head>
<body>
    <h1>🎯 Final FCM Test - Your Project</h1>
    
    <p>Now that we've resolved the permission and network issues, let's test with your actual Firebase project!</p>
    
    <button onclick="testYourProject()">🚀 Test Your Firebase Project</button>
    <button onclick="sendTestNotification()">📱 Send Test Notification</button>
    
    <div id="result" class="result">Ready to test your project...</div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>

    <script>
        const result = document.getElementById('result');
        let currentToken = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            result.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }

        async function testYourProject() {
            result.innerHTML = 'Testing your Firebase project...\n';
            
            try {
                // Check permission
                log('🔔 Checking notification permission...', 'info');
                if (Notification.permission !== 'granted') {
                    log('📋 Requesting permission...', 'info');
                    const permission = await Notification.requestPermission();
                    if (permission !== 'granted') {
                        log('❌ Permission denied', 'error');
                        return;
                    }
                }
                log('✅ Permission granted', 'success');

                // Your actual Firebase configuration
                const firebaseConfig = {
                    apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
                    authDomain: "iconpal-cf925.firebaseapp.com",
                    projectId: "iconpal-cf925",
                    storageBucket: "iconpal-cf925.appspot.com",
                    messagingSenderId: "887109976546",
                    appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
                };

                // Your VAPID key
                const vapidKey = "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";

                log('🔥 Initializing your Firebase project...', 'info');
                log('📋 Project ID: iconpal-cf925', 'info');

                // Initialize Firebase
                const app = firebase.initializeApp(firebaseConfig, 'your-project-' + Date.now());
                log('✅ Firebase app initialized', 'success');

                // Check messaging support
                if (!firebase.messaging.isSupported()) {
                    log('❌ Firebase Messaging not supported', 'error');
                    return;
                }

                const messaging = firebase.messaging(app);
                log('✅ Firebase Messaging initialized', 'success');

                // Get token with VAPID
                log('🎫 Getting FCM token with your VAPID key...', 'info');
                log('🔑 Using VAPID key...', 'info');
                
                const tokenPromise = messaging.getToken({ vapidKey: vapidKey });
                const timeoutPromise = new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Timeout after 10 seconds')), 10000)
                );

                const token = await Promise.race([tokenPromise, timeoutPromise]);

                if (token) {
                    currentToken = token;
                    log('🎉 SUCCESS! Your FCM token generated!', 'success');
                    log(`📋 Token: ${token.substring(0, 50)}...`, 'success');
                    log(`📏 Length: ${token.length} characters`, 'success');
                    log('', 'info');
                    log('✅ Your Firebase project is working correctly!', 'success');
                    log('✅ VAPID key is valid!', 'success');
                    log('✅ Network connectivity is good!', 'success');
                    log('', 'info');
                    log('🎯 You can now use this token for push notifications!', 'info');
                    
                    // Copy to clipboard
                    try {
                        await navigator.clipboard.writeText(token);
                        log('📋 Token copied to clipboard!', 'success');
                    } catch (e) {
                        log('⚠️ Could not copy to clipboard', 'warning');
                    }

                    // Enable test notification button
                    document.querySelector('button[onclick="sendTestNotification()"]').style.display = 'inline-block';
                    
                } else {
                    log('❌ No token received', 'error');
                }

            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                console.error('Full error:', error);
                
                if (error.message.includes('API key not valid')) {
                    log('', 'info');
                    log('🔍 API Key issue detected:', 'warning');
                    log('- Check if your Firebase project exists', 'warning');
                    log('- Verify the API key in Firebase Console', 'warning');
                    log('- Ensure Cloud Messaging is enabled', 'warning');
                } else if (error.message.includes('Timeout')) {
                    log('', 'info');
                    log('🔍 Still having timeout issues:', 'warning');
                    log('- Try a different network', 'warning');
                    log('- Check firewall settings', 'warning');
                } else {
                    log('', 'info');
                    log('🔍 Other error details:', 'warning');
                    log(`- Error type: ${error.constructor.name}`, 'warning');
                    log(`- Error code: ${error.code || 'N/A'}`, 'warning');
                }
            }
        }

        async function sendTestNotification() {
            if (!currentToken) {
                log('❌ No FCM token available. Run the test first!', 'error');
                return;
            }

            log('📱 Sending test notification...', 'info');
            
            // Send a local test notification
            try {
                const notification = new Notification('🎉 PinPal Test Notification', {
                    body: 'Your Firebase Cloud Messaging is working perfectly!',
                    icon: '/pinpal-logo-icon.png',
                    badge: '/pinpal-logo-icon.png',
                    tag: 'pinpal-test',
                    requireInteraction: true,
                    data: {
                        url: 'http://localhost:5773',
                        action: 'test'
                    }
                });

                notification.onclick = function() {
                    log('✅ Test notification clicked!', 'success');
                    window.focus();
                    notification.close();
                };

                log('✅ Test notification sent successfully!', 'success');
                log('📋 Check your screen for the notification', 'info');
                
                // Auto-close after 5 seconds
                setTimeout(() => {
                    notification.close();
                }, 5000);

            } catch (error) {
                log(`❌ Error sending notification: ${error.message}`, 'error');
            }
        }

        // Hide test notification button initially
        document.addEventListener('DOMContentLoaded', () => {
            document.querySelector('button[onclick="sendTestNotification()"]').style.display = 'none';
        });
    </script>
</body>
</html>
